
import { useState, useCallback, useRef } from 'react';
import { PlayerStatus, CoreAttributes, Skill, InventoryItem, VisitedLocation, Quest, CharacterProfile, NotificationType, DialogueLine, ImportantEvent, DetailModalType, RPGNotification, StatusEffect, RPGEffectAddParams } from '../types';
import { UIText, InitialPlayerStatus, LEVEL_UP_BASE_XP, LEVEL_UP_XP_FACTOR, LEVEL_UP_ATTRIBUTE_POINTS_AWARDED, LEVEL_UP_SKILL_POINTS_AWARDED, defaultCoreAttributes, defaultSkills, AVAILABLE_ACHIEVEMENTS } from '../constants';
import { summarizeInventory, summarizeLocations, summarizeQuests, summarizeCharacters, summarizeImportantEvents } from '../services/geminiService'; 

const getAttributeDisplayName = (attrKey: keyof CoreAttributes): string => {
    switch (attrKey) {
        case 'strength': return UIText.strengthShort;
        case 'agility': return UIText.agilityShort;
        case 'intelligence': return UIText.intelligenceShort;
        case 'charisma': return UIText.charismaShort;
        case 'luck': return UIText.luckShort;
        case 'sanity': return UIText.sanityShort;
        default:
            const _exhaustiveCheck: never = attrKey;
            return String(_exhaustiveCheck).toUpperCase();
    }
};

const TIME_OF_DAY_ORDER: string[] = ["凌晨", "早晨", "上午", "中午", "下午", "傍晚", "深夜"];

const calculateXpAndLevelChanges = (
    currentStatus: PlayerStatus,
    amount: number
): { statusChanges: Partial<PlayerStatus>, notifications: RPGNotification[], achievementsToUnlock: string[] } => {
    if (amount <= 0) return { statusChanges: {}, notifications: [], achievementsToUnlock: [] };

    let newXp = (currentStatus.xp || 0) + amount;
    let newLevel = currentStatus.level || 1;
    let newXpToNextLevel = currentStatus.xpToNextLevel || LEVEL_UP_BASE_XP;
    let newAttributePoints = currentStatus.attributePoints || 0;
    let newSkillPoints = currentStatus.skillPoints || 0;
    const notifications: RPGNotification[] = [];
    const achievementsToUnlockInternal: string[] = []; 

    notifications.push({ message: UIText.xpGained(amount), type: 'info', duration: 3000, displayToUser: true });

    while (newXp >= newXpToNextLevel) {
        newLevel++;
        newXp -= newXpToNextLevel;
        newXpToNextLevel = Math.floor(LEVEL_UP_BASE_XP * Math.pow(LEVEL_UP_XP_FACTOR, newLevel - 1));

        const awardedAttrPoints = LEVEL_UP_ATTRIBUTE_POINTS_AWARDED;
        const awardedSkillPoints = LEVEL_UP_SKILL_POINTS_AWARDED;

        newAttributePoints += awardedAttrPoints;
        newSkillPoints += awardedSkillPoints;

        notifications.push({ message: UIText.levelUpNotification(newLevel), type: 'achievement', duration: 6000, title: UIText.achievementUnlockedTitle, displayToUser: true });
        if (awardedAttrPoints > 0) {
            notifications.push({ message: `等级提升奖励: 获得 ${awardedAttrPoints} 属性点！`, type: 'success', duration: 4000, displayToUser: true });
        }
        if (awardedSkillPoints > 0) {
            notifications.push({ message: `等级提升奖励: 获得 ${awardedSkillPoints} 技能点！`, type: 'success', duration: 4000, displayToUser: true });
        }
        achievementsToUnlockInternal.push('level_up_first');
        if (newLevel >= 5) achievementsToUnlockInternal.push('level_five');
        if (newLevel >= 10) achievementsToUnlockInternal.push('level_ten_hero');
    }

    return {
        statusChanges: {
            xp: newXp,
            level: newLevel,
            xpToNextLevel: newXpToNextLevel,
            attributePoints: newAttributePoints,
            skillPoints: newSkillPoints,
        },
        notifications,
        achievementsToUnlock: achievementsToUnlockInternal
    };
};

interface RPGTagProcessor {
    regex: RegExp;
    handler: (match: RegExpExecArray, currentProcessingStatus: PlayerStatus) => { changes: Partial<PlayerStatus>, notifications?: RPGNotification[], achievement?: string | string[] };
}

const rpgTagProcessors: RPGTagProcessor[] = [
    {
        regex: /\[RPG:attribute_change:(?<attrKey>\w+),(?<changeValue>[+-]\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentProcessingStatus) => {
            const attrKey = match.groups?.attrKey?.toLowerCase() as keyof CoreAttributes | undefined;
            const changeValue = parseInt(match.groups?.changeValue || '0', 10);
            const reason = match.groups?.reason || "未知原因";

            if (!attrKey || !Object.keys(defaultCoreAttributes).includes(attrKey) || isNaN(changeValue)) {
                console.warn("Malformed RPG:attribute_change tag:", match[0]);
                return { changes: {} };
            }
            const currentAttributes = { ...(currentProcessingStatus.coreAttributes || defaultCoreAttributes) };
            const oldValue = currentAttributes[attrKey] || 0;
            const newValue = Math.max(0, oldValue + changeValue);

            let achievementToUnlock: string | undefined;
            if (newValue > oldValue) {
                if (attrKey === 'strength' && newValue >= 10 && oldValue < 10) achievementToUnlock = 'strength_manifested';
                else if (attrKey === 'agility' && newValue >= 10 && oldValue < 10) achievementToUnlock = 'agile_as_wind';
                else if (attrKey === 'intelligence' && newValue >= 10 && oldValue < 10) achievementToUnlock = 'light_of_intellect';
                else if (attrKey === 'charisma' && newValue >= 10 && oldValue < 10) achievementToUnlock = 'charm_of_words';
            }

            return {
                changes: { coreAttributes: { ...currentAttributes, [attrKey]: newValue } },
                notifications: [{ message: `${getAttributeDisplayName(attrKey)} ${changeValue > 0 ? '提升' : '降低'}至 ${newValue} (原因: ${reason})`, type: changeValue > 0 ? 'success' : 'warning', displayToUser: true }],
                achievement: achievementToUnlock
            };
        },
    },
    { 
        regex: /\[RPG:skill_update:(?<skillId>[\w_]+),level:(?<newLevel>\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentProcessingStatus) => {
            const skillId = match.groups?.skillId;
            const newLevelFromTag = parseInt(match.groups?.newLevel || '0', 10);
            const reason = match.groups?.reason || "技能提升";

            if (!skillId || isNaN(newLevelFromTag)) {
                console.warn("Malformed RPG:skill_update tag:", match[0]);
                return { changes: {} };
            }

            const currentSkills = [...(currentProcessingStatus.skills || []).map(s => ({ ...s }))];
            const skillIndex = currentSkills.findIndex(s => s.id === skillId);
            const skillDefinition = defaultSkills.find(s => s.id === skillId);
            let notification: RPGNotification | undefined;
            let achievementToUnlock: string | undefined;

            if (skillIndex > -1) { 
                if (newLevelFromTag > currentSkills[skillIndex].level) {
                    currentSkills[skillIndex].level = newLevelFromTag;
                    currentSkills[skillIndex].currentXp = 0; 
                    notification = { message: UIText.skillLeveledUp(currentSkills[skillIndex].name, newLevelFromTag) + ` (${reason})`, type: 'success', displayToUser: true };
                    achievementToUnlock = 'skill_master_initiate';
                     if (currentSkills.filter(s => s.level >= 5).length >= 3) achievementToUnlock = 'three_arts_mastery';
                }
            } else if (skillDefinition && newLevelFromTag > 0) { 
                currentSkills.push({ ...skillDefinition, level: newLevelFromTag, currentXp: 0 });
                notification = { message: UIText.skillLearned(skillDefinition.name) + ` (初始等级 ${newLevelFromTag}, ${reason})`, type: 'success', displayToUser: true };
                achievementToUnlock = 'skill_master_initiate';
            } else {
                console.warn("RPG:skill_update tag for unknown or invalid skill:", match[0]);
                return { changes: {} };
            }
            return { changes: { skills: currentSkills }, notifications: notification ? [notification] : [], achievement: achievementToUnlock };
        },
    },
     { 
        regex: /\[RPG:skill_xp_gain:(?<skillId>[\w_]+),xp:(?<xpAmount>\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentProcessingStatus) => {
            const skillId = match.groups?.skillId;
            const xpAmount = parseInt(match.groups?.xpAmount || '0', 10);
            const reason = match.groups?.reason || "技能运用";
            const notificationsInternal: RPGNotification[] = [];

            if (!skillId || isNaN(xpAmount) || xpAmount <= 0) {
                console.warn("Malformed RPG:skill_xp_gain tag:", match[0]);
                return { changes: {} };
            }

            let currentSkills = [...(currentProcessingStatus.skills || []).map(s => ({ ...s }))];
            const skillIndex = currentSkills.findIndex(s => s.id === skillId);
            const skillDefinition = defaultSkills.find(s => s.id === skillId);
            let achievementToUnlock: string | undefined;

            if (skillIndex > -1) { 
                let skill = currentSkills[skillIndex];
                skill.currentXp = (skill.currentXp || 0) + xpAmount;
                notificationsInternal.push({ message: `${skill.name} 获得 ${xpAmount} 技能经验 (${reason})。`, type: 'info', displayToUser: true });
                while (skill.currentXp >= (skill.xpToNextLevel || 50)) {
                    skill.level++;
                    skill.currentXp -= (skill.xpToNextLevel || 50);
                    skill.xpToNextLevel = Math.floor((skill.xpToNextLevel || 50) * 1.3); 
                    notificationsInternal.push({ message: UIText.skillLeveledUp(skill.name, skill.level), type: 'success', title: "技能提升！", displayToUser: true });
                    achievementToUnlock = 'skill_master_initiate';
                    if (currentSkills.filter(s => s.level >= 5).length >= 3) achievementToUnlock = 'three_arts_mastery';
                }
                currentSkills[skillIndex] = skill;
            } else if (skillDefinition) { 
                 let newSkill: Skill = { ...skillDefinition, level: 0, currentXp: xpAmount, xpToNextLevel: skillDefinition.xpToNextLevel || 50 };
                 notificationsInternal.push({ message: `${newSkill.name} 获得 ${xpAmount} 技能经验 (${reason})。`, type: 'info', displayToUser: true });
                 while (newSkill.currentXp >= (newSkill.xpToNextLevel || 50) && newSkill.level < 1) { 
                    newSkill.level++;
                    newSkill.currentXp -= (newSkill.xpToNextLevel || 50);
                    newSkill.xpToNextLevel = Math.floor((newSkill.xpToNextLevel || 50) * 1.3);
                    notificationsInternal.push({ message: UIText.skillLearned(newSkill.name) + ` (初始等级 ${newSkill.level})`, type: 'success', title: "习得技能！", displayToUser: true });
                    achievementToUnlock = 'skill_master_initiate';
                 }
                 if (newSkill.level > 0) currentSkills.push(newSkill); 
            } else {
                console.warn("RPG:skill_xp_gain tag for unknown skill:", match[0]);
                return { changes: {} };
            }
            return { changes: { skills: currentSkills }, notifications: notificationsInternal, achievement: achievementToUnlock };
        },
    },
    {
        regex: /\[RPG:attribute_points_award:(?<amount>\d+),reason:(?<reason>[^\]]+)\]/gi,
        handler: (match, currentProcessingStatus) => {
            const amount = parseInt(match.groups?.amount || '0', 10);
            const reason = match.groups?.reason || "剧情奖励";
            if (isNaN(amount) || amount <= 0) {
                console.warn("Malformed RPG:attribute_points_award tag (invalid amount):", match[0]);
                return { changes: {} };
            }
            return {
                changes: { attributePoints: (currentProcessingStatus.attributePoints || 0) + amount },
                notifications: [{ message: `获得 ${amount} 属性点! (原因: ${reason})`, type: 'success', displayToUser: true }]
            };
        },
    },
    {
        regex: /\[RPG:skill_points_award:(?<amount>\d+),reason:(?<reason>[^\]]+)\]/gi,
        handler: (match, currentProcessingStatus) => {
            const amount = parseInt(match.groups?.amount || '0', 10);
            const reason = match.groups?.reason || "剧情奖励";
            if (isNaN(amount) || amount <= 0) {
                console.warn("Malformed RPG:skill_points_award tag (invalid amount):", match[0]);
                return { changes: {} };
            }
            return {
                changes: { skillPoints: (currentProcessingStatus.skillPoints || 0) + amount },
                notifications: [{ message: `获得 ${amount} 技能点! (原因: ${reason})`, type: 'success', displayToUser: true }]
            };
        },
    },
    {
        regex: /\[status_update:key=(?<key>\w+),value:(?<value>[^\]]+)\]/gi,
        handler: (match, currentProcessingStatus) => { 
            const key = match.groups?.key;
            const value = match.groups?.value;
            if (!key || value === undefined) {
                 console.warn("Malformed status_update tag:", match[0]);
                 return { changes: {} };
            }
            const notificationsInternal: RPGNotification[] = [];
            let changesInternal: Partial<PlayerStatus> = {};

            if (key === 'currentDay') {
              const newDay = parseInt(value, 10) || currentProcessingStatus.currentDay;
              changesInternal.currentDay = newDay;
              notificationsInternal.push({ message: UIText.dayAdvancedTo(newDay), type: 'info', displayToUser: true });
            } else if (key === 'mood') {
              changesInternal.mood = value;
              notificationsInternal.push({ message: `心情变为: ${value}`, type: 'info', displayToUser: true });
            } else if (key === 'weather') {
              changesInternal.weather = value;
              notificationsInternal.push({ message: `天气变为: ${value}`, type: 'info', displayToUser: true });
            } else if (key === 'timeOfDay') {
              changesInternal.timeOfDay = value;
              notificationsInternal.push({ message: `时刻变为: ${value}`, type: 'info', displayToUser: true });
            } else {
                 console.warn("Unhandled key in status_update tag:", key);
                 return { changes: {} };
            }
            return { changes: changesInternal, notifications: notificationsInternal };
        },
    },
    {
        regex: /\[RPG:health_energy_change:(?<change>[+-]\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentProcessingStatus) => {
            const change = parseInt(match.groups?.change || '0', 10);
            const reason = match.groups?.reason || "未知原因";
            if (isNaN(change)) {
                console.warn("Malformed RPG:health_energy_change tag:", match[0]);
                return { changes: {} };
            }
            const currentHealth = currentProcessingStatus.healthEnergy?.current ?? defaultCoreAttributes.strength * 10;
            const maxHealth = currentProcessingStatus.healthEnergy?.max ?? defaultCoreAttributes.strength * 10;
            const newHealth = Math.max(0, Math.min(currentHealth + change, maxHealth));
            const changeText = change > 0 ? `恢复 ${change}` : `受到 ${-change} 点伤害`;
            return {
                changes: { healthEnergy: { current: newHealth, max: maxHealth } },
                notifications: [{ message: `元气值变化: ${changeText} (原因: ${reason})。当前: ${newHealth}/${maxHealth}`, type: change > 0 ? 'success' : 'warning', displayToUser: true }]
            };
        },
    },
     {
        regex: /\[RPG:health_energy_set:current=(?<current>\d+),max=(?<max>\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentProcessingStatus) => {
            const current = parseInt(match.groups?.current || '100', 10);
            const max = parseInt(match.groups?.max || '100', 10);
            const reason = match.groups?.reason || "状态设定";
            if (isNaN(current) || isNaN(max) || max <=0) {
                console.warn("Malformed RPG:health_energy_set tag:", match[0]);
                return { changes: {} };
            }
            const newMax = max;
            const newCurrent = Math.min(current, newMax);
            return {
                changes: { healthEnergy: { current: newCurrent, max: newMax } },
                notifications: [{ message: `元气值设定为: ${newCurrent}/${newMax} (原因: ${reason})`, type: 'info', displayToUser: true }]
            };
        },
    },
    { 
        regex: /\[RPG:effect_add:type=(?<type>buff|debuff),name=(?<name>[^,\]]+),duration=(?<duration>-?\d+)(?:,description:(?<description>[^,\]]+))?(?:,icon:(?<icon>[^,\]]+))?(?:,source:(?<source>[^,\]]+))?\]/gi,
        handler: (match, currentProcessingStatus): { changes: Partial<PlayerStatus>, notifications?: RPGNotification[] } => {
            const { type, name, duration, description, icon, source } = match.groups as unknown as RPGEffectAddParams & { type: 'buff' | 'debuff' }; // Assert type
            if (!type || !name || duration === undefined) {
                console.warn("Malformed RPG:effect_add tag:", match[0]);
                return { changes: {} };
            }
            const newEffect: StatusEffect = {
                id: `effect_${name.replace(/\s+/g, '_')}_${Date.now()}`,
                name: name.trim(),
                description: description?.trim(),
                icon: icon?.trim(),
                durationTurns: Number(duration),
                remainingTurns: Number(duration),
                timestampApplied: Date.now(),
                source: source?.trim(),
                isBuff: type.toLowerCase() === 'buff',
            };

            let changes: Partial<PlayerStatus> = {};
            if (newEffect.isBuff) {
                changes.buffs = [...(currentProcessingStatus.buffs || []), newEffect];
            } else {
                changes.debuffs = [...(currentProcessingStatus.debuffs || []), newEffect];
            }
            return {
                changes,
                notifications: [{ message: `${newEffect.isBuff ? '获得增益' : '受到减益'}: ${newEffect.name}`, type: newEffect.isBuff ? 'success' : 'warning', displayToUser: true }]
            };
        }
    },
    { 
        regex: /\[RPG:effect_remove:name=(?<name>[^\]]+)\]/gi,
        handler: (match, currentProcessingStatus): { changes: Partial<PlayerStatus>, notifications?: RPGNotification[] } => {
            const name = match.groups?.name?.trim();
            if (!name) {
                console.warn("Malformed RPG:effect_remove tag:", match[0]);
                return { changes: {} };
            }
            return {
                changes: {
                    buffs: (currentProcessingStatus.buffs || []).filter(eff => eff.name !== name),
                    debuffs: (currentProcessingStatus.debuffs || []).filter(eff => eff.name !== name),
                },
                notifications: [{ message: `效果移除: ${name}`, type: 'info', displayToUser: true }]
            };
        }
    },
    { 
        regex: /\[RPG:special_effect_add:(?<effectName>[^,\]]+)(?:,duration:(?<duration>[^,\]]+))?(?:,description:(?<description>[^\]]+))?\]/gi,
        handler: (match, currentProcessingStatus) => {
            const effectName = match.groups?.effectName?.trim();
            if (!effectName) return { changes: {} };
            const newEffect: StatusEffect = {
                id: `legacy_effect_${effectName.replace(/\s+/g, '_')}_${Date.now()}`,
                name: effectName,
                description: match.groups?.description?.trim() || effectName,
                durationTurns: -1, 
                remainingTurns: -1,
                timestampApplied: Date.now(),
                isBuff: true, 
            };
            return {
                changes: { buffs: [...(currentProcessingStatus.buffs || []), newEffect] },
                notifications: [{ message: `获得效果: ${newEffect.name}`, type: 'info', displayToUser: true }]
            };
        },
    },
    { 
        regex: /\[RPG:special_effect_remove:(?<effectName>[^\]]+)\]/gi,
        handler: (match, currentProcessingStatus) => {
            const effectName = match.groups?.effectName?.trim();
            if (!effectName) return { changes: {} };
            return {
                changes: {
                  buffs: (currentProcessingStatus.buffs || []).filter(eff => eff.name !== effectName),
                  debuffs: (currentProcessingStatus.debuffs || []).filter(eff => eff.name !== effectName),
                },
                notifications: [{ message: `效果移除: ${effectName}`, type: 'info', displayToUser: true }]
            };
        },
    },
    {
        regex: /\[RPG:quest_complete:(?<questId>[a-zA-Z0-9_]+),xp_reward:(?<xpReward>\d+),attribute_points_reward:(?<attrPointsReward>\d+),skill_points_reward:(?<skillPointsReward>\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentProcessingStatus) => {
            const questId = match.groups?.questId;
            const xpReward = parseInt(match.groups?.xpReward || '0', 10);
            const attrPointsReward = parseInt(match.groups?.attrPointsReward || '0', 10);
            const skillPointsReward = parseInt(match.groups?.skillPointsReward || '0', 10);
            const reason = match.groups?.reason || "任务完成";
    
            if (!questId) {
                console.warn("Malformed RPG:quest_complete tag (missing questId):", match[0]);
                return { changes: {} };
            }
    
            const currentQuests = [...(currentProcessingStatus.quests || [])];
            const questIndex = currentQuests.findIndex(q => q.id === questId && q.status === 'active');
    
            if (questIndex === -1) {
                console.warn(`RPG:quest_complete tag for unknown or already completed/failed quest: ${questId}`, match[0]);
                return { changes: {} };
            }
    
            const completedQuest = { ...currentQuests[questIndex], status: 'completed' as 'completed', lastUpdated: Date.now() };
            currentQuests[questIndex] = completedQuest;
    
            const notificationsInternal: RPGNotification[] = [];
            notificationsInternal.push({ message: `任务【${completedQuest.title}】已完成！(${reason})`, type: 'success', title: "任务完成", displayToUser: true });
    
            let newStatusChanges: Partial<PlayerStatus> = { quests: currentQuests };
            const achievementsFromRewards: string[] = [];
    
            // Apply XP reward and handle level ups
            if (xpReward > 0) {
              const xpUpdateResult = calculateXpAndLevelChanges(currentProcessingStatus, xpReward);
              newStatusChanges = { ...newStatusChanges, ...xpUpdateResult.statusChanges };
              notificationsInternal.push(...xpUpdateResult.notifications.map(n => ({...n, displayToUser: true})));
              achievementsFromRewards.push(...xpUpdateResult.achievementsToUnlock);
            }
    
            // Apply attribute points reward
            if (attrPointsReward > 0) {
                newStatusChanges.attributePoints = (currentProcessingStatus.attributePoints || 0) + attrPointsReward;
                notificationsInternal.push({ message: `任务奖励: 获得 ${attrPointsReward} 属性点！`, type: 'success', displayToUser: true });
            }
    
            // Apply skill points reward
            if (skillPointsReward > 0) {
                newStatusChanges.skillPoints = (currentProcessingStatus.skillPoints || 0) + skillPointsReward;
                notificationsInternal.push({ message: `任务奖励: 获得 ${skillPointsReward} 技能点！`, type: 'success', displayToUser: true });
            }
    
            return {
                changes: newStatusChanges,
                notifications: notificationsInternal,
                achievement: achievementsFromRewards.length > 0 ? achievementsFromRewards : undefined
            };
        },
    },
  ];

export const usePlayerStatus = (
    addNotification: (message: string, type: NotificationType, duration?: number, title?: string) => void,
    unlockAchievement: (achievementId: string) => void,
    setActiveDetailModal: (modal: DetailModalType | null) => void
) => {
  const [playerStatus, setPlayerStatus] = useState<PlayerStatus>(InitialPlayerStatus);
  const [isSummarizing, setIsSummarizing] = useState({ inventory: false, locations: false, quests: false, characters: false, importantEvents: false });
  const notificationTimestampsRef = useRef<Record<string, number>>({});

  const showRateLimitedNotification = useCallback((
    messageKey: string, 
    message: string,
    type: NotificationType,
    duration?: number,
    title?: string
  ): boolean => {
    const now = Date.now();
    const lastShown = notificationTimestampsRef.current[messageKey] || 0;
    const cooldown = 60 * 1000; 

    if (now - lastShown > cooldown) {
      addNotification(message, type, duration, title);
      notificationTimestampsRef.current[messageKey] = now;
      return true;
    }
    return false;
  }, [addNotification]);


  const allocateAttributePoint = useCallback((attributeKeyToAllocate: keyof CoreAttributes) => {
    setPlayerStatus(prevStatus => {
        if ((prevStatus.attributePoints || 0) <= 0) { addNotification(UIText.cannotAllocateAttribute, 'warning'); return prevStatus; }

        const currentAttributeValue = prevStatus.coreAttributes?.[attributeKeyToAllocate] ?? defaultCoreAttributes[attributeKeyToAllocate];
        const newAttributes = {
            ...(prevStatus.coreAttributes || defaultCoreAttributes),
            [attributeKeyToAllocate]: currentAttributeValue + 1
        };
        addNotification(UIText.attributeIncreasedNotification(getAttributeDisplayName(attributeKeyToAllocate), newAttributes[attributeKeyToAllocate]), 'success', 4000, "属性提升！");
        unlockAchievement('attribute_enhancer');
        if (attributeKeyToAllocate === 'strength' && newAttributes.strength >= 10 && (prevStatus.coreAttributes?.strength || 0) < 10) unlockAchievement('strength_manifested');
        if (attributeKeyToAllocate === 'agility' && newAttributes.agility >= 10 && (prevStatus.coreAttributes?.agility || 0) < 10) unlockAchievement('agile_as_wind');
        if (attributeKeyToAllocate === 'intelligence' && newAttributes.intelligence >= 10 && (prevStatus.coreAttributes?.intelligence || 0) < 10) unlockAchievement('light_of_intellect');
        if (attributeKeyToAllocate === 'charisma' && newAttributes.charisma >= 10 && (prevStatus.coreAttributes?.charisma || 0) < 10) unlockAchievement('charm_of_words');

        return { ...prevStatus, coreAttributes: newAttributes, attributePoints: (prevStatus.attributePoints || 0) - 1 };
    });
  }, [addNotification, unlockAchievement]);

  const allocateSkillPoint = useCallback((skillIdToAllocate: string) => {
    setPlayerStatus(prevStatus => {
        if ((prevStatus.skillPoints || 0) <= 0) { addNotification(UIText.cannotAllocateSkill, 'warning'); return prevStatus; }

        const existingSkillIndex = (prevStatus.skills || []).findIndex(s => s.id === skillIdToAllocate);
        let newSkillsArray = [...(prevStatus.skills || []).map(s => ({ ...s }))];
        let skillNameForNotification = "";
        let newLevelForNotification = 0;

        if (existingSkillIndex > -1) {
            const oldSkill = newSkillsArray[existingSkillIndex];
            newLevelForNotification = oldSkill.level + 1;
            newSkillsArray[existingSkillIndex] = {
                ...oldSkill,
                level: newLevelForNotification,
                currentXp: 0,
                xpToNextLevel: Math.floor((oldSkill.xpToNextLevel || 50) * 1.3) 
            };
            skillNameForNotification = newSkillsArray[existingSkillIndex].name;
            addNotification(UIText.skillLeveledUp(skillNameForNotification, newLevelForNotification), 'success', 4000, "技能提升！");
        } else {
            const baseSkillDefinition = defaultSkills.find(s => s.id === skillIdToAllocate);
            if (baseSkillDefinition) {
                newLevelForNotification = 1;
                const newSkill: Skill = {
                    ...baseSkillDefinition,
                    level: newLevelForNotification,
                    currentXp: 0,
                    xpToNextLevel: baseSkillDefinition.xpToNextLevel || 50
                };
                newSkillsArray.push(newSkill);
                skillNameForNotification = newSkill.name;
                addNotification(UIText.skillLearned(skillNameForNotification), 'success', 4000, "习得技能！");
            } else {
                addNotification(`错误：尝试分配点数给未知技能 ID "${skillIdToAllocate}"。`, "error");
                return prevStatus;
            }
        }
        unlockAchievement('skill_master_initiate');
        if (newSkillsArray.filter(s => s.level >= 5).length >= 3) unlockAchievement('three_arts_mastery');
        return { ...prevStatus, skills: newSkillsArray, skillPoints: (prevStatus.skillPoints || 0) - 1 };
    });
  }, [addNotification, unlockAchievement]);

  const processStoryUpdateForRPG = useCallback((
    storyUpdateText: string | undefined,
    currentMood?: string,
    currentTimeOfDay?: string
  ) => {
    if (!storyUpdateText && !currentMood && !currentTimeOfDay) return;

    setPlayerStatus(prevFullStatus => {
        let totalXpGainedThisTurn = 0;
        const notificationsToTrigger: RPGNotification[] = [];
        let achievementsToUnlockInternal: string[] = [];

        let workingStatus: PlayerStatus = {
            ...prevFullStatus,
            coreAttributes: { ...(prevFullStatus.coreAttributes || defaultCoreAttributes) },
            skills: (prevFullStatus.skills || defaultSkills.map(s => ({...s, level: 0, currentXp:0}))).map(s => ({ ...s })),
            inventory: prevFullStatus.inventory.map(item => ({...item})),
            visitedLocations: prevFullStatus.visitedLocations.map(loc => ({...loc})),
            quests: prevFullStatus.quests.map(q => ({...q, objectives: [...q.objectives]})),
            characterProfiles: prevFullStatus.characterProfiles.map(cp => ({...cp, notableInteractions: [...cp.notableInteractions]})),
            importantEvents: prevFullStatus.importantEvents.map(ev => ({...ev})),
            buffs: prevFullStatus.buffs.map(b => ({...b})),
            debuffs: prevFullStatus.debuffs.map(d => ({...d})),
            attributePoints: prevFullStatus.attributePoints || 0,
            skillPoints: prevFullStatus.skillPoints || 0,
            healthEnergy: { ...(prevFullStatus.healthEnergy || {current: 100, max: 100}) },
            mood: currentMood || prevFullStatus.mood,
            timeOfDay: currentTimeOfDay || prevFullStatus.timeOfDay,
        };
        
        workingStatus.buffs = workingStatus.buffs
            .map(b => (b.durationTurns > 0 ? { ...b, remainingTurns: b.remainingTurns - 1 } : b))
            .filter(b => b.durationTurns === -1 || b.remainingTurns > 0);
        workingStatus.debuffs = workingStatus.debuffs
            .map(d => (d.durationTurns > 0 ? { ...d, remainingTurns: d.remainingTurns - 1 } : d))
            .filter(d => d.durationTurns === -1 || d.remainingTurns > 0);


        let textBeingProcessed = storyUpdateText || ""; 

        const xpGainRegex = /\[RPG:xp_gain:(?<amount>\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi;
        let xpMatch;
        while ((xpMatch = xpGainRegex.exec(textBeingProcessed)) !== null) {
            const amount = parseInt(xpMatch.groups?.amount || '0', 10);
            if (!isNaN(amount) && amount > 0) {
                totalXpGainedThisTurn += amount;
            } else if (isNaN(amount)) {
                 console.warn("Malformed RPG:xp_gain tag (invalid amount):", xpMatch[0]);
            }
        }
        textBeingProcessed = textBeingProcessed.replace(xpGainRegex, "").trim();

        for (const processor of rpgTagProcessors) {
            let match;
            processor.regex.lastIndex = 0;
            while ((match = processor.regex.exec(textBeingProcessed)) !== null) {
                const result = processor.handler(match, workingStatus);
                const changesFromTag = result.changes;

                if (changesFromTag.coreAttributes) workingStatus.coreAttributes = { ...(workingStatus.coreAttributes || defaultCoreAttributes), ...changesFromTag.coreAttributes };
                if (changesFromTag.skills) workingStatus.skills = changesFromTag.skills;
                if (changesFromTag.quests) workingStatus.quests = changesFromTag.quests;
                if (changesFromTag.attributePoints !== undefined) workingStatus.attributePoints = changesFromTag.attributePoints;
                if (changesFromTag.skillPoints !== undefined) workingStatus.skillPoints = changesFromTag.skillPoints;
                if (changesFromTag.healthEnergy) workingStatus.healthEnergy = { ...workingStatus.healthEnergy, ...changesFromTag.healthEnergy };
                if (changesFromTag.buffs) workingStatus.buffs = changesFromTag.buffs;
                if (changesFromTag.debuffs) workingStatus.debuffs = changesFromTag.debuffs;
                
                const { coreAttributes, skills, attributePoints, skillPoints, healthEnergy, buffs, debuffs, quests, ...otherDirectChanges } = changesFromTag;
                workingStatus = { ...workingStatus, ...otherDirectChanges };

                if (result.notifications) notificationsToTrigger.push(...result.notifications);
                if (result.achievement) {
                    if (Array.isArray(result.achievement)) achievementsToUnlockInternal.push(...result.achievement);
                    else achievementsToUnlockInternal.push(result.achievement);
                }
            }
        }

        if (totalXpGainedThisTurn > 0) {
            const xpUpdateResult = calculateXpAndLevelChanges(workingStatus, totalXpGainedThisTurn);
            workingStatus = { ...workingStatus, ...xpUpdateResult.statusChanges };
            notificationsToTrigger.push(...xpUpdateResult.notifications);
            achievementsToUnlockInternal.push(...xpUpdateResult.achievementsToUnlock);
        }
        
        if(workingStatus.coreAttributes?.sanity !== undefined && workingStatus.coreAttributes.sanity <= 2 && workingStatus.currentDay >=3) {
            const existingLowSanityAchievement = AVAILABLE_ACHIEVEMENTS.find(ach => ach.id === 'price_of_clarity');
            if (existingLowSanityAchievement) achievementsToUnlockInternal.push(existingLowSanityAchievement.id);
        }

        const oldTimeIndex = TIME_OF_DAY_ORDER.indexOf(prevFullStatus.timeOfDay);
        const newTimeIndex = TIME_OF_DAY_ORDER.indexOf(workingStatus.timeOfDay);

        if (oldTimeIndex !== -1 && newTimeIndex !== -1 && newTimeIndex < oldTimeIndex && workingStatus.timeOfDay !== prevFullStatus.timeOfDay) { 
          workingStatus.currentDay = workingStatus.currentDay + 1;
          notificationsToTrigger.push({ message: UIText.dayAdvancedTo(workingStatus.currentDay), type: 'info', displayToUser: true });
        }

        const uniqueNotifications = Array.from(new Map(notificationsToTrigger.map(n => [`${n.type}_${n.message.substring(0,50)}`, n])).values());

        uniqueNotifications.forEach(n => {
            if (n.displayToUser) { 
                addNotification(n.message, n.type, n.duration, n.title);
            }
        });

        Array.from(new Set(achievementsToUnlockInternal)).forEach(achId => unlockAchievement(achId));

        return workingStatus;
    });
  }, [addNotification, unlockAchievement]);

  const summarizeAndUpdatePlayerStatus = useCallback((updates: Partial<PlayerStatus>) => setPlayerStatus(prev => ({ ...prev, ...updates })), []);

  const handleSummarizeAndShowDetails = useCallback(async <DataType,>(
    dialogueLog: DialogueLine[],
    summaryModelId: string,
    summaryType: DetailModalType,
    currentData: DataType[],
    serviceFn: (log: DialogueLine[], current: DataType[], modelId: string) => Promise<DataType[]>,
    updatePlayerStatusFn: (data: DataType[]) => void
  ) => {
    if(isSummarizing[summaryType as keyof typeof isSummarizing]) return;
    setIsSummarizing(prev => ({ ...prev, [summaryType]: true }));
    try {
      const updatedData = await serviceFn(dialogueLog, currentData, summaryModelId);
      updatePlayerStatusFn(updatedData);

      showRateLimitedNotification(
        'statusAutoUpdated',
        `${UIText[summaryType as keyof typeof UIText] || summaryType} ${UIText.statusAutoUpdated.split(' ')[1]}`,
        'info'
      );

      setActiveDetailModal(summaryType);
      if (summaryType === 'importantEvents' && updatedData.length >= 5) {
          unlockAchievement('event_tracker');
      }
       if (summaryType === 'inventory' && updatedData.length >= 10) {
          unlockAchievement('backpacker');
      }
    } catch (error: any) {
      addNotification(`Failed to summarize ${summaryType}: ${error.message}`, 'error');
      setActiveDetailModal(summaryType);
    }
    finally {
      setIsSummarizing(prev => ({ ...prev, [summaryType]: false }));
    }
  }, [addNotification, isSummarizing, setActiveDetailModal, unlockAchievement, showRateLimitedNotification]);

  const handleSummarizeInventoryAndShowDetails = useCallback((dialogueLog: DialogueLine[], summaryModelId: string) => {
    handleSummarizeAndShowDetails<InventoryItem>(dialogueLog, summaryModelId, 'inventory', playerStatus.inventory, summarizeInventory, (data) => summarizeAndUpdatePlayerStatus({ inventory: data }));
  }, [handleSummarizeAndShowDetails, playerStatus.inventory, summarizeAndUpdatePlayerStatus]);

  const handleSummarizeLocationsAndShowDetails = useCallback((dialogueLog: DialogueLine[], summaryModelId: string) => {
    handleSummarizeAndShowDetails<VisitedLocation>(dialogueLog, summaryModelId, 'locations', playerStatus.visitedLocations, summarizeLocations, (data) => summarizeAndUpdatePlayerStatus({ visitedLocations: data }));
  }, [handleSummarizeAndShowDetails, playerStatus.visitedLocations, summarizeAndUpdatePlayerStatus]);

  const handleSummarizeQuestsAndShowDetails = useCallback((dialogueLog: DialogueLine[], summaryModelId: string) => {
    handleSummarizeAndShowDetails<Quest>(dialogueLog, summaryModelId, 'quests', playerStatus.quests, summarizeQuests, (data) => summarizeAndUpdatePlayerStatus({ quests: data }));
  }, [handleSummarizeAndShowDetails, playerStatus.quests, summarizeAndUpdatePlayerStatus]);

  const handleSummarizeCharactersAndShowDetails = useCallback((dialogueLog: DialogueLine[], summaryModelId: string) => {
    handleSummarizeAndShowDetails<CharacterProfile>(dialogueLog, summaryModelId, 'characters', playerStatus.characterProfiles, summarizeCharacters, (data) => summarizeAndUpdatePlayerStatus({ characterProfiles: data }));
  }, [handleSummarizeAndShowDetails, playerStatus.characterProfiles, summarizeAndUpdatePlayerStatus]);

  const handleSummarizeImportantEventsAndShowDetails = useCallback((dialogueLog: DialogueLine[], summaryModelId: string) => {
    handleSummarizeAndShowDetails<ImportantEvent>(dialogueLog, summaryModelId, 'importantEvents', playerStatus.importantEvents, summarizeImportantEvents, (data) => summarizeAndUpdatePlayerStatus({ importantEvents: data }));
  }, [handleSummarizeAndShowDetails, playerStatus.importantEvents, summarizeAndUpdatePlayerStatus]);

  const handleSummarizeAll = useCallback(async (dialogueLog: DialogueLine[], summaryModelId: string) => {
    if (Object.values(isSummarizing).some(status => status)) return;

    showRateLimitedNotification('summarizingInfo', UIText.summarizingInfo, 'info', 2500);

    const tempIsSummarizingState = {...isSummarizing};

    const updateTempSummarizing = (type: DetailModalType, value: boolean) => {
      tempIsSummarizingState[type as keyof typeof tempIsSummarizingState] = value;
    };

    const callIndividualSummary = async <DataType,>(
        summaryType: DetailModalType,
        currentData: DataType[],
        serviceFn: (log: DialogueLine[], current: DataType[], modelId: string) => Promise<DataType[]>,
        updatePlayerStatusFn: (data: DataType[]) => void
      ) => {
        if(tempIsSummarizingState[summaryType as keyof typeof tempIsSummarizingState]) return;
        updateTempSummarizing(summaryType, true);
        try {
          const updatedData = await serviceFn(dialogueLog, currentData, summaryModelId);
          updatePlayerStatusFn(updatedData);
           if (summaryType === 'importantEvents' && updatedData.length >= 5) {
              unlockAchievement('event_tracker');
          }
          if (summaryType === 'inventory' && updatedData.length >= 10) {
            unlockAchievement('backpacker');
          }
        } catch (error: any) {
            console.warn(`Auto-summary for ${summaryType} failed: ${error.message}`);
        }
        finally {
            updateTempSummarizing(summaryType, false);
        }
    };

    await Promise.all([
        callIndividualSummary<InventoryItem>('inventory', playerStatus.inventory, summarizeInventory, (data) => summarizeAndUpdatePlayerStatus({ inventory: data })),
        callIndividualSummary<VisitedLocation>('locations', playerStatus.visitedLocations, summarizeLocations, (data) => summarizeAndUpdatePlayerStatus({ visitedLocations: data })),
        callIndividualSummary<Quest>('quests', playerStatus.quests, summarizeQuests, (data) => summarizeAndUpdatePlayerStatus({ quests: data })),
        callIndividualSummary<CharacterProfile>('characters', playerStatus.characterProfiles, summarizeCharacters, (data) => summarizeAndUpdatePlayerStatus({ characterProfiles: data })),
        callIndividualSummary<ImportantEvent>('importantEvents', playerStatus.importantEvents, summarizeImportantEvents, (data) => summarizeAndUpdatePlayerStatus({ importantEvents: data }))
    ]);

    showRateLimitedNotification('statusAutoUpdated', UIText.statusAutoUpdated, 'success', 2000);

    setPlayerStatus(currentLatestStatus => {
      if (currentLatestStatus.attributePoints > 0) {
        showRateLimitedNotification(
          "attribute_points_available_prompt", 
          UIText.attributePointsSpendPrompt(currentLatestStatus.attributePoints),
          'info',
          7000,
          UIText.characterGrowthTitle
        );
      }
      if (currentLatestStatus.skillPoints > 0) {
        showRateLimitedNotification(
          "skill_points_available_prompt", 
          UIText.skillPointsSpendPrompt(currentLatestStatus.skillPoints),
          'info',
          7000,
          UIText.characterGrowthTitle
        );
      }
      return currentLatestStatus;
    });

  }, [addNotification, playerStatus, summarizeAndUpdatePlayerStatus, isSummarizing, setActiveDetailModal, setPlayerStatus, unlockAchievement, showRateLimitedNotification]);

  return {
    playerStatus,
    setPlayerStatus,
    isSummarizing,
    allocateAttributePoint,
    allocateSkillPoint,
    processStoryUpdateForRPG,
    handleSummarizeInventoryAndShowDetails,
    handleSummarizeLocationsAndShowDetails,
    handleSummarizeQuestsAndShowDetails,
    handleSummarizeCharactersAndShowDetails,
    handleSummarizeImportantEventsAndShowDetails,
    handleSummarizeAll,
  };
};
