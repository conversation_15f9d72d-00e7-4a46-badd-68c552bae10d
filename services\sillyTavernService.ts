import { 
  SillyTavernCharCard, 
  SillyTavernCharCardEntry, 
  SillyTavernEntry, 
  SillyTavernCharBook,
  CharacterCardData,
  CustomNarrativePrimaryElement,
  RegexRule
} from '../types';
import { extractCharaDataFromPng } from '../utils/pngCardParser';
import { localApiService } from './localApiService';

export interface EnhancedCharacterCard extends SillyTavernCharCard {
  // Enhanced fields for better compatibility
  version?: string;
  creator?: string;
  character_version?: string;
  extensions?: Record<string, any>;
  system_prompt?: string;
  post_history_instructions?: string;
  alternate_greetings?: string[];
  character_book?: EnhancedCharBook;
  lorebook?: EnhancedCharBook;
}

export interface EnhancedCharBook extends SillyTavernCharBook {
  // Enhanced world book features
  extensions?: Record<string, any>;
  global_priority?: number;
  character_filter?: string[];
  world_info_before?: string;
  world_info_after?: string;
  entries: EnhancedCharBookEntry[];
}

export interface EnhancedCharBookEntry extends SillyTavernCharCardEntry {
  // Enhanced entry features
  id?: string;
  uid?: number;
  priority?: number;
  order?: number;
  probability?: number;
  useProbability?: boolean;
  depth?: number;
  selectiveLogic?: number;
  comment_display?: string;
  filters?: string[];
  extensions?: Record<string, any>;
  case_sensitive?: boolean;
  match_whole_words?: boolean;
  use_group_scoring?: boolean;
  automation_id?: string;
  role?: number;
  vectorized?: boolean;
  prevent_recursion?: boolean;
}

export interface CharacterRelationship {
  id: string;
  sourceCharacterId: string;
  targetCharacterId: string;
  relationshipType: 'friend' | 'enemy' | 'family' | 'romantic' | 'neutral' | 'unknown';
  strength: number; // -100 to 100
  description: string;
  history: Array<{
    timestamp: number;
    event: string;
    impact: number;
  }>;
}

export interface WorldBookHierarchy {
  id: string;
  parentId?: string;
  children: string[];
  depth: number;
  dependencies: string[];
  conflicts: string[];
}

export class SillyTavernService {
  private characterRelationships: Map<string, CharacterRelationship[]> = new Map();
  private worldBookHierarchy: Map<string, WorldBookHierarchy> = new Map();

  // Enhanced character card processing
  async processCharacterCard(
    cardData: EnhancedCharacterCard,
    fileName: string
  ): Promise<{
    characterData: CharacterCardData;
    worldBookElements: CustomNarrativePrimaryElement[];
    relationships: CharacterRelationship[];
    metadata: any;
  }> {
    // Process basic character data
    const characterData: CharacterCardData = {
      characterName: cardData.name || cardData.char_name || 'Unknown Character',
      characterDescription: this.selectBestDescription(cardData),
      characterPersonality: cardData.personality || '',
      characterOpeningMessage: this.selectBestGreeting(cardData),
      characterScenario: cardData.scenario || cardData.world_scenario || '',
      characterExampleDialogue: cardData.mes_example || cardData.example_dialogue || '',
      characterPortraitKeywords: this.generatePortraitKeywords(cardData)
    };

    // Process world book data
    const worldBookElements = await this.processWorldBookData(cardData, fileName);

    // Extract relationships
    const relationships = this.extractCharacterRelationships(cardData);

    // Extract metadata
    const metadata = {
      version: cardData.version || cardData.character_version || '1.0',
      creator: cardData.creator || 'Unknown',
      extensions: cardData.extensions || {},
      alternateGreetings: cardData.alternate_greetings || [],
      systemPrompt: cardData.system_prompt || '',
      postHistoryInstructions: cardData.post_history_instructions || ''
    };

    return {
      characterData,
      worldBookElements,
      relationships,
      metadata
    };
  }

  private selectBestDescription(cardData: EnhancedCharacterCard): string {
    // Priority: description > char_persona > fallback
    const candidates = [
      cardData.description,
      cardData.char_persona
    ].filter(Boolean);

    if (candidates.length === 0) return '';

    // Return the longest non-empty description
    return candidates.reduce((best, current) => 
      current.length > best.length ? current : best
    );
  }

  private selectBestGreeting(cardData: EnhancedCharacterCard): string {
    const greetings = [
      cardData.first_mes,
      cardData.char_greeting,
      ...(cardData.alternate_greetings || [])
    ].filter(Boolean);

    return greetings[0] || '';
  }

  private generatePortraitKeywords(cardData: EnhancedCharacterCard): string {
    const keywords: string[] = [];

    // Add character name
    if (cardData.name) keywords.push(cardData.name);

    // Add tags
    if (cardData.tags) keywords.push(...cardData.tags);

    // Extract keywords from description
    const description = this.selectBestDescription(cardData);
    const extractedKeywords = this.extractKeywordsFromText(description);
    keywords.push(...extractedKeywords);

    return keywords.join(', ');
  }

  private extractKeywordsFromText(text: string): string[] {
    if (!text) return [];

    // Simple keyword extraction - can be enhanced with NLP
    const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should']);
    
    const words = text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !commonWords.has(word))
      .slice(0, 10); // Limit to 10 keywords

    return words;
  }

  private async processWorldBookData(
    cardData: EnhancedCharacterCard,
    fileName: string
  ): Promise<CustomNarrativePrimaryElement[]> {
    const elements: CustomNarrativePrimaryElement[] = [];

    // Process character_book
    if (cardData.character_book?.entries?.length) {
      const element = this.createWorldBookElement(
        cardData.character_book,
        `[${cardData.name || fileName}] Character Book`
      );
      elements.push(element);
    }

    // Process lorebook
    if (cardData.lorebook?.entries?.length) {
      const element = this.createWorldBookElement(
        cardData.lorebook,
        `[${cardData.name || fileName}] Lorebook`
      );
      elements.push(element);
    }

    // Process direct entries
    if (cardData.entries) {
      const entriesArray = Array.isArray(cardData.entries) 
        ? cardData.entries 
        : Object.values(cardData.entries);
      
      if (entriesArray.length > 0) {
        const element = this.createWorldBookElementFromEntries(
          entriesArray,
          `[${cardData.name || fileName}] World Info`
        );
        elements.push(element);
      }
    }

    return elements;
  }

  private createWorldBookElement(
    charBook: EnhancedCharBook,
    name: string
  ): CustomNarrativePrimaryElement {
    const element: CustomNarrativePrimaryElement = {
      id: `wb_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
      name,
      isActive: true,
      subElements: []
    };

    charBook.entries.forEach((entry, index) => {
      const keys = entry.keys || [];
      const key = entry.comment || (keys.length > 0 ? keys[0] : `Entry ${index + 1}`);
      
      element.subElements.push({
        id: `sub_${element.id}_${index}`,
        key: key.trim(),
        value: (entry.content || '').trim(),
        isActive: !entry.disable
      });
    });

    return element;
  }

  private createWorldBookElementFromEntries(
    entries: (SillyTavernCharCardEntry | SillyTavernEntry)[],
    name: string
  ): CustomNarrativePrimaryElement {
    const element: CustomNarrativePrimaryElement = {
      id: `wb_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
      name,
      isActive: true,
      subElements: []
    };

    entries.forEach((entry, index) => {
      const keys = (entry as SillyTavernCharCardEntry).keys || (entry as SillyTavernEntry).key || [];
      const key = entry.comment || (keys.length > 0 ? keys[0] : `Entry ${index + 1}`);
      
      element.subElements.push({
        id: `sub_${element.id}_${index}`,
        key: key.trim(),
        value: (entry.content || '').trim(),
        isActive: !entry.disable
      });
    });

    return element;
  }

  private extractCharacterRelationships(cardData: EnhancedCharacterCard): CharacterRelationship[] {
    const relationships: CharacterRelationship[] = [];

    // Extract relationships from description and scenario
    const text = `${cardData.description || ''} ${cardData.scenario || ''} ${cardData.char_persona || ''}`;
    
    // Simple relationship extraction - can be enhanced with NLP
    const relationshipPatterns = [
      /(?:friend|ally|companion|partner)\s+(?:of|with|to)\s+([A-Z][a-z]+)/gi,
      /(?:enemy|rival|opponent)\s+(?:of|with|to)\s+([A-Z][a-z]+)/gi,
      /(?:sister|brother|mother|father|parent|child)\s+(?:of|to)\s+([A-Z][a-z]+)/gi,
      /(?:lover|boyfriend|girlfriend|spouse|husband|wife)\s+(?:of|to)\s+([A-Z][a-z]+)/gi
    ];

    relationshipPatterns.forEach((pattern, index) => {
      const matches = text.matchAll(pattern);
      for (const match of matches) {
        const targetName = match[1];
        const types = ['friend', 'enemy', 'family', 'romantic'] as const;
        
        relationships.push({
          id: `rel_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
          sourceCharacterId: cardData.name || 'unknown',
          targetCharacterId: targetName,
          relationshipType: types[index] || 'neutral',
          strength: 50, // Default neutral strength
          description: match[0],
          history: [{
            timestamp: Date.now(),
            event: 'Relationship extracted from character description',
            impact: 0
          }]
        });
      }
    });

    return relationships;
  }

  // Enhanced export functionality
  async exportCharacterCard(
    characterData: CharacterCardData,
    worldBookElements: CustomNarrativePrimaryElement[],
    relationships: CharacterRelationship[],
    format: 'json' | 'png' = 'json'
  ): Promise<Blob> {
    const enhancedCard: EnhancedCharacterCard = {
      name: characterData.characterName,
      description: characterData.characterDescription,
      personality: characterData.characterPersonality,
      first_mes: characterData.characterOpeningMessage,
      scenario: characterData.characterScenario,
      mes_example: characterData.characterExampleDialogue,
      tags: characterData.characterPortraitKeywords.split(',').map(tag => tag.trim()),
      version: '2.0',
      creator: 'MemoryAble',
      character_book: this.convertToCharBook(worldBookElements),
      extensions: {
        memoryable: {
          relationships,
          exportedAt: new Date().toISOString()
        }
      }
    };

    if (format === 'json') {
      const jsonString = JSON.stringify(enhancedCard, null, 2);
      return new Blob([jsonString], { type: 'application/json' });
    } else {
      // PNG export would require canvas manipulation
      // For now, return JSON blob
      const jsonString = JSON.stringify(enhancedCard, null, 2);
      return new Blob([jsonString], { type: 'application/json' });
    }
  }

  private convertToCharBook(elements: CustomNarrativePrimaryElement[]): EnhancedCharBook {
    const entries: EnhancedCharBookEntry[] = [];

    elements.forEach(element => {
      element.subElements.forEach((subElement, index) => {
        entries.push({
          keys: [subElement.key],
          comment: subElement.key,
          content: subElement.value,
          disable: !subElement.isActive,
          id: subElement.id,
          priority: 100 - index, // Higher priority for earlier entries
          order: index,
          depth: 4,
          selectiveLogic: 0,
          case_sensitive: false,
          match_whole_words: false
        });
      });
    });

    return {
      name: 'MemoryAble World Book',
      description: 'Exported from MemoryAble',
      scan_depth: 4,
      token_budget: 2048,
      recursive_scanning: false,
      entries
    };
  }

  // Batch operations
  async importMultipleCharacterCards(files: File[]): Promise<{
    successful: Array<{ file: string; data: any }>;
    failed: Array<{ file: string; error: string }>;
  }> {
    const successful: Array<{ file: string; data: any }> = [];
    const failed: Array<{ file: string; error: string }> = [];

    for (const file of files) {
      try {
        const data = await this.importSingleCharacterCard(file);
        successful.push({ file: file.name, data });
      } catch (error) {
        failed.push({ file: file.name, error: error.message });
      }
    }

    return { successful, failed };
  }

  private async importSingleCharacterCard(file: File): Promise<any> {
    const fileName = file.name;
    
    if (fileName.toLowerCase().endsWith('.png')) {
      const arrayBuffer = await file.arrayBuffer();
      const base64Data = extractCharaDataFromPng(arrayBuffer);
      
      if (!base64Data) {
        throw new Error('No character data found in PNG file');
      }
      
      const jsonString = new TextDecoder('utf-8').decode(
        Uint8Array.from(atob(base64Data), c => c.charCodeAt(0))
      );
      
      return JSON.parse(jsonString);
    } else if (fileName.toLowerCase().endsWith('.json') || fileName.toLowerCase().endsWith('.jsonc')) {
      const text = await file.text();
      return JSON.parse(text);
    } else {
      throw new Error('Unsupported file format');
    }
  }

  // Relationship management
  addRelationship(relationship: CharacterRelationship): void {
    const sourceRelationships = this.characterRelationships.get(relationship.sourceCharacterId) || [];
    sourceRelationships.push(relationship);
    this.characterRelationships.set(relationship.sourceCharacterId, sourceRelationships);
  }

  getRelationships(characterId: string): CharacterRelationship[] {
    return this.characterRelationships.get(characterId) || [];
  }

  updateRelationshipStrength(relationshipId: string, newStrength: number, reason: string): void {
    for (const [characterId, relationships] of this.characterRelationships.entries()) {
      const relationship = relationships.find(r => r.id === relationshipId);
      if (relationship) {
        const oldStrength = relationship.strength;
        relationship.strength = Math.max(-100, Math.min(100, newStrength));
        relationship.history.push({
          timestamp: Date.now(),
          event: reason,
          impact: relationship.strength - oldStrength
        });
        break;
      }
    }
  }
}

// Singleton instance
export const sillyTavernService = new SillyTavernService();
