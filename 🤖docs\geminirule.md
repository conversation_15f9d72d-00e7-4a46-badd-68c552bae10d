todolist规范修复rule

> ## 架构修复TodoList文档规范
> 
> ## 文档结构模板
> 
> ### 1\. 顶部紧急概览 (必需)
> 
> ```markdown
> > **🚨 [模块名称]架构问题 - 紧急修复方案 🚨**
> 
> >
> 
> > **危机等级**: 🔴/🟠/🟡 [RED/ORANGE/YELLOW] - [问题描述]
> 
> > **修复紧迫性**: [时间要求] - [后果说明]
> 
> > **技术债务**: [X]个高优先级修复任务，涉及[核心模块]
> ```
> 
> ### 2\. 核心问题概览 (3分钟快速了解)
> 
> - **灾难性问题发现**: 用数字列表，每项说明具体问题和影响
> - **修复策略**: 使用Mermaid流程图展示修复路径
> - **实施路径**: 表格形式，包含阶段/优先级/任务数/关键成果/风险评估
> - **预期成果**: 格式的成果列表，量化收益
> 
> ### 3\. 上下文交接蓝图预留空间 (必需)
> 
> ```markdown
> ## 📋 **上下文交接蓝图预留空间**
> 
> ### 当前架构状态图
> ```
> 
> \[预留空间 - 架构现状图\]
> 
> 待填充: 当前重复实现关系图
> 
> ```shell
> ### 目标架构蓝图
> ```
> 
> \[预留空间 - 目标架构图\]
> 
> 待填充: 修复后清洁架构图
> 
> ### 4\. 问题详细分析
> 
> - 使用 标记最高优先级问题
> - 用 保留/删除 标记明确决策
> - 按技术层级组织 (Data层/Domain层/Presentation层)
> 
> ### 5\. 阶段性任务清单
> 
> - 使用checkbox格式: `- [ ] **任务名称**`
> - 每个任务包含: 文件路径、原因说明、验证标准
> - 按执行顺序编号阶段
> 
> ## 格式规范
> 
> ### Emoji使用标准
> 
> | 用途 | Emoji | 含义 |
> 
> |------|-------|------|
> 
> | 紧急警告 | | 最高优先级问题 |
> 
> | 问题发现 | | 严重架构问题 |
> 
> | 修复策略 | | 解决方案 |
> 
> | 实施路径 | | 执行计划 |
> 
> | 预期成果 | | 修复收益 |
> 
> | 数据层 | | Data层相关 |
> 
> | 领域层 | | Domain层相关 |
> 
> | 展示层 | | Presentation层相关 |
> 
> | 架构冲突 | | Clean Architecture违反 |
> 
> | 清理任务 | | 代码清理 |
> 
> | 依赖注入 | | DI配置 |
> 
> | 目录重组 | | 文件结构 |
> 
> ### 优先级标识
> 
> - P0: 极高优先级 (阻塞性问题)
> - P1: 高优先级 (影响开发效率)
> - P2: 中优先级 (改善代码质量)
> - P3: 低优先级 (优化和清理)
> 
> ### 风险评估标准
> 
> - 极高: 可能导致系统崩溃或无法编译
> - 高: 影响核心功能，需要大量重构
> - 中: 影响代码质量，需要仔细处理
> - 低: 优化改进，风险可控
> 
> ### 表格格式要求
> 
> ```markdown
> | 阶段 | 优先级 | 任务数 | 关键成果 | 风险评估 |
> 
> | --------------- | ------ | ------ | ---------------------- | -------- |
> 
> | **[阶段名称]** | [优先级] | [X]个 | [具体成果描述] | [风险级别] |
> ```
> 
> ## 内容要求
> 
> ### 问题描述原则
> 
> 1. **量化影响**: 使用具体数字 (13+重复UseCase，5个重复模型)
> 2. **明确后果**: 说明不修复的风险
> 3. **技术精确**: 使用准确的技术术语
> 4. **决策明确**: 每个问题都有明确的保留/删除决策
> 
> ### 任务描述原则
> 
> 1. **可执行性**: 每个任务都可以立即执行
> 2. **可验证性**: 包含明确的完成标准
> 3. **原因说明**: 解释为什么要这样做
> 4. **影响评估**: 说明对其他模块的影响
> 
> ### 追踪要求
> 
> ```markdown
> ### 进度统计
> 
> - **总任务数**: [X]个任务 (+[Y]个新增任务)
> 
> - **已完成**: [完成数]/[总数]
> 
> - **进行中**: [进行数]/[总数]
> 
> - **待开始**: [待开始数]/[总数]
> 
> ### 阶段完成状态
> 
> - [ ] 阶段一：[名称] ([完成数]/[总数])
> 
> - [ ] 阶段二：[名称] ([完成数]/[总数])
> ```
> 
> ## 成功标准
> 
> ### 文档质量检查
> 
> - 新窗口3分钟内能理解问题和解决方案
> - 所有任务都可以立即执行
> - 风险评估覆盖所有高危操作
> - 预留空间为后续协作留足余地
> - 完成标准明确且可验证
> 
> ### 技术标准检查
> 
> - 严格遵循Clean Architecture原则
> - 每个功能只有唯一实现
> - 命名规范一致性
> - 错误处理标准化
> - 测试覆盖率要求明确
> 
> ## 文档维护
> 
> ### 更新原则
> 
> 1. **版本标识**: 每次重大更新增加版本号
> 2. **变更记录**: 在文档底部记录重要变更
> 3. **状态同步**: 实时更新进度和发现的新问题
> 4. **影响评估**: 记录修复对其他模块的影响
> 
> ### 交接要求
> 
> 1. **完整性**: 包含所有必要的上下文信息
> 2. **可读性**: 新团队成员能快速上手
> 3. **可追溯性**: 决策过程和原因清晰记录
> 4. **可扩展性**: 为未来发现的问题预留空间
> 
> 这个规范基于GymBro项目Auth模块修复的成功实践，确保架构修复文档的标准化和可执行性。

todolist规范施工rule

> ## 通用代码施工规范 (Universal Code Construction Rules)
> 
> ## 施工文档结构模板
> 
> ### 1\. 顶部施工概览 (必需)
> 
> ```markdown
> > **🚧 [项目/模块名称]代码施工方案 🚧**
> >
> > **施工等级**: 🔴/🟠/🟡 [MAJOR/MODERATE/MINOR] - [施工性质描述]
> > **施工紧迫性**: [时间要求] - [不完成的后果]
> > **工程量**: [X]个任务，涉及[Y]个文件，预计[Z]工时
> ```
> 
> ### 2\. 核心施工概览 (5分钟快速了解)
> 
> - **施工目标**: 用数字列表，每项说明具体目标和预期效果
> - **施工策略**: 使用Mermaid流程图展示施工路径
> - **实施计划**: 表格形式，包含阶段/优先级/任务数/关键里程碑/风险评估
> - **预期产出**: 格式的成果列表，量化收益
> 
> ### 3\. 上下文施工蓝图预留空间 (必需)
> 
> ```markdown
> ## 📋 **上下文施工蓝图预留空间**
> 
> ### 当前代码结构图
> ```
> 
> \[预留空间 - 现状架构图\]  
> 待填充: 当前代码组织关系图
> 
> ```shell
> ### 目标代码结构图
> ```
> 
> \[预留空间 - 目标架构图\]  
> 待填充: 施工后代码组织图
> 
> ```shell
> ### 关键文件依赖图
> ```
> 
> \[预留空间 - 依赖关系图\]  
> 待填充: 文件间依赖和影响关系
> 
> ### 4\. 详细施工分析
> 
> - 使用 标记最高优先级任务
> - 用 新增/修改/删除 标记明确操作
> - 按技术层级组织 (基础设施层/业务逻辑层/接口层等)
> 
> ### 5\. 阶段性施工清单
> 
> - 使用checkbox格式: `- [ ] **任务名称**`
> - 每个任务包含: 文件路径、操作类型、完成标准
> - 按执行顺序编号阶段
> 
> ## 格式规范
> 
> ### Emoji使用标准
> 
> | 用途 | Emoji | 含义 |
> | --- | --- | --- |
> | 紧急任务 |  | 最高优先级施工 |
> | 施工目标 |  | 核心目标 |
> | 施工策略 |  | 整体规划 |
> | 实施计划 |  | 执行方案 |
> | 预期产出 |  | 施工收益 |
> | 新建文件 |  | 创建新代码 |
> | 修改代码 |  | 更新现有代码 |
> | 删除代码 |  | 移除废弃代码 |
> | 重构优化 |  | 代码重构 |
> | 测试验证 |  | 测试相关 |
> | 文档更新 |  | 文档维护 |
> | 配置调整 |  | 配置文件 |
> | 依赖管理 |  | 依赖处理 |
> 
> ### 优先级标识
> 
> - P0: 极高优先级 (阻塞性任务，必须首先完成)
> - P1: 高优先级 (核心功能，影响主要特性)
> - P2: 中优先级 (重要改进，提升代码质量)
> - P3: 低优先级 (优化细节，可延后处理)
> 
> ### 风险评估标准
> 
> - 极高: 可能导致系统不可用或数据丢失
> - 高: 影响核心功能，需要大量测试验证
> - 中: 影响局部功能，需要仔细处理
> - 低: 优化改进，风险可控
> 
> ### 表格格式要求
> 
> ```markdown
> | 阶段            | 优先级 | 任务数 | 关键里程碑             | 风险评估 | 预计工时 |
> | --------------- | ------ | ------ | ---------------------- | -------- | -------- |
> | **[阶段名称]**  | [优先级] | [X]个   | [具体里程碑描述]       | [风险级别] | [X]小时   |
> ```
> 
> ## 内容要求
> 
> ### 任务描述原则
> 
> 1. **操作明确**: 使用动词开头，明确说明要做什么
> 2. **路径具体**: 包含完整的文件路径或代码位置
> 3. **标准清晰**: 包含可验证的完成标准
> 4. **原因说明**: 解释为什么要执行这个任务
> 5. **影响评估**: 说明对其他模块的潜在影响
> 
> ### 代码规范要求
> 
> 1. **命名一致性**: 统一的命名规范和风格
> 2. **结构清晰**: 合理的文件组织和模块划分
> 3. **注释规范**: 关键逻辑必须包含清晰注释
> 4. **错误处理**: 统一的错误处理和异常管理
> 5. **测试覆盖**: 核心功能必须包含相应测试
> 
> ### 质量控制标准
> 
> ```markdown
> ### 代码质量检查清单
> - [ ] **语法正确**: 代码能够正常编译/运行
> - [ ] **逻辑完整**: 业务逻辑实现完整
> - [ ] **异常处理**: 包含适当的错误处理
> - [ ] **性能考虑**: 无明显性能问题
> - [ ] **安全检查**: 符合安全编码规范
> - [ ] **测试验证**: 通过相关测试用例
> - [ ] **文档同步**: 相关文档已更新
> ```
> 
> ## 施工流程管控
> 
> ### 施工前检查
> 
> ```markdown
> ### 施工准备检查清单
> - [ ] **环境准备**: 开发环境配置正确
> - [ ] **依赖确认**: 所需依赖已安装/更新
> - [ ] **备份完成**: 重要代码已备份
> - [ ] **权限验证**: 具备必要的操作权限
> - [ ] **资源确认**: 时间和人力资源已分配
> ```
> 
> ### 施工中监控
> 
> ```markdown
> ### 进度统计
> - **总任务数**: [X]个任务 (+[Y]个新增任务)
> - **已完成**: [完成数]/[总数] ([百分比]%)
> - **进行中**: [进行数]/[总数] ([百分比]%)
> - **待开始**: [待开始数]/[总数] ([百分比]%)
> - **遇到问题**: [问题数]个 (详见问题跟踪)
> 
> ### 阶段完成状态
> - [ ] 阶段一：[名称] ([完成数]/[总数]) - [状态说明]
> - [ ] 阶段二：[名称] ([完成数]/[总数]) - [状态说明]
> - [ ] 阶段三：[名称] ([完成数]/[总数]) - [状态说明]
> ```
> 
> ### 施工后验收
> 
> ```markdown
> ### 验收标准检查
> - [ ] **功能验证**: 所有功能按预期工作
> - [ ] **性能测试**: 性能指标达到要求
> - [ ] **兼容性**: 与现有系统兼容
> - [ ] **安全检查**: 通过安全审查
> - [ ] **文档更新**: 相关文档已同步更新
> - [ ] **部署就绪**: 可以安全部署到生产环境
> ```
> 
> ## 风险管控
> 
> ### 风险识别与预防
> 
> ```markdown
> ### 高风险操作识别
> - 🚨 **数据库结构变更**: [具体风险和预防措施]
> - 🚨 **核心算法修改**: [具体风险和预防措施]
> - 🚨 **第三方依赖升级**: [具体风险和预防措施]
> - 🚨 **配置文件变更**: [具体风险和预防措施]
> ```
> 
> ### 回滚方案
> 
> ```markdown
> ### 回滚策略
> | 风险场景 | 触发条件 | 回滚步骤 | 预计用时 | 责任人 |
> |----------|----------|----------|----------|--------|
> | [场景1] | [条件] | [步骤] | [时间] | [人员] |
> | [场景2] | [条件] | [步骤] | [时间] | [人员] |
> ```
> 
> ## 施工监控与报告
> 
> ### 日报模板
> 
> ```markdown
> ### 施工日报 - [日期]
> **今日完成**:
> - [任务1]: [状态] - [说明]
> - [任务2]: [状态] - [说明]
> 
> **遇到问题**:
> - [问题1]: [影响] - [解决方案]
> - [问题2]: [影响] - [解决方案]
> 
> **明日计划**:
> - [任务1]: [预期完成时间]
> - [任务2]: [预期完成时间]
> 
> **风险提醒**:
> - [风险点]: [影响程度] - [应对措施]
> ```
> 
> ### 里程碑报告
> 
> ```markdown
> ### [里程碑名称] 完成报告
> **完成时间**: [日期]
> **完成质量**: [质量评估]
> **关键成果**: 
> - ✅ [成果1]
> - ✅ [成果2]
> **经验总结**:
> - [经验1]
> - [经验2]
> **后续建议**:
> - [建议1]
> - [建议2]
> ```
> 
> ## 成功标准
> 
> ### 文档质量检查
> 
> - 新团队成员5分钟内能理解施工目标和方案
> - 所有任务都有明确的执行步骤和验收标准
> - 风险评估覆盖所有潜在问题
> - 预留空间为团队协作提供充足信息
> - 进度跟踪机制完整有效
> 
> ### 技术标准检查
> 
> - 符合项目既定的技术规范和编码标准
> - 代码结构清晰，可维护性良好
> - 测试覆盖率达到项目要求
> - 性能指标满足预期目标
> - 安全规范得到有效执行
> 
> ### 交付标准检查
> 
> - 所有计划功能均已实现并通过验证
> - 相关文档已同步更新
> - 部署和配置文档完整
> - 团队知识已有效传递
> - 后续维护方案明确
> 
> ## 文档维护
> 
> ### 更新原则
> 
> 1. **版本控制**: 每次重大更新增加版本号和变更日志
> 2. **实时同步**: 施工进度和发现的问题及时更新
> 3. **决策记录**: 重要技术决策和变更原因详细记录
> 4. **知识沉淀**: 经验教训和最佳实践及时总结
> 
> ### 交接要求
> 
> 1. **完整性**: 包含所有必要的技术细节和上下文信息
> 2. **可理解性**: 新接手人员能快速理解和继续工作
> 3. **可追溯性**: 每个决策和变更都有清晰的来龙去脉
> 4. **可扩展性**: 为未来的功能扩展和维护留有充分空间
> 
> ### 归档标准
> 
> ```markdown
> ### 项目归档清单
> - [ ] **源代码**: 完整的代码仓库和版本历史
> - [ ] **文档资料**: 设计文档、API文档、用户手册等
> - [ ] **配置文件**: 部署配置、环境配置等
> - [ ] **测试资料**: 测试用例、测试报告、性能基准等
> - [ ] **运维资料**: 部署指南、监控配置、故障处理手册等
> ```

---

Note: **You should always communicate with the user in Chinese** and use **smaller blocks for editing**