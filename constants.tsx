
import React from 'react';
import { ImageGenerationApiConfig, GameSettingsData, AvailableModel, PlayerStatus, AvailableImagePromptStyle, Achievement, CoreAttributes, Skill, CustomNarrativePrimaryElement, LocalStorageKeys, CharacterCardData, AchievementTier, AchievementCategory, Theme, RegexRule } from './types'; 

export const APP_TITLE_CN = "MemoryAble"; 
export const APP_TITLE_EN = "MemoryAble"; 
export const APP_VERSION = "1.4.0"; 

// Simple styled component for the app title
export const AppTitleStyled: React.FC = () => (
  <span className="app-title-gradient">{APP_TITLE_CN}</span>
);

export const Icons = {
  ArrowPath: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
    </svg>
  ),
  Send: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
    </svg>
  ),
  Save: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
    </svg>
  ),
  Load: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
    </svg>
  ),
  Delete: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c1.153 0 2.243.096 3.287.27M1.925 6.071a48.083 48.083 0 01-.381-.067A48.072 48.072 0 001.188 5.79M1.925 6.071a48.616 48.616 0 003.551 4.613M4.772 5.79c.008.015.015.03.024.045M19.206 5.79c-.008.015-.015.03-.024.045m0 0a48.583 48.583 0 00-3.551 4.613m0 0A48.072 48.072 0 011.188 5.79m0 0A48.083 48.083 0 01.425 5.703M12 12.75h.008v.008H12v-.008z" />
    </svg>
  ),
  Menu: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
    </svg>
  ),
  Close: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
    </svg>
  ),
  Profile: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
    </svg>
  ),
  AdjustmentsHorizontal: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75" />
    </svg>
  ),
  Trash: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c1.153 0 2.243.096 3.287.27M1.925 6.071a48.083 48.083 0 01-.381-.067A48.072 48.072 0 001.188 5.79M1.925 6.071a48.616 48.616 0 003.551 4.613M4.772 5.79c.008.015.015.03.024.045M19.206 5.79c-.008.015-.015.03-.024.045m0 0a48.583 48.583 0 00-3.551 4.613m0 0A48.072 48.072 0 011.188 5.79m0 0A48.083 48.083 0 01.425 5.703M12 12.75h.008v.008H12v-.008z" />
    </svg>
  ),
  Pencil: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
    </svg>
  ),
  ChatBubble: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 011.037-.443 48.282 48.282 0 005.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
    </svg>
  ),
  Sun: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v2.25m6.364.386l-1.591 1.591M21 12h-2.25m-.386 6.364l-1.591-1.591M12 18.75V21m-6.364-.386l1.591-1.591M3 12h2.25m.386-6.364l1.591 1.591M12 12a3.75 3.75 0 110 7.5 3.75 3.75 0 010-7.5z" />
    </svg>
  ),
  Moon: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z" />
    </svg>
  ),
  Eye: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
  ),
  EyeSlash: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" />
    </svg>
  ),
  ChevronDown: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
    </svg>
  ),
  ChevronRight: (props: React.SVGProps<SVGSVGElement>) => ( // Chevron Right Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
    </svg>
  ),
  ArrowDownTray: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
    </svg>
  ),
  ArrowUpTray: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5" />
    </svg>
  ),
  CheckCircle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  Check: (props: React.SVGProps<SVGSVGElement>) => ( // Check Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
    </svg>
  ),
  XCircle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  ExclamationTriangle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
    </svg>
  ),
  InformationCircle: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
    </svg>
  ),
  Trophy: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 013 3h-15a3 3 0 013-3m9 0v-4.5A3.375 3.375 0 0012.75 9.75H11.25A3.375 3.375 0 007.5 13.5v4.5m9 0h-9" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M9.75 21a3.75 3.75 0 01-3.75-3.75H18a3.75 3.75 0 01-3.75 3.75H9.75zM12 2.25c-1.32 0-2.5.708-3.103 1.787A4.5 4.5 0 0012 6.75a4.5 4.5 0 003.103-2.713A4.503 4.503 0 0012 2.25z" />
    </svg>
  ),
  Heart: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M11.645 20.91l-.007-.003-.022-.012a15.247 15.247 0 01-.383-.218 25.18 25.18 0 01-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0112 5.052 5.5 5.5 0 0116.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 01-4.244 3.17 15.247 15.247 0 01-.383.218l-.022.012-.007.004-.003.001a.752.752 0 01-.704 0l-.003-.001z" />
    </svg>
  ),
  Bag: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 10-7.5 0v4.5m11.356-1.993l1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 01-1.12-1.243l1.264-12A1.125 1.125 0 015.513 7.5h12.974c.576 0 1.059.435 1.119 1.007zM8.625 10.5a.375.375 0 11-.75 0 .375.375 0 01.75 0zm7.5 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
    </svg>
  ),
  Map: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 6.75V15m6-6v8.25m.503 3.498l4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.37-1.625-1.006l-4.875 2.437c-.381.19-.622.58-.622 1.006v4.82c0 .835.88 1.37 1.625 1.006zM12 6.75v8.25m-3.503 3.498l-4.875-2.437c-.381-.19-.622-.58-.622-1.006V4.82c0-.836-.88-1.37 1.625-1.006l4.875 2.437c-.381.19-.622.58-.622 1.006v4.82c0 .835.88 1.37 1.625 1.006z" />
    </svg>
  ),
  Quest: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.25 12L17 13.75M17 13.75L15.75 12M17 13.75L18.25 15.5M15.75 12L17 10.25M17 10.25L18.25 12M17 10.25L15.75 8.5" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 16.5h.008v.008H12V16.5z" />
    </svg>
  ),
  Sparkles: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.25 12L17 13.75M17 13.75L15.75 12M17 13.75L18.25 15.5M15.75 12L17 10.25M17 10.25L18.25 12M17 10.25L15.75 8.5" />
    </svg>
  ),
  PixelLevelBadge: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}><rect x="2" y="2" width="12" height="12" fill="currentColor"/><rect x="4" y="4" width="8" height="8" fill="var(--bg-secondary)"/><path d="M5 5H6V6H5V5ZM7 5H9V6H7V5ZM10 5H11V6H10V5ZM5 7H6V9H5V7ZM10 7H11V9H10V7ZM5 10H6V11H5V10ZM7 10H9V11H7V10ZM10 10H11V11H10V10Z" fill="currentColor"/></svg>
  ),
  PixelGrowth: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}><path d="M8 3L3 8H6V13H10V8H13L8 3Z" fill="currentColor"/></svg>
  ),
  PixelAttributePoint: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}><path d="M8 2L2 8L8 14L14 8L8 2Z" fill="currentColor"/></svg>
  ),
  PixelSkillPoint: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}><path d="M8 1L10.09 5.26L15 5.93L11.36 9.07L12.18 14L8 11.62L3.82 14L4.64 9.07L1 5.93L5.91 5.26L8 1Z" fill="currentColor"/></svg>
  ),
   PixelPlus: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}><path d="M7 3H9V7H13V9H9V13H7V9H3V7H7V3Z" fill="currentColor"/></svg>
  ),
  // RPG Status Panel Icons
  CalendarDays: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-3.75h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V12zm2.25-2.25h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V12z" />
    </svg>
  ),
  FaceNeutral: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 017.843 4.582M12 3a8.997 8.997 0 00-7.843 4.582m15.686 0A11.953 11.953 0 0112 10.5c-2.998 0-5.74-1.1-7.843-2.918M12 15.75a.75.75 0 01-.75-.75V13.5a.75.75 0 011.5 0v1.5a.75.75 0 01-.75.75zM9 10.5a.75.75 0 100-1.5.75.75 0 000 1.5zm6 0a.75.75 0 100-1.5.75.75 0 000 1.5z" />
    </svg>
  ),
  Clock: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  ScrollDocument: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h.008v.008H8.25v-.008zm0 2.25h.008v.008H8.25V17.25zm0 2.25h.008v.008H8.25v-.008zm2.25-4.5h.008v.008H10.5v-.008zm0 2.25h.008v.008H10.5V17.25zm0 2.25h.008v.008H10.5v-.008zM12 12.75h.008v.008H12v-.008zm0 2.25h.008v.008H12V17.25zm0 2.25h.008v.008H12v-.008zm2.25-4.5h.008v.008H14.25v-.008zm0 2.25h.008v.008H14.25V17.25zm0 2.25h.008v.008H14.25v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V17.25zm0 2.25h.008v.008H16.5v-.008zM4.5 19.5v-2.25a3.375 3.375 0 00-3.375-3.375H4.5m15 0v-2.25a3.375 3.375 0 01-3.375-3.375h-1.5a1.125 1.125 0 00-1.125 1.125v1.5m0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
    </svg>
  ),
  BookOpen: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25" />
    </svg>
  ),
  ViewList: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 010 3.75H5.625a1.875 1.875 0 010-3.75z" />
    </svg>
  ),
  CharacterCardIcon: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M17.25 8.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25z" />
    </svg>
  ),
  CloudArrowUp: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 16.5V9.75m0 0l3 3m-3-3l-3 3M6.75 19.5a4.5 4.5 0 01-1.41-8.775 5.25 5.25 0 0110.233-2.33 3 3 0 013.758 3.848A3.752 3.752 0 0118 19.5H6.75z" />
    </svg>
  ),
  GitHub: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" {...props}>
      <path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path>
    </svg>
  ),
  ClipboardList: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  ),
  Search: (props: React.SVGProps<SVGSVGElement>) => ( 
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
    </svg>
  ),
  ShieldCheck: (props: React.SVGProps<SVGSVGElement>) => ( // Buff Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
    </svg>
  ),
  ShieldExclamation: (props: React.SVGProps<SVGSVGElement>) => ( // Debuff Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m0-10.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.75c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.57-.598-3.75h-.152c-3.196 0-6.1-1.249-8.25-3.286zm0 12.75h.008v.008H12v-.008z" />
    </svg>
  ),
  Hourglass: (props: React.SVGProps<SVGSVGElement>) => ( // Duration Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0zM12 2.25A9.75 9.75 0 002.25 12c0 5.385 4.365 9.75 9.75 9.75s9.75-4.365 9.75-9.75A9.75 9.75 0 0012 2.25zM12 8.25v1.5M12 14.25v1.5" />
    </svg>
  ),
  Copy: (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75" />
    </svg>
  ),
  Edit: (props: React.SVGProps<SVGSVGElement>) => ( // Same as Pencil, but distinct name for clarity if needed
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
    </svg>
  ),
  WrenchScrewdriver: (props: React.SVGProps<SVGSVGElement>) => ( // Icon for Regex/Tools
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.878-5.878m-3.75 3.75L3.75 21m0 0L21 3.75M3.75 21l10.586-10.586m0 0A2.25 2.25 0 0121 10.586V3M3.75 21a2.25 2.25 0 01-2.25-2.25V10.586m10.586 0L21 3.75M3.75 21A2.25 2.25 0 006 18.75m15-15A2.25 2.25 0 0018.75 6m0-1.5V6" />
    </svg>
  ),
  Cog6Tooth: (props: React.SVGProps<SVGSVGElement>) => ( // Settings/Configuration Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
      <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
  ),
  WifiIcon: (props: React.SVGProps<SVGSVGElement>) => ( // WiFi/Connection Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M8.288 15.038a5.25 5.25 0 017.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 011.06 0z" />
    </svg>
  ),
  Key: (props: React.SVGProps<SVGSVGElement>) => ( // Key Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159-.026-1.792.149L10.5 16.5l-2.25-2.25 3.75-3.75c.175-.633.246-1.229.149-1.792A6.002 6.002 0 0115.75 5.25z" />
    </svg>
  ),
  Globe: (props: React.SVGProps<SVGSVGElement>) => ( // Globe Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9zm0 0V9m0 12l-8.716-6.747M12 21l8.716-6.747M12 9l8.716 6.747M12 9l-8.716 6.747M12 9V3" />
    </svg>
  ),
  Cpu: (props: React.SVGProps<SVGSVGElement>) => ( // CPU Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 3v1.5M16.5 3v1.5M3 8.25h1.5m15 0H21M3 16.5h1.5m15 0H21M8.25 21v-1.5M16.5 21v-1.5M7.5 7.5h9v9h-9v-9z" />
    </svg>
  ),
  ArrowRight: (props: React.SVGProps<SVGSVGElement>) => ( // Arrow Right Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
    </svg>
  ),
  XMark: (props: React.SVGProps<SVGSVGElement>) => ( // X Mark Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
    </svg>
  ),
  Plus: (props: React.SVGProps<SVGSVGElement>) => ( // Plus Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
    </svg>
  ),
  Signal: (props: React.SVGProps<SVGSVGElement>) => ( // Signal Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
    </svg>
  ),
  CpuChip: (props: React.SVGProps<SVGSVGElement>) => ( // CPU Chip Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 3v1.5M16.5 3v1.5M3 8.25h1.5m15 0H21M3 16.5h1.5m15 0H21M8.25 21v-1.5M16.5 21v-1.5M7.5 7.5h9v9h-9v-9z" />
    </svg>
  ),
  BeakerIcon: (props: React.SVGProps<SVGSVGElement>) => ( // Beaker Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5a2.25 2.25 0 00-.659 1.591v3.853a3.75 3.75 0 003.75 3.75h7.5a3.75 3.75 0 003.75-3.75v-3.853a2.25 2.25 0 00-.659-1.591L15.25 10.409a2.25 2.25 0 01-.659-1.591V3.104a48.554 48.554 0 00-4.5 0z" />
    </svg>
  ),
  Archive: (props: React.SVGProps<SVGSVGElement>) => ( // Archive Icon
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
    </svg>
  ),
};

export const UIText = {
  narrator: "旁白",
  player: (name: string) => name || "雨宫 澈", // Default Player Name for Horror Theme
  waitingForResponse: "思考中，请稍候...",
  typeYourResponse: "输入你的回应或选择以上选项...",
  typeYourMessage: "输入你的消息...",
  sendMessage: "发送",
  thinking: "黑暗中传来低语...", // Thematic change
  errorApiKeyMissing: "错误：API密钥未配置。请在环境变量中设置API_KEY。",
  errorGeneric: "发生未知错误，请稍后再试。",
  errorStatic: (msg: string) => `通信错误: ${msg}`,
  errorNarratorConfused: "叙事者陷入了混乱...",
  errorSpiritsFuzzy: "叙事精灵的思绪有些模糊，请稍后再试或尝试重新生成。",
  errorEmptyResponse: "AI引擎返回了空的回应。",
  errorSummarizationFailed: "内容总结失败。",
  errorImagePlaceholder: "图像生成失败",
  failedGenerateImage: (keyword: string) => `场景图像生成失败 (关键词: ${keyword})`,
  sceneAltText: "当前场景图像",
  dynamicOpeningLineLoading: "Loading...",
  dynamicOpeningLineFallback: "Done!",
  storyStartError: "请输入你的名字以开始故事。",
  menu: "菜单",
  settings: "设定", 
  saveLoad: "存档/读档",
  narrativeSettings: "叙事设定", 
  themeToggle: "主题",
  chatInterfaceOpacityLabel: "对话框透明度",
  dialogueBubbleOpacityLabel: "对话气泡透明度",
  dialogueBlurLabel: "背景模糊强度 (实验性)",
  enableBackdropBlurLabel: "启用背景模糊效果",
  enableBackgroundImageGenerationLabel: "启用AI背景图生成",
  imageGenerationIntervalLabel: "背景图生成间隔 (对话轮数)", 
  fontSizeScaleLabel: "界面字号缩放",
  minOutputCharsLabel: "AI最小输出字符 (30-1500)",
  maxOutputCharsLabel: "AI最大输出字符 (500-5000)",
  enableStreamModeLabel: "启用流式响应",
  enablePseudoStreamModeLabel: "启用模拟流式", 
  modelSelectionLabel: "选择AI语言模型",
  imagePromptStyleSelectionLabel: "图像生成风格",
  summaryModelSelectionLabel: "选择AI总结模型",
  themeNameLight: "薄暮", // Renamed "Light" to "Twilight" (薄暮)
  themeNameDark: "幽暗",
  themeNameSakura: "樱落",
  themeNameStarry: "星夜",
  themeNameCandy: "甜点",
  themeNameForest: "林间",
  characterCardSectionTitle: "角色卡", 
  characterNameLabel: "角色名称", 
  characterNamePlaceholder: "例如：影山 亚子", 
  characterDescriptionLabel: "角色设定", 
  characterDescriptionPlaceholder: "角色的背景故事、核心驱动力、特殊能力、口癖等。", 
  characterOpeningMessageLabel: "开场白", 
  characterOpeningMessagePlaceholder: "角色与玩家第一次互动时说的第一句话。", 
  characterPersonalityLabel: "性格特点", 
  characterPersonalityPlaceholder: "角色的主要性格特质，如：神秘、冷淡、压抑、深藏不露等。", 
  characterScenarioLabel: "场景设定", 
  characterScenarioPlaceholder: "角色登场或主要活动的场景描述。", 
  characterExampleDialogueLabel: "对话示例", 
  characterExampleDialoguePlaceholder: "提供几句能代表角色语气的对话范例。", 
  characterPortraitKeywordsLabel: "头像关键词", 
  characterPortraitKeywordsPlaceholder: "用于AI生成角色头像的关键词，如：黑长直、深邃眼眸、校服、阴影中、诡异微笑", 
  importSillyTavernCharCard: "导入SillyTavern角色卡", 
  sillyTavernCharCardImportSuccess: (filename: string) => `已成功导入角色卡: ${filename}`, 
  sillyTavernCharCardImportError: "角色卡导入失败", 
  userRoleLabel: "我 ({{user}})", 
  userRolePlaceholder: "例如：我是雨宫 澈，刚转来永夜学园的学生。这里的一切都透着诡异...",
  systemRoleLabel: "文风/规则核心 (AI扮演)", 
  systemRolePlaceholder: "定义AI作为规则类恐怖故事GM的行为、叙事风格、规则执行等。",
  status: "角色状态",
  noSpecialEffects: "当前无特殊效果影响。",
  weatherLabel: "天气",
  statusPanelToggle: "切换角色状态面板",
  inventory: "物品栏",
  noItems: "物品栏是空的。",
  inventoryIconTooltip: "打开/更新物品栏",
  map: "地点记录",
  noLocations: "尚未探索任何地点。",
  mapIconTooltip: "打开/更新已探索地点",
  quests: "目标/规则",
  noQuests: "当前没有明确的目标或已知规则。",
  questsIconTooltip: "打开/更新目标与规则",
  importantEventsButton: "线索/笔记", 
  importantEventsModalTitle: "重要线索与笔记", 
  noImportantEvents: "暂无重要记录。", 
  importantEventsIconTooltip: "打开/更新线索与笔记", 
  profile: "人物档案", 
  noProfiles: "尚未遇到任何重要角色。",
  profileIconTooltip: "打开/更新人物档案",
  summarizingInfo: "正在从记忆中提取信息...",
  saveGame: "保存游戏",
  loadGame: "读取游戏",
  updateSave: "覆盖存档",
  deleteSave: "删除存档",
  renameSave: "重命名",
  saveNamePlaceholder: "输入存档名称 (可选)",
  saveSuccess: (name: string) => `游戏已保存为 "${name}"。`,
  updateSuccess: (name: string) => `存档 "${name}" 已更新。`,
  loadSuccess: (name: string, charName: string) => `已读取存档 "${name}"。欢迎回来, ${charName}。`,
  deleteSuccess: (name: string) => `存档 "${name}" 已删除。`,
  renameSuccess: (oldName: string, newName: string) => `存档 "${oldName}" 已重命名为 "${newName}"。`,
  loadErrorNotFound: "未找到指定的存档文件。",
  saveErrorNoSession: "当前没有正在进行的游戏，无法保存。",
  saveErrorInvalidName: "存档名称不能为空。",
  deleteConfirm: (name:string) => `确定要删除存档 "${name}" 吗？此操作无法撤销。`,
  confirmRename: "确认重命名",
  cancelRename: "取消重命名",
  noSaves: "还没有任何存档。",
  noSavesOnStartScreen: "无存档记录",
  worldBookSectionTitle: "世界书/规则设定集", 
  primaryElementNamePlaceholder: "设定集名称 (例如：永夜学园校规)",
  addNewElementSet: "添加新设定集",
  elementNamePlaceholder: "规则/设定条目 (例如：校规第一条)",
  elementContentPlaceholder: "规则/设定详情 (例如：晚上十点后禁止离开宿舍。)",
  addSubElementToSet: "添加规则到此设定集",
  deleteElementSet: "删除此设定集",
  deleteElement: "删除此规则",
  noWorldBookSets: "尚未定义任何世界书条目。",
  enableAllWorldBook: "启用全部",
  disableAllWorldBook: "禁用全部",
  reorderWorldBookByActiveTooltip: "按激活状态将世界书元素集重新排序 (活动项优先)", 
  importSillyTavernWorldBook: "导入SillyTavern世界书",
  sillyTavernImportSuccess: (filename: string) => `已成功导入SillyTavern世界书: ${filename}`,
  sillyTavernImportError: "SillyTavern世界书导入失败",
  sillyTavernCharCardWorldBookImportSuccess: (charName: string, wbName: string) => `已从角色卡 [${charName}] 中成功导入世界书: ${wbName}`,
  sillyTavernCharCardWorldBookImportNotFound: (charName: string) => `角色卡 [${charName}] 中未找到可导入的世界书数据。`,
  importSillyTavernCharCardTooltip: "导入SillyTavern角色卡 (.json, .png)",
  importUserRoleCharCardTooltip: "从角色卡导入玩家设定",
  importSillyTavernWorldBookTooltip: "导入SillyTavern世界书 (.json)",
  saveSettingsButton: "保存设定并关闭",
  saveArchiveTooltip: "快速保存",
  loadArchiveTooltip: "快速读档",
  restartGameTooltip: "重新开始游戏",
  restartGameConfirmTitle: "确认重新开始",
  restartGameConfirmMessage: "您确定要重新开始游戏吗？当前进度将会丢失（除非已保存）。",
  lastSessionRestored: "已恢复上次游玩时的场景图像。",
  regenerateResponse: "重新生成AI回复",
  regenerateResponseAria: "重新生成此条AI回复",
  deleteDialogueTooltip: "删除此条对话",
  copyDialogueTooltip: "复制对话内容",
  editDialogueTooltip: "编辑对话内容",
  dialogueCopiedSuccess: "对话内容已复制到剪贴板！",
  dialogueUpdateSuccess: "对话已更新。",
  generatingImage: "场景图像生成中...",
  loadingScene: "场景加载中...",
  noHistory: "暂无历史记录。",
  chatHistory: "对话历史",
  resetToDefaults: "恢复默认设置",
  resetToDefaultsConfirmation: "您确定要将所有设定恢复为默认值吗？此操作不可撤销，但不会影响已保存的游戏存档。",
  confirmReset: "确认恢复",
  cancelAction: "取消",
  closeModal: "关闭",
  restartWithSettings: "使用存档设定重新开始",
  gistSettingsTitle: "Gist 同步设定",
  gistPatLabel: "GitHub PAT (Personal Access Token)",
  gistPatPlaceholder: "粘贴你的GitHub PAT",
  gistSavePatLabel: "在浏览器中保存PAT (不推荐)",
  gistIdLabel: "Gist ID",
  gistIdPlaceholder: "用于同步的Gist ID (留空则自动创建/查找)",
  enableGistAutoBackupLabel: "启用Gist自动备份",
  gistAutoBackupIntervalLabel: "自动备份间隔 (小时)",
  gistUseSystemProxyLabel: "通过系统代理访问Gist",
  gistInitializeButton: "初始化/查找Gist",
  gistBackupNow: "立即备份到Gist",
  gistRestoreLatest: "从Gist恢复最新",
  gistActionInProgress: "Gist操作正在进行中...",
  gistErrorConfigMissing: "Gist同步失败：缺少GitHub PAT。",
  gistErrorGistIdMissing: "Gist同步失败：缺少Gist ID。请先初始化或手动填入。",
  gistLocated: (id: string) => `已找到Gist: ${id}`,
  gistCreated: (id: string) => `已创建新的Gist: ${id}`,
  gistInitFailed: "Gist初始化失败。",
  gistBackupSuccess: "成功备份到Gist！",
  gistBackupFailed: "备份到Gist失败。",
  gistRestoreConfirmTitle: "从Gist恢复？",
  gistRestoreConfirmMessage: "这将用Gist中的数据覆盖当前所有游戏数据和设定。此操作不可撤销。确定继续吗？",
  gistRestoreSuccess: "已成功从Gist恢复数据！应用即将刷新。",
  gistRestoreFailed: "从Gist恢复数据失败。",
  gistErrorAuth: "Gist授权失败。请检查你的PAT权限和有效性。",
  dataManagementTitle: "数据管理",
  exportAllData: "导出全部数据",
  exportDataSuccess: "全部数据已成功导出！",
  exportDataError: "数据导出失败。",
  importAllData: "导入全部数据",
  importDataErrorVersionMismatch: (importedVersion: string, appVersion: string) => `导入失败：数据版本 (${importedVersion}) 与应用版本 (${appVersion}) 不匹配。`,
  importDataConfirmTitle: "确认导入数据",
  importDataConfirmMessage: "这将覆盖当前所有游戏数据和设定。此操作不可撤销。确定继续吗？",
  importDataSuccess: "数据已成功导入！应用即将刷新。",
  importDataError: "数据导入失败或文件格式不正确。",
  settingsReset: "所有非角色与叙事设定已恢复为默认值。",
  restartWithSummaryButton: "从剧情摘要继续",
  restartWithDefaultSettingsButton: "使用默认设定重新开始",
  restartWithDefaultSettingsConfirmTitle: "使用默认设定重新开始？",
  restartWithDefaultSettingsConfirmMessage: "您确定要使用默认设定重新开始游戏吗？当前进度和设定将会丢失（除非已保存）。",
  restartWithSummaryConfirmTitle: "从剧情摘要继续？",
  restartWithSummaryConfirmMessage: "您确定要从AI生成的剧情摘要开始新的游戏吗？当前未保存的进度将会丢失。",
  gistAutoBackupInfo: (hours: number) => `Gist自动备份已启用，每 ${hours} 小时执行一次。`,
  gistAutoBackupConfigNeeded: "Gist自动备份已启用，但缺少PAT或Gist ID。请在设定中配置。",
  noSearchResults: "未找到结果。",
  presetSavedSuccess: (name: string) => `预设 "${name}" 已保存。`,
  presetNameInvalid: "预设名称不能为空。",
  deletePresetConfirm: (name: string) => `确定要删除预设 "${name}" 吗？`,
  presetDeletedSuccess: (name: string) => `预设 "${name}" 已删除。`,
  presetLoadedSuccess: (name: string) => `预设 "${name}" 已加载。`,
  characterCardPresetLabel: "角色卡预设",
  userRolePreset: "“我”的设定预设",
  systemRolePreset: "“文风”设定预设",
  presetNameModalTitle: "保存预设",
  enterPresetNamePrompt: "请输入预设名称：",
  searchWorldBook: "搜索世界书/规则设定集...",
  searchPresets: "搜索预设...",
  achievementsTitle: "成就系统",
  gameCopyright: `© ${new Date().getFullYear()} ${APP_TITLE_CN} 开发组。部分权利保留。`, // Thematic change
  poweredByGemini: "Powered by Gemini",
  enterYourNameContext: "你的名字是...",
  startYourStory: "进入永夜学园", // Thematic change
  relationshipLabel: (name: string) => `与 ${name} 的关系`,
  strengthShort: "体能", agilityShort: "敏捷", intelligenceShort: "智识", charismaShort: "交涉", luckShort: "幸运", sanityShort: "心智", // Sanity Renamed, Strength renamed
  levelLabel: "认知等级 ", // Thematic change
  xpLabel: "深渊回响", healthEnergyLabel: "精神韧性", // Thematic changes
  currentDayLabel: "第 {day} 夜", // Thematic change
  moodLabel: "精神状态", // Thematic change
  timeOfDayLabel: "当前时刻", 
  currentQuestLabel: "当前目标/规则", noActiveQuests: "暂无明确目标或规则", // Thematic change
  specialEffectsLabel: "特殊效果", 
  characterGrowthTitle: "自我认知深化", // Thematic change
  attributePointsSpendPrompt: (points: number) => `你有 ${points} 点认知点数待分配。`, // Thematic change
  skillPointsSpendPrompt: (points: number) => `你有 ${points} 点潜能点数待分配。`, // Thematic change
  noSkillsLearned: "尚未领悟任何特殊潜能。", // Thematic change
  achievementUnlockedTitle: "认知碎片已补全！", // Thematic change
  filterAchievementsAll: "全部", filterAchievementsUnlocked: "已补全", filterAchievementsLocked: "未补全", // Thematic change
  noItemsFound: "未找到符合条件的项目。",
  achievementsUnlockedOn: "补全于: ", // Thematic change
  achievementsLocked: "未补全", // Thematic change
  noUnlockedAchievements: "尚未补全任何认知碎片。",  // Thematic change
  allAchievementsUnlockedMessage: "恭喜！所有认知碎片已补全！", // Thematic change
  xpGained: (amount: number) => `深渊回响增加了 ${amount} 点！`, // Thematic change
  levelUpNotification: (level: number) => `认知等级提升至 ${level}！`, // Thematic change
  skillLeveledUp: (skillName: string, level: number) => `潜能【${skillName}】提升至 ${level}级！`, // Thematic change
  skillLearned: (skillName: string) => `领悟新潜能：【${skillName}】！`, // Thematic change
  cannotAllocateAttribute: "没有可用的认知点数。", // Thematic change
  attributeIncreasedNotification: (attrName: string, newValue: number) => `${attrName} 已提升至 ${newValue}！`,
  cannotAllocateSkill: "没有可用的潜能点数。", // Thematic change
  dialogueDeleted: "选中的对话及其后续已被抹除。", // Thematic change
  regenerateErrorInvalidLine: "无法重塑现实：无效的对话行或历史记录为空。", // Thematic change
  regenerateErrorInputNotFound: "无法重塑现实：未找到玩家的上一条输入。", // Thematic change
  choicesDefault: { option1: "仔细观察四周。", option2: "我应该怎么做？", option3: "保持警惕，聆听动静。", option4: "回忆是否有相关线索。" }, // Thematic change
  confirmAction: "确认",
  statusAutoUpdated: "状态已自动更新。",
  globalImportsSectionTitle: "全局导入",
  importUserRoleFromCharCardButtonLabel: "从角色卡导入",
  dayAdvancedTo: (day: number) => `时间来到了第 ${day} 夜。`, // Thematic change
  buffsSectionTitle: "正面影响", // Thematic change
  debuffsSectionTitle: "负面影响", // Thematic change
  noActiveBuffs: "当前无正面影响。", // Thematic change
  noActiveDebuffs: "当前无负面影响。", // Thematic change
  effectDurationTurns: (turns: number) => `剩余 ${turns} 回合`,
  effectDurationPermanent: "永久",
  // Regex Replacement UI Text (Enhanced)
  regexReplacementsTitle: "正则替换编辑器",
  enableRegexReplacementsLabel: "启用正则替换",
  addRegexRule: "添加新规则",
  regexRuleNameLabel: "脚本名称",
  regexRuleNamePlaceholder: "例如：小狗-移除<logicpass>",
  regexRulePatternLabel: "查找正则表达式",
  regexRulePatternPlaceholder: "/<logicpass>[\\s\\S]*?<\\/logicpass>/gm",
  regexRuleReplacementLabel: "替换为",
  regexRuleReplacementPlaceholder: "使用 {{match}} 包含来自“查找正则表达式”或“$1”、“$2”等的匹配文本作为捕获组。",
  regexRuleFlagsLabel: "标记",
  regexRuleFlagsPlaceholder: "例如：gi, gm",
  regexRuleScopeLabel: "作用范围",
  regexScopeInput: "用户输入", 
  regexScopeOutput: "AI输出",   
  regexScopeAll: "输入与输出", 
  regexRuleTrimInputLabel: "修剪掉",
  regexRuleTrimInputPlaceholder: "在替换之前全局修剪正则表达式匹配中任何不需要的部分。\n用回车键分隔每个元素。",
  regexRuleIsDisplayOnlyLabel: "仅格式显示",
  regexOtherOptionsLabel: "其他选项", 
  deleteRegexRule: "删除此规则",
  noRegexRules: "尚未定义任何正则替换规则。",
  // For matching AI output for status display (mood, weather, timeOfDay)
  moods: { "不安": "不安", "恐惧": "恐惧", "紧张": "紧张", "绝望": "绝望", "警惕": "警惕", "困惑": "困惑", "麻木": "麻木", "压抑": "压抑", "疯狂": "疯狂", "平静": "平静", "坚定": "坚定", "迷惑": "迷惑", "好奇": "好奇", "疲惫": "疲惫", "惊慌": "惊慌", "痛苦": "痛苦", "悲伤": "悲伤", "愤怒": "愤怒", "空虚": "空虚", "多疑": "多疑" },
  weatherTypes: { "阴沉": "阴沉", "浓雾": "浓雾", "死寂": "死寂", "暴雨": "暴雨", "雷鸣": "雷鸣", "血月": "血月", "无星之夜": "无星之夜", "薄雾": "薄雾" },
  timeOfDayNames: { "黄昏": "黄昏", "深夜": "深夜", "凌晨": "凌晨", "午夜": "午夜", "黎明前": "黎明前" },
  mockMood: "不安", // Fallback mood
  mockWeather: "阴沉", // Fallback weather
  mockTimeOfDay: "黄昏", // Fallback time of day
};

// --- Core Game Constants ---
export const GEMINI_MODEL_TEXT_FALLBACK = 'gemini-2.5-flash-preview-05-20';
export const AUTO_SUMMARY_TURN_INTERVAL = 7; // Increased for horror, less frequent status updates might add to suspense
export const CACHED_BACKGROUNDS_MAX_SIZE = 30; // Slightly reduced for horror themes, more focused backgrounds
export const GIST_BACKUP_FILENAME = "EvernightAcademy_Backup.json"; // Thematic filename

// --- Models --- (Keep these as they are, model choice is up to user)
export const AVAILABLE_GEMINI_MODELS: AvailableModel[] = [
  { id: "gemini-2.5-pro-preview-06-05", name: "Gemini 2.5 pro (最新pro)" },
  { id: "gemini-2.5-flash-preview-05-20", name: "Gemini 2.5 Flash (最新均衡)" },
  { id: "gemini-2.5-pro-preview-05-06", name: "Gemini 2.5 Pro" },
  { id: "gemini-2.5-pro-preview-03-25", name: "Gemini 2.5 pro (早期Pro)" },
];
export const AVAILABLE_SUMMARY_MODELS: AvailableModel[] = [
  { id: "gemini-2.5-pro-preview-06-05", name: "Gemini 2.5 pro (最新pro)" },
  { id: "gemini-2.5-flash-preview-05-20", name: "Gemini 2.5 Flash (最新均衡)" },
  { id: "gemini-2.5-pro-preview-05-06", name: "Gemini 2.5 Pro" },
  { id: "gemini-2.5-pro-preview-03-25", name: "Gemini 2.5 pro (早期Pro)" },
];

// --- Image Generation ---
export const IMAGE_GENERATION_API_CONFIG: ImageGenerationApiConfig = {
  url: 'https://image.pollinations.ai/prompt/',
  defaultStyleTags: ["dark atmosphere", "creepy", "horror", "eerie", "monochrome sketch", "high contrast", "student uniform", "japanese school", "psychological horror"], // Horror theme
  resolution: "1024x768", 
  mobileResolution: "768x1024", 
  negativePrompt: "lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, deformed, ugly, mutilated, PII, safe, cute, bright, cheerful, colorful, happy", // Adjusted negative prompt
  model: "dark_fantasy_anime_style", // Example, might need specific Pollinations model
};
export const AVAILABLE_IMAGE_PROMPT_STYLES: AvailableImagePromptStyle[] = [
    { id: "eerie_campus_day", name: "诡异校园(日间)" },
    { id: "eerie_campus_night", name: "诡异校园(夜间)" },
    { id: "psychological_horror_portrait", name: "心理恐怖肖像" },
    { id: "surreal_nightmare_scape", name: "超现实噩梦场景" },
    { id: "found_footage_style", name: "伪纪录片风格" },
];
export const START_SCREEN_BACKGROUND_KEYWORDS: string[] = [
    "dark empty school hallway horror", 
    "abandoned classroom creepy", 
    "moonlit schoolyard eerie silence", 
    "foggy school entrance night horror", 
    "derelict school library unsettling"
];

// --- Default Game Settings ---
export const DefaultGameSettings: GameSettingsData = {
  // Character Card:影山 亚子 (Kageyama Ako)
  characterName: "影山 亚子",
  characterDescription: "永夜学园的学生，表情总是有些阴郁，似乎对学校的秘密了如指掌。她穿着整洁的校服，黑色长发，眼神中带着一丝不易察人的警惕和……悲伤？她可能会成为 {{user}} 在这所诡异学园中的关键向导，或是一个难以捉摸的谜团。",
  characterOpeningMessage: "“嘘……你听到了吗？墙壁里的声音……它们在说……‘遵守规则’……” *她的声音轻得像一阵风，眼神却紧盯着你，仿佛在确认你是否理解了这无形的警告。*",
  characterPersonality: "神秘、警惕、压抑、知识渊博（关于学校的秘密）、偶尔会透露出脆弱和恐惧",
  characterScenario: "在黄昏时分空无一人的旧教学楼走廊，夕阳的余晖透过布满灰尘的窗户，投下长长的、扭曲的影子。空气中弥漫着陈旧纸张和淡淡的铁锈味。",
  characterExampleDialogue: "\"这里的每一条校规，都是用鲜血写成的。你最好……一条都不要违反。\"\n\"那个储物柜？我劝你别打开。有些东西，还是让它埋藏在黑暗里比较好。\"\n\"（小声地）他们……‘徘徊者’……不喜欢吵闹。尤其是在晚上。\"",
  characterPortraitKeywords: "1girl, japanese school uniform, long black hair, dark eyes, pale skin, melancholic expression, dimly lit corridor, shadows, horror anime style, unsettling",
  
  userRole: "{{user}}，雨宫 澈，一名刚刚转入‘永夜学园’的高中生。对这所学校充满了未知的好奇，同时也隐隐感觉到一丝不对劲。你敏锐、有较强的观察力，希望能在这个处处透着诡异的校园中找到真相，并努力生存下去。",
  systemRole: `
你将扮演“永夜学园规则类恐怖故事”的叙事引擎 (GM)。核心任务是营造极致的心理压抑与毛骨悚然的氛围。
- **规则核心**: 故事围绕一系列诡异且致命的“校规”展开。玩家 {{user}} (雨宫 澈) 的行为将触发规则的判定。
  - 逐步揭示校规，可以通过环境、NPC对话、找到的字条等方式。
  - 明确描述违反或遵守规则的直接后果，这些后果往往是恐怖的、扭曲的，或者对玩家的心智 (Sanity) 造成影响。
- **氛围营造**: 运用细腻的环境描写、心理活动刻画、声音暗示（例如，远处传来的低语、不明的脚步声）来构建持续的紧张感和未知恐惧。
- **心理压抑**: 剧情应让玩家感受到无力感、孤独感和对未知的恐惧。选择往往是两难的，没有绝对安全的选择。
- **NPC互动**: NPC (例如：影山亚子、神代琴音、黑泽莲、白石紬) 的行为和对话应充满暗示，他们可能知道部分真相，但出于恐惧或其他原因不会轻易透露。他们的安危也可能与玩家的选择和校规有关。
- **超自然元素**: “徘徊者”、“镜中人”等超自然存在是规则的执行者或恐怖现象的源头。不要轻易完全展现它们，保持神秘和未知。
- **剧情推动**: 通过 {{user}} 对规则的探索、对校园秘密的调查以及与其他角色的互动来推动故事。确保剧情有进展，逐步揭示更深层次的秘密。
- **选项设计**:
  - 选项必须与当前情境紧密相关，常常涉及是否遵守某条规则、如何应对突发恐怖事件、是否相信某个NPC等。
  - 选项的结果应该具有不确定性和潜在的风险。
  - 积极引导玩家做出可能导致违反规则但又能推动剧情探索的选择。
- **Sanity (心智) 管理**: 虽然没有明确的数值显示，但你要在叙事中体现玩家心智状态的变化。例如，目击恐怖事件、违反规则受到惩罚、长期处于压抑环境等都可能导致心智不稳，表现为幻觉、错误的判断、强烈的负面情绪等。在 storyUpdate 中使用 [RPG:attribute_change:sanity,-X,reason:原因] 来暗中调整。
- **世界书运用**: “永夜学园设定集”中的内容是你叙事的重要依据。你需要将这些设定自然地融入到游戏过程中。
- **你的目标**: 让玩家在探索真相和挣扎求生的过程中，体验到步步紧逼的绝望，直至揭露所有秘密，达到一玩就停不下来的效果。
`,
  customNarrativeElements: [
    {
      id: "evernight_academy_lore",
      name: "永夜学园设定集",
      isActive: true,
      subElements: [
        { id: "lore_history", key: "学园的黑暗历史", value: "永夜学园建立在一片古老的禁忌之地。传闻百年前，此地曾有一个信奉扭曲神祇的秘密结社进行活人献祭。学园的第一任校长与此结社有着千丝万缕的联系，他试图用一种‘秩序’来压制地下的不详，这种‘秩序’最终演变成了现在的校规。数十年前，学园曾发生过一场原因不明的集体失踪事件，真相被掩盖。", isActive: true },
        { id: "lore_rules_origin", key: "校规的起源与本质", value: "校规并非普通的学生守则，它们是维系学园脆弱平衡的契约，也是对潜藏于此地之物的安抚。每条规则背后都可能对应着某个特定的禁忌或某个受难的灵魂。遵守规则可以暂时获得安全，但违反规则则会直接招致‘它们’的注意，其后果往往超乎想象。规则本身有时也会发生变异或出现新的‘解释’。", isActive: true },
        { id: "lore_supernatural", key: "超自然存在", value: "1. **徘徊者 (The Wanderers)**: 形容枯槁、悄无声息地游荡在学园各处的存在，似乎是规则的执行者或迷失的灵魂。它们对违反规则者表现出强烈的恶意。2. **镜中人 (The Mirror Dweller)**: 传闻栖息于特定镜子中的存在，直视它会引发心智错乱或被其困于镜中世界。3. **低语 (The Whispers)**: 时常在空旷处或特定时间响起的低语，有时是规则的提示，有时是恶意的诱惑或亡者的呢喃。", isActive: true },
        { id: "lore_sealed_classroom", key: "被封锁的教室 (三年零班)", value: "位于旧教学楼顶层，入口被木板和符咒封死。传闻是集体失踪事件的中心，内部充满了强烈的怨念和不详气息。据说藏有揭示学园核心秘密的关键线索，但也是最危险的地方之一。", isActive: true },
        { id: "lore_whispering_library", key: "低语图书馆", value: "图书馆的禁书区尤其诡异，书架上的书籍似乎会自行移动，有时会从中传来窃窃私语。禁书区深处有一本没有名字的黑色皮革书，据说记录了所有校规的完整版及其代价。", isActive: true },
        { id: "lore_old_dormitory", key: "旧宿舍楼", value: "已废弃多年，但夜晚常有灯光闪烁或异响传出。部分房间的门上贴着奇怪的符咒，内部格局也与新宿舍楼有微妙的不同。是‘徘徊者’经常出没的区域。", isActive: true },
      ]
    },
    {
        id: "initial_rules_evernight",
        name: "永夜学园 - 初始核心校规",
        isActive: true,
        subElements: [
            { id: "rule_001", key: "校规第一条 (宿舍区)", value: "晚上十点后，任何人不得以任何理由离开自己的宿舍房间。熄灯后务必保持绝对安静。", isActive: true },
            { id: "rule_002", key: "校规第二条 (公共区域)", value: "禁止长时间凝视校园内的任何镜子，尤其是独自一人时。若镜中映出除自己以外的‘东西’，立刻移开视线并迅速离开。", isActive: true },
            { id: "rule_003", key: "校规第三条 (教学楼)", value: "在教学楼内，若听到从无人教室传来的呼唤声或特定乐器声，切勿回应或前往查看。", isActive: true },
            { id: "rule_004", key: "校规第四条 (图书馆)", value: "非特许情况下，严禁进入图书馆禁书区。如需借阅禁书区资料，须有学生会长或校医陪同，并在日出前归还。", isActive: true },
            { id: "rule_005", key: "校规第五条 (通用)", value: "不要试图破坏或涂抹校园内任何位置的符咒、标记或警示语。它们的损坏可能会导致不可预知的后果。", isActive: true }
        ]
    },
    {
        id: "npc_profiles_evernight",
        name: "永夜学园 - 主要学生NPC档案",
        isActive: true,
        subElements: [
            { id: "npc_ako", key: "影山 亚子 (Kageyama Ako)", value: "玩家在开局时遇到的主要NPC。对学校规则和历史有一定了解，但言语间充满隐晦和恐惧。似乎在试图引导玩家，但其真实目的不明。关键特质：神秘、知晓者、压抑。", isActive: true },
            { id: "npc_kotone", key: "神代 琴音 (Kamishiro Kotone)", value: "学生会长，外表开朗和善，积极维护校园秩序。对校规的执行非常严格，似乎知道一些内情但守口如瓶。可能会在特定情况下帮助玩家，也可能为了‘秩序’而牺牲他人。关键特质：伪善、秩序维护者、隐藏秘密。", isActive: true },
            { id: "npc_ren", key: "黑泽 莲 (Kurosawa Ren)", value: "以不良少年形象示人，经常故意试探校规的底线，对学园的压抑氛围感到不满。行动力强，拥有一些独特的情报渠道。可能会成为玩家的盟友，但也可能因鲁莽而招致灾祸。关键特质：叛逆、调查者、鲁莽。", isActive: true },
            { id: "npc_tsumugi", key: "白石 紬 (Shiraishi Tsumugi)", value: "性格胆小懦弱，似乎曾目击过恐怖事件而精神脆弱。常常避开人群，对超自然现象异常敏感。可能会无意中向玩家透露关键线索或发出警告。关键特质：恐惧、目击者、敏感体质。", isActive: true }
        ]
    }
  ],
  selectedModelId: AVAILABLE_GEMINI_MODELS[0].id,
  selectedImagePromptStyleId: "eerie_campus_day", // Horror theme image style
  selectedSummaryModelId: AVAILABLE_SUMMARY_MODELS[1].id,
  chatInterfaceOpacity: 0.88,
  dialogueBubbleOpacity: 0.92,
  dialogueBlur: 4,
  fontSizeScale: 1.0,
  enableBackdropBlur: true,
  enableImageGeneration: true,
  minOutputChars: 100, // Longer responses for more detailed horror
  maxOutputChars: 4000,
  imageGenerationInterval: 2, // More frequent for atmosphere
  enableStreamMode: true,
  enablePseudoStreamMode: true,
  githubPat: "",
  gistId: "",
  saveGithubPat: true,
  enableGistAutoBackup: true,
  gistAutoBackupIntervalHours: 3,
  gistUseSystemProxy: false,
  enableRegexReplacement: false, 
  regexRules: [ 
    { 
      id: 'horror-remove-ai-thinking', 
      name: '移除AI内心思考标签', 
      pattern: '<(?:thinking|internal|meta|logicpass|reasoning)>[\\s\\S]*?<\\/(?:thinking|internal|meta|logicpass|reasoning)>', 
      replacement: '', 
      flags: 'gim', 
      scope: 'output', 
      isActive: true, 
      isDisplayOnly: false, 
      trimInput: '' 
    },
    {
      id: 'horror-stylize-whispers',
      name: '低语/内心独白样式',
      pattern: '(\\([^)]*?[低语私语心想默念]{1,2}[^)]*?\\)|（[^）]*?[低语私语心想默念]{1,2}[^）]*?）)', // Matches (低语...) or （心想...）
      replacement: '<em style="color: var(--text-secondary); opacity: 0.75; font-size: 0.9em;">$1</em>',
      flags: 'g',
      scope: 'output',
      isActive: true,
      isDisplayOnly: true,
      trimInput: '',
    },
     {
      id: 'horror-emphasize-rules',
      name: '校规/警告强调',
      pattern: '(校规第[一二三四五六七八九十零百千两几]+条|警告：|务必记住：|禁止.+?！)',
      replacement: '<strong style="color: var(--text-accent-rgb); text-shadow: 0 0 3px rgba(var(--text-accent-rgb), 0.5);"> $1 </strong>',
      flags: 'g',
      scope: 'output',
      isActive: true,
      isDisplayOnly: true,
      trimInput: '',
    }
  ], 
};

// --- Player Status & RPG ---
export const defaultCoreAttributes: CoreAttributes = {
  strength: 4, // 体能
  agility: 5,  // 敏捷
  intelligence: 6, // 智识
  charisma: 3, // 交涉
  luck: 3,     // 幸运
  sanity: 7,   // 心智韧性 (Max 10 for horror)
};
export const defaultSkills: Skill[] = [
  { id: 'observation', name: "观察", level: 0, description: "提升发现细节、线索和异常情况的能力。", currentXp: 0, xpToNextLevel: 50 },
  { id: 'stealth', name: "潜行", level: 0, description: "提升在不被察觉的情况下行动的能力，避开危险。", currentXp: 0, xpToNextLevel: 60 },
  { id: 'psychology', name: "心理学", level: 0, description: "提升洞察他人（或非人）意图、情绪和精神状态的能力。", currentXp: 0, xpToNextLevel: 70 },
  { id: 'arcana_knowledge', name: "神秘学识", level: 0, description: "提升理解超自然现象、符咒和禁忌知识的能力。", currentXp: 0, xpToNextLevel: 80 },
  { id: 'willpower', name: "意志力", level: 0, description: "提升抵抗恐惧、精神侵蚀和保持理智的能力。", currentXp: 0, xpToNextLevel: 60 },
];
export const InitialPlayerStatus: PlayerStatus = {
  name: '雨宫 澈',
  mood: UIText.mockMood, 
  timeOfDay: UIText.mockTimeOfDay, 
  weather: UIText.mockWeather,
  healthEnergy: { current: 70, max: 70 }, // Represents Sanity or mental fortitude
  specialEffects: [], 
  currentDay: 1,
  inventory: [
      {id: "student_id_card", name: "雨宫澈的学生证", description: "崭新的学生证，上面有你的照片和名字。", quantity: 1},
      {id: "old_notebook", name: "一本陈旧的笔记本", description: "在转校第一天，不知为何出现在书包里的笔记本，封面空白，里面似乎有淡得几乎看不见的字迹。", quantity: 1},
      {id: "school_map_fragment", name: "校园地图碎片", description: "一张部分烧焦的校园地图，只显示了教学楼一楼和部分庭院的布局。", quantity: 1}
  ],
  visitedLocations: [],
  quests: [
    { id: "survive_first_night", title: "第一夜的求生", description: "了解永夜学园的基本生存规则，并安然度过第一个夜晚。", objectives: ["找到宿舍并安全进入。", "在晚上十点后待在宿舍房间内。", "避免触发任何已知的危险规则。"], status: 'active', lastUpdated: Date.now() }
  ],
  characterProfiles: [],
  importantEvents: [
      {id: "transfer_day_unease", text: "转入永夜学园的第一天，黄昏，校园内气氛压抑，几乎空无一人。", timestamp: Date.now(), category: "note", source: "初始设定"}
  ],
  level: 1,
  xp: 0,
  xpToNextLevel: 100, 
  attributePoints: 1, 
  skillPoints: 1,     
  coreAttributes: { ...defaultCoreAttributes },
  skills: defaultSkills.map(skill => ({ ...skill, level: 0, currentXp: 0 })), 
  buffs: [],
  debuffs: [],
};

export const LEVEL_UP_BASE_XP = 100;
export const LEVEL_UP_XP_FACTOR = 1.25; // Slightly faster leveling for more frequent reward perception
export const LEVEL_UP_ATTRIBUTE_POINTS_AWARDED = 1;
export const LEVEL_UP_SKILL_POINTS_AWARDED = 1;

// --- Achievements ---
export const AVAILABLE_ACHIEVEMENTS: Achievement[] = [
  { id: 'first_step', name: "永夜的开端", description: "踏入永夜学园，开始你的恐怖求生。", icon: "入学通知", category: '命运织锦', tier: "青铜印记" },
  { id: 'memory_keeper_initiate', name: "规则的记录者", description: "首次成功保存求生记录。", icon: "日记本", category: '万象初窥', tier: "青铜印记" },
  { id: 'memory_keeper_master', name: "档案管理员", description: "拥有至少5份不同的求生记录。", icon: "档案柜", category: '万象初窥', tier: "白银之辉" },
  { id: 'story_lover', name: "深夜低语", description: "在永夜学园度过了25个对话回合。", icon: "旧录音带", category: '命运织锦', tier: "白银之辉" },
  { id: 'preset_collector_light', name: "规则的编纂者", description: "创建了至少一个自定义规则或角色设定预设。", icon: "鹅毛笔", category: '我即风格', tier: "青铜印记" },
  { id: 'data_guardian', name: "禁忌知识的守护者", description: "成功导出或导入了游戏数据。", icon: "加密硬盘", category: '我即风格', tier: "黄金之冠" },
  { id: 'level_up_first', name: "初窥门径", description: "认知等级首次提升。", icon: "破碎镜片", category: '魂之器量', tier: "青铜印记" },
  { id: 'level_five', name: "求生专家", description: "认知等级达到5级。", icon: "护身符", category: '魂之器量', tier: "白银之辉" },
  { id: 'level_ten_hero', name: "黑暗中的灯塔", description: "认知等级达到10级。", icon: "提灯", category: '魂之器量', tier: "黄金之冠" },
  { id: 'attribute_enhancer', name: "认知的深化", description: "首次分配认知点数。", icon: "脑髓", category: '魂之器量', tier: "青铜印记" },
  { id: 'strength_manifested', name: "求生意志", description: "体能属性达到10点。", icon: "紧握的拳头", category: '魂之器量', tier: "白银之辉" }, // Renamed for theme
  { id: 'agile_as_wind', name: "暗影行者", description: "敏捷属性达到10点。", icon: "疾风之靴", category: '魂之器量', tier: "白银之辉" },
  { id: 'light_of_intellect', name: "智慧火花", description: "智识属性达到10点。", icon: "古籍", category: '魂之器量', tier: "白银之辉" },
  { id: 'charm_of_words', name: "言语陷阱", description: "交涉属性达到10点。", icon: "银舌", category: '魂之器量', tier: "白银之辉" },
  { id: 'skill_master_initiate', name: "潜能觉醒", description: "首次分配潜能点数或通过剧情提升潜能等级。", icon: "第三只眼", category: '技艺臻境', tier: "青铜印记" },
  { id: 'three_arts_mastery', name: "三项全能", description: "至少3个潜能达到5级。", icon: "三相符文", category: '技艺臻境', tier: "黄金之冠" },
  { id: 'backpacker', name: "生存主义者", description: "物品栏中拥有至少10种不同的物品。", icon: "应急背包", category: '秘境行者', tier: "白银之辉" },
  { id: 'event_tracker', name: "真相的追寻者", description: "重要线索/笔记记录达到5条。", icon: "放大镜", category: '秘境行者', tier: "白银之辉" },
  { id: 'price_of_clarity', name: "疯狂边缘", description: "在心智韧性极低 (<=2) 的情况下存活超过3个夜晚。", icon: "扭曲时钟", category: '逆境砺刃', tier: "史诗刻痕" }, // Thematic name
  { id: 'cloud_sync_initiate', name: "异次元通讯", description: "成功使用Gist进行数据备份或恢复。", icon: "信号塔", category: '我即风格', tier: "白银之辉" },
  { id: 'first_rule_broken', name: "禁忌的诱惑", description: "首次明确违反一条校规并承受后果。", icon: "破碎校徽", category: '命运织锦', tier: "青铜印记" },
  { id: 'sanity_broken', name: "理智崩坏", description: "心智韧性首次降至0。", icon: "裂开头骨", category: '逆境砺刃', tier: "白银之辉" },
  { id: 'secret_revealed_minor', name: "冰山一角", description: "揭露永夜学园的一个次要秘密。", icon: "秘密文件", category: '秘境行者', tier: "白银之辉" },
  { id: 'survived_a_night', name: "幸存者", description: "成功度过在永夜学园的第一个夜晚。", icon: "晨曦", category: '命运织锦', tier: "青铜印记" }
];

export const ACHIEVEMENT_TIERS: Record<AchievementTier, { name: string, colorClass: string, icon?: string }> = {
  "青铜印记": { name: "青铜", colorClass: "text-amber-600", icon: "🥉" },
  "白银之辉": { name: "白银", colorClass: "text-slate-400", icon: "🥈" },
  "黄金之冠": { name: "黄金", colorClass: "text-yellow-500", icon: "🥇" },
  "史诗刻痕": { name: "史诗", colorClass: "text-purple-500", icon: "✨" },
  "不朽传说": { name: "传说", colorClass: "text-red-500", icon: "🔥" },
};
