This document outlines a series of meticulously refined and comprehensively detailed enhancements for the application, categorized into three distinct user stories. The objective is to elevate code quality, optimize UI/UX, and ensure a more cohesive and intuitive user experience.

**Story 1: Centralized and Atomic Player Status Updates**

**Current State Analysis:**
Player status information is currently fragmented, with session-specific data (e.g., mood, timeOfDay, weather) managed by `useGameSession` and RPG-specific data (e.g., stats, inventory, skills) handled by `useRPGSystem`. `App.tsx` acts as an integrator, combining these into a `compositePlayerStatus`. This distributed management can lead to an indirect data flow, particularly when processing a single AI turn's updates. The `useGameSession` hook directly manipulates session-specific status while also triggering `processStoryUpdateForRPG` in `useRPGSystem` for RPG-related updates.

**Goal Definition:**
To establish a single, atomic update mechanism for all player status changes originating from an AI response. This involves centralizing the application of these changes within `App.tsx`, making it the definitive single source of truth for `compositePlayerStatus`. Furthermore, this refinement aims to clarify the responsibilities of `useGameSession` as an orchestrator of AI interactions and `useRPGSystem` as a provider of pure RPG logic.

**Proposed Modifications:**

1.  **`App.tsx` as the Single Source of Truth for `compositePlayerStatus`:**
    *   `App.tsx` will continue to hold the `compositePlayerStatus` state.
    *   The `useRPGSystem` hook will be initialized with and operate on the RPG-specific portion of this `compositePlayerStatus`.
    *   The `useGameSession` hook will receive a new callback from `App.tsx`, for instance, `handleProcessAiResponseAndUpdateStatus(geminiResponse: GeminiResponseFormat)`.

2.  **`useGameSession` as an Orchestrator:**
    *   When `useGameSession` processes an AI response (via `sendMessageToGemini` and `parseGeminiResponse`), it will invoke the new callback (`handleProcessAiResponseAndUpdateStatus`) passed from `App.tsx`, providing the full `GeminiResponseFormat`.
    *   The `onPlayerStatusSessionChange` callback and direct local state management for `mood`, `timeOfDay`, and `weather` within `useGameSession` will be removed, as these will now be managed centrally by `App.tsx`.

3.  **`App.tsx` Handles Atomic Updates:**
    *   The `handleProcessAiResponseAndUpdateStatus` function within `App.tsx` will perform the following actions:
        *   Extract `storyUpdate`, `mood`, `timeOfDay`, and `weather` (if applicable) from the `geminiResponse`.
        *   Call `rpgSystem.processStoryUpdateRPG(storyUpdateText, playerStatusRPG.timeOfDay)` (from `useRPGSystem`), passing the current RPG status part of `compositePlayerStatus` and the AI's determined time of day (as RPG effects might depend on it).
        *   `processStoryUpdateRPG` will return an `RPGProcessingResult` (containing changes to `PlayerStatus_RPG`, notifications, and achievements).
        *   `App.tsx` will then update `compositePlayerStatus` in a single `setState` call, merging:
            *   The `statusChanges` from `RPGProcessingResult` into the RPG part of `compositePlayerStatus`.
            *   The `mood`, `timeOfDay`, and `weather` from `GeminiResponseFormat` into the session-specific part of `compositePlayerStatus`.
        *   Finally, it will process any `notifications` and `achievements` returned by `processStoryUpdateRPG`.

4.  **`useRPGSystem` Focuses on Pure RPG Logic:**
    *   `processStoryUpdateRPG` will be modified to be a pure function. It will take the current `PlayerStatus_RPG` and the `storyUpdate` string (and potentially `currentTimeOfDay`) as input and return the `RPGProcessingResult` containing the changes or delta to the RPG status, rather than setting state directly within the hook.
    *   Similarly, `allocateAttributePointRPG` and `allocateSkillPointRPG` will compute and return the new `PlayerStatus_RPG` state or the delta to be applied by `App.tsx`.

5.  **Simplified Props for `RPGStatusPanel` and `ChatStatusHeader`:**
    *   The `RPGStatusPanel` (or `PersistentStatusDisplay`) will now receive `playerStatusRPG` (the RPG part of `compositePlayerStatus`) and the session-specific `currentMood`, `currentTimeOfDay`, `currentWeather` directly from `App.tsx`.
    *   `ChatStatusHeader` will also receive its necessary `playerStatusSession` (now derived from `compositePlayerStatus`) and `playerStatusRPG` from `App.tsx`.

**Affected Files:**
*   `src/App.tsx`
*   `src/hooks/useGameSession.ts`
*   `src/rpgSystem/hooks/useRPGSystem.ts`
*   `src/types.ts` (ensuring `PlayerStatus_Session` and `PlayerStatus` composition is accurate)
*   `src/components/ChatStatusHeader.tsx`
*   `src/rpgSystem/components/RPGStatusPanel.tsx`

**Benefits:**
This architectural refinement ensures that all status modifications resulting from a single AI turn are processed and applied together, significantly reducing potential inconsistencies and race conditions. It clarifies `App.tsx`'s role as the definitive owner of the complete player state and makes `useGameSession` and `useRPGSystem` more focused on their respective domains (session management and pure RPG rule processing), leading to a more maintainable and predictable data flow.

---

**Story 2: UI/UX Enhancements for Global Imports and Advanced Settings**

**Current State Analysis:**
The "Global Imports" feature in the header uses a generic `ArrowUpTray` icon, which may not clearly convey its purpose for importing diverse file types (Character Cards, World Books). The Regex Editor, while powerful, lacks sufficient guidance and context, making it less user-friendly for individuals unfamiliar with regular expressions.

**Goal Definition:**
To significantly improve the discoverability and intuitiveness of the Global Import feature. To enhance the Regex Editor's user experience by providing clearer guidance, descriptive fields, and potentially immediate feedback mechanisms, thereby making this advanced functionality more accessible.

**Proposed Modifications:**

1.  **Global Imports Dropdown (in `App.tsx` and related components):**
    *   **Clearer Trigger Icon:** In `App.tsx`, replace the `Icons.ArrowUpTray` for the Global Imports Dropdown with a more specific icon like `Icons.ClipboardList` (representing structured data or configuration import). Alternatively, a custom `FolderImport` icon could be designed for bespoke clarity.
    *   **Descriptive Dropdown Items:** In `App.tsx`, modify the `globalImportDropdownItems` array. For each item's `label`, utilize a `React.Fragment` to include the primary label and a smaller, secondary line of text indicating accepted file types.
        ```tsx
        // Example for globalImportDropdownItems in App.tsx
        const globalImportDropdownItems = [
            { 
                id: 'importCharCard', 
                label: (
                    <>
                        {UIText.importSillyTavernCharCard}
                        <small className="block text-xs text-secondary-themed/70 opacity-80 mt-0.5">(.json, .png, .jsonc)</small>
                    </>
                ), 
                icon: () => <span role="img" aria-label="Character Card">🃏</span>, 
                onClick: () => importCharCardFileRef.current?.click() 
            },
            // ... other items following this pattern
        ];
        ```
    *   The `Dropdown` component (`src/components/Dropdown.tsx`) might require minor style adjustments to gracefully accommodate these two-line items.

2.  **Regex Editor Enhancements (in `SettingsMenu.tsx`):**
    *   **Rule Description Field:**
        *   Add an optional `description?: string;` field to the `RegexRule` type in `src/types.ts`.
        *   Update `ensureRegexRulesStructure` in `src/hooks/useGameSettings.ts` to initialize `description: typeof rule.description === 'string' ? rule.description : '',`.
        *   In `SettingsMenu.tsx`, within the regex rule rendering logic, add a `textarea` for this `rule.description`. This `textarea` will allow users to add notes about what each rule does and should be placed logically within the rule's collapsible section (e.g., below the rule name or pattern).
    *   **Improved Placeholders and Guidance:**
        *   For the "Flags" input, add a tooltip (e.g., triggered by an info icon `Icons.InformationCircle` next to the label) explaining common flags: `g` (global), `i` (case-insensitive), `m` (multiline), `s` (dotall).
        *   For the "Scope" select, provide a similar tooltip explaining that "Input" refers to the player's messages before sending to AI, "Output" to AI's messages before display, and "All" to both.
    *   **"Trim Input" Field Clarity:**
        *   The existing `textarea` for "Trim Input" is suitable for multiline entries. Ensure its `placeholder` clearly states that each line represents a separate string to be trimmed from the matched text before replacement.
        ```html
        <textarea placeholder="例: <thought> 或 </thought>\n在替换前，从匹配到的文本中移除此处列出的每个字符串（每行一个）。"></textarea>
        ```

**Affected Files:**
*   `src/App.tsx`
*   `src/components/Dropdown.tsx`
*   `src/types.ts`
*   `src/hooks/useGameSettings.ts`
*   `src/components/SettingsMenu.tsx`
*   `src/constants.tsx` (for new `UIText` strings if required for tooltips)

**Benefits:**
A more descriptive icon and clearer labels in the Global Import dropdown will significantly improve its discoverability and user comprehension. The enhancements to the Regex Editor will make this powerful, advanced feature substantially more accessible and manageable for all users, reducing potential errors and frustration.

---

**Story 3: Polishing RPG Panel Interactions and Visual Feedback**

**Current State Analysis:**
The `RPGStatusPanel` (rendered as `PersistentStatusDisplay` in `App.tsx`) effectively displays RPG information. However, the visual feedback upon allocating attribute or skill points is minimal, potentially leading to a less engaging user experience. The display of buffs and debuffs could also benefit from clearer visual distinctions.

**Goal Definition:**
To make the RPG status panel more visually dynamic and provide immediate, clear feedback upon point allocation. To enhance the presentation of buffs and debuffs for improved at-a-glance comprehension of character status.

**Proposed Modifications:**

1.  **Point Allocation Feedback in `RPGStatusPanel.tsx`:**
    *   **Highlighting:** When an attribute or skill point is successfully allocated (i.e., the `onAllocateAttribute` or `onAllocateSkill` props are called and the state in `App.tsx` updates), the specific row (`AttributeRowRPG` or `SkillRowRPG`) that was modified should briefly highlight.
        *   **Implementation Idea:** `RPGStatusPanel` could maintain a local state, e.g., `lastAllocatedItemId: string | null` and `highlightType: 'attribute' | 'skill' | null`.
        *   When `onAllocateAttribute` or `onAllocateSkill` is about to be called (or just after, if the parent `App.tsx` signals success via a new prop), set this `lastAllocatedItemId` and `highlightType`.
        *   Pass `lastAllocatedItemId` and `highlightType` down to `AttributeRowRPG` and `SkillRowRPG`. These components would check if their own `attrKey` or `skill.id` matches and apply a temporary highlight class (e.g., `ring-2 ring-yellow-400 ring-offset-2 ring-offset-[var(--rpg-panel-bg)] shadow-lg`) for a short duration.
        *   Use a `useEffect` in `RPGStatusPanel` with a `setTimeout` to clear `lastAllocatedItemId` and `highlightType` after approximately 500-1000ms, removing the highlight.
    *   **Button State:** Ensure the "+" buttons in `AttributeRowRPG` and `SkillRowRPG` are visually disabled (e.g., lower opacity, `cursor-not-allowed`) when `canAllocate` is `false` (i.e., no points available for that type). The `btn-dreamy:disabled` class should handle this if applied correctly.

2.  **Buff/Debuff Display Clarity in `RPGStatusPanel.tsx`:**
    *   **Icons:** The use of `ShieldCheck` for buffs and `ShieldExclamation` for debuffs is appropriate. Ensure custom icons passed via the `StatusEffect_RPG.icon` field are also rendered if present, perhaps with a fallback to the default shield icons.
    *   **Duration Text:** The `formatDuration` function (which should be moved to `rpgSystem/utils/rpgUtils.ts` or a shared utility file if `RPGStatusPanel` also uses it) for "Permanent" or "X turns remaining" is effective. Ensure consistency across all displays.
    *   **Visual Distinction:** In `EffectDisplayRPG`, the conditional border color based on `isBuff` is a good starting point. Consider applying slightly different background shades within the `rpg-attribute-slot` for buffs versus debuffs to make them more distinct at a glance, utilizing the RPG theme variables (e.g., `rgba(var(--rpg-gauge-health-fill-start-rgb), 0.1)` for buff backgrounds, and a muted red/orange `rgba(var(--rpg-text-accent-rgb), 0.1)` for debuffs). This requires ensuring the RGB versions of these color variables are readily available.

3.  **ChatStatusHeader Refinements (`ChatStatusHeader.tsx`):**
    *   **Dynamic Items:** The logic to add buff/debuff counts to `dynamicItems` is well-implemented.
    *   **Tooltips:** Ensure tooltips for buffs/debuffs in the header clearly list all active effects and their remaining durations, as currently implemented with `formatEffectTooltip`.
    *   **Space Efficiency:** If the number of status items in the header grows significantly, consider strategies to combine less critical items or relocate them to the main RPG panel to prevent header clutter, especially on smaller screens. (The current set appears manageable.)

**Affected Files:**
*   `src/rpgSystem/components/RPGStatusPanel.tsx`
*   `src/rpgSystem/components/AttributeRowRPG.tsx`
*   `src/rpgSystem/components/SkillRowRPG.tsx`
*   `src/rpgSystem/components/EffectDisplayRPG.tsx`
*   `src/rpgSystem/utils/rpgUtils.ts` (for `formatDuration` relocation)
*   `src/components/ChatStatusHeader.tsx`
*   `index.html` (or theme CSS files for new RGB color variables if needed)

**Benefits:**
These enhancements will make RPG point allocation more interactive and satisfying, providing immediate visual confirmation of progression. Clearer visual distinction and information for buffs and debuffs will enable players to quickly and intuitively understand their character's current state, improving overall game comprehension and engagement.
