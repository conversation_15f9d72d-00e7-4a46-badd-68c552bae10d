
import React, { useContext } from 'react';
import { Quest } from '../types';
import { Icons, UIText } from '../constants';
import { ThemeContext } from '../contexts/ThemeContext';

interface QuestsModalProps {
  isOpen: boolean;
  quests: Quest[];
  onClose: () => void;
  enableBackdropBlur?: boolean;
}

const getQuestStatusStyle = (status: Quest['status']) => {
  switch (status) {
    case 'active': return {
        textColor: 'var(--rpg-text-accent, var(--text-yellow-500))', // Fallback if RPG var not set
        bgColor: 'rgba(var(--rpg-text-accent-rgb, 251, 192, 45), 0.2)', // RPG Yellow (or similar) with alpha
        borderColor: 'rgba(var(--rpg-text-accent-rgb, 251, 192, 45), 0.4)'
    };
    case 'completed': return {
        textColor: 'var(--rpg-gauge-health-fill-start, var(--text-green-500))', // RPG Green
        bgColor: 'rgba(var(--rpg-gauge-health-fill-start-rgb, 76, 175, 80), 0.2)',
        borderColor: 'rgba(var(--rpg-gauge-health-fill-start-rgb, 76, 175, 80), 0.4)'
    };
    case 'failed': return {
        textColor: 'var(--rpg-text-secondary, var(--text-red-500))', // RPG Red (muted)
        bgColor: 'rgba(var(--rpg-text-secondary-rgb, 211, 47, 47), 0.15)',
        borderColor: 'rgba(var(--rpg-text-secondary-rgb, 211, 47, 47), 0.3)'
    };
    default: return {
        textColor: 'var(--rpg-text-secondary)',
        bgColor: 'rgba(var(--rpg-secondary-rgb), 0.1)',
        borderColor: 'var(--rpg-divider-color)'
    };
  }
};


const QuestsModal: React.FC<QuestsModalProps> = ({ isOpen, quests, onClose, enableBackdropBlur = true }) => {
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  if (!isOpen) return null;

  const mainBgClasses = "fixed inset-0 bg-black/60 flex items-center justify-center z-[70] p-4";
  const mainBgBlurClass = enableBackdropBlur ? "backdrop-blur-sm" : "";
  const contentAreaId = "quests-modal-content";

  return (
    <div
      className={`${mainBgClasses} ${mainBgBlurClass}`}
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="quests-modal-title"
      aria-describedby={contentAreaId}
    >
      <div
        className="bg-secondary-themed p-5 md:p-6 rounded-xl shadow-themed-xl w-full max-w-lg border border-themed flex flex-col max-h-[80vh]"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4 flex-shrink-0">
          <h3 id="quests-modal-title" className="text-lg font-semibold text-accent-themed flex items-center">
            <Icons.Quest className="w-5 h-5 mr-2" />
            {UIText.quests}
          </h3>
          <button
            onClick={onClose}
            className="p-1 text-primary-themed hover:text-accent-themed rounded-md hover:bg-element-themed/50 transition-colors"
            aria-label={UIText.closeModal}
          >
            <Icons.Close className="w-5 h-5" />
          </button>
        </div>
        
        <div id={contentAreaId} className="text-primary-themed text-sm overflow-y-auto flex-grow pr-1">
          {quests.length === 0 ? (
            <p className="italic text-secondary-themed text-center py-4">{UIText.noQuests}</p>
          ) : (
            <ul className="space-y-3">
              {quests.sort((a,b) => b.lastUpdated - a.lastUpdated).map(quest => {
                const statusStyle = getQuestStatusStyle(quest.status);
                return (
                  <li 
                    key={quest.id} 
                    className="p-2.5 rounded-md shadow-sm"
                    style={{
                      backgroundColor: 'var(--rpg-slot-bg)',
                      border: '1px solid var(--rpg-slot-border)',
                    }}
                  >
                    <div className="flex justify-between items-start">
                      <p className="font-semibold text-[var(--rpg-text-primary)]">{quest.title}</p>
                      <span 
                        className="text-xs font-medium px-1.5 py-0.5 rounded-full border"
                        style={{ 
                            color: statusStyle.textColor, 
                            backgroundColor: statusStyle.bgColor,
                            borderColor: statusStyle.borderColor,
                        }}
                      >
                        {quest.status.toUpperCase()}
                      </span>
                    </div>
                    <p className="text-xs text-[var(--rpg-text-secondary)] mt-1">{quest.description}</p>
                    {quest.objectives && quest.objectives.length > 0 && (
                      <div className="mt-1.5">
                        <p className="text-xs font-medium text-[var(--rpg-text-accent)] opacity-90">目标:</p>
                        <ul className="pl-2 text-xs text-[var(--rpg-text-secondary)] space-y-0.5">
                          {quest.objectives.map((obj, i) => <li key={i}><span className="mr-1 text-[var(--rpg-text-accent)]">✧</span>{obj}</li>)}
                        </ul>
                      </div>
                    )}
                  </li>
                );
              })}
            </ul>
          )}
        </div>
        <div className="mt-6 flex justify-end flex-shrink-0">
            <button
              onClick={onClose}
              className="btn-dreamy btn-dreamy-sm"
            >
              {UIText.closeModal}
            </button>
        </div>
      </div>
    </div>
  );
};

export default QuestsModal;
