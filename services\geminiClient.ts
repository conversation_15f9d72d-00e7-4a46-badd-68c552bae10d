
// Enhanced AI Service - Supports multiple providers (OpenAI, Gemini, Claude, etc.)
import { GoogleGenAI } from "@google/genai";
import { UIText } from '../constants';
import { AIServiceManager, AIServiceConfig } from "./aiProviders/aiServiceManager";
import { AIMessage, AIProviderType, GeminiResponseFormat } from "./aiProviders/types";
import { localConfigService } from "./localConfigService";

// Legacy Gemini client for backward compatibility
const API_KEY = process.env.API_KEY || process.env.GEMINI_API_KEY;

if (!API_KEY) {
  if (typeof process === 'undefined' || (process && process.env && process.env.NODE_ENV !== 'test')) {
    console.warn("AI API key not found. Multi-provider AI service will have limited functionality.");
  }
}

export const ai = API_KEY ? new GoogleGenAI({ apiKey: API_KEY }) : null;

// Enhanced AI Service Manager
let aiServiceManager: AIServiceManager | null = null;

function initializeAIService(): AIServiceManager {
  if (aiServiceManager) return aiServiceManager;

  const config = localConfigService.getConfig();
  const apiKeys = {
    gemini: process.env.GEMINI_API_KEY || process.env.API_KEY,
    openai: process.env.OPENAI_API_KEY,
    anthropic: process.env.ANTHROPIC_API_KEY
  };

  const serviceConfig: AIServiceConfig = {
    primaryProvider: (config.ai?.provider as AIProviderType) || 'gemini',
    fallbackProviders: ['openai', 'anthropic', 'gemini'].filter(p => p !== config.ai?.provider) as AIProviderType[],
    enableAutoFallback: config.ai?.fallbackToCloud ?? true,
    retryAttempts: config.ai?.maxRetries || 3,
    providers: {}
  };

  // Configure available providers
  if (apiKeys.gemini) {
    serviceConfig.providers.gemini = {
      type: 'gemini',
      apiKey: apiKeys.gemini,
      defaultModel: 'gemini-2.5-flash-preview-05-20',
      timeout: config.ai?.timeout || 30000
    };
  }

  if (apiKeys.openai) {
    serviceConfig.providers.openai = {
      type: 'openai',
      apiKey: apiKeys.openai,
      defaultModel: 'gpt-4o-mini',
      timeout: config.ai?.timeout || 30000
    };
  }

  if (apiKeys.anthropic) {
    serviceConfig.providers.anthropic = {
      type: 'anthropic',
      apiKey: apiKeys.anthropic,
      defaultModel: 'claude-3-5-haiku-20241022',
      timeout: config.ai?.timeout || 30000
    };
  }

  // Add local provider if enabled
  if (config.features?.enableLocalAI && config.ai?.localEndpoint) {
    serviceConfig.providers.local = {
      type: 'local',
      baseUrl: config.ai.localEndpoint,
      timeout: config.ai?.timeout || 30000
    };
  }

  aiServiceManager = new AIServiceManager(serviceConfig);
  return aiServiceManager;
}

// Enhanced AI functions
export function getAIServiceManager(): AIServiceManager {
  return initializeAIService();
}

export async function switchAIProvider(providerType: AIProviderType): Promise<boolean> {
  const aiService = initializeAIService();
  return aiService.switchProvider(providerType);
}

export async function getAvailableModels(): Promise<Record<AIProviderType, any[]>> {
  const aiService = initializeAIService();
  return await aiService.getAllAvailableModels();
}

export async function validateAllProviders(): Promise<Record<AIProviderType, boolean>> {
  const aiService = initializeAIService();
  return await aiService.validateAllProviders();
}

export function getProviderStatus(): Record<AIProviderType, { available: boolean; current: boolean }> {
  const aiService = initializeAIService();
  return aiService.getProviderStatus();
}
