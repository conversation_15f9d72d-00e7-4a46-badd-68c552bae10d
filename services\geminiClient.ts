
import { GoogleGenAI } from "@google/genai";
import { UIText } from '../constants';

const API_KEY = process.env.API_KEY;

// This console.error is for developer awareness, doesn't stop execution.
if (!API_KEY) {
  // Avoid logging in 'test' environments if process is defined.
  if (typeof process === 'undefined' || (process && process.env && process.env.NODE_ENV !== 'test')) {
    console.error(UIText.errorApiKeyMissing + " The application may not function correctly without a valid API_KEY.");
  }
}

// Initialize with API_KEY directly, which might be undefined if not set.
// The Gemini SDK should handle an undefined apiKey gracefully or throw an error that can be caught later during API calls,
// rather than at instantiation if possible. However, we must follow the direct usage guideline.
// The prompt states "Assume this variable is pre-configured, valid, and accessible". If this holds, API_KEY is a string.
// If it doesn't hold and API_KEY is undefined, this adheres to `new GoogleGenAI({apiKey: process.env.API_KEY})`.
export const ai = new GoogleGenAI({ apiKey: API_KEY });
