import React from 'react';
import ReactDOM from 'react-dom/client';
import { App } from './App'; // Corrected import
import { ThemeProvider } from './contexts/ThemeContext';
import { NotificationProvider } from './contexts/NotificationContext';
import './src/tailwind.css'; // Import Tailwind CSS

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
    <ThemeProvider>
      <NotificationProvider> 
        <App />
      </NotificationProvider>
    </ThemeProvider>
  </React.StrictMode>
);