
import { useState, useCallback, useRef } from 'react';
import { ActiveTab as SidebarActiveTabType } from '../components/SidebarMenu';
import { NotificationType, DetailModalType } from '../types'; // Import DetailModalType from global types
import { UIText } from '../constants'; 

interface ConfirmationModalState {
  isOpen: boolean;
  title: string;
  message?: string;
  children?: React.ReactNode;
  onConfirm: (() => void) | null;
  confirmText?: string;
  cancelText?: string;
}

// Removed local definition: type DetailModalType = 'inventory' | 'locations' | 'quests' | 'characters';

export const useUIState = (
    addNotification: (message: string, type: NotificationType, duration?: number) => void
) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);
  const [sidebarActiveTab, setSidebarActiveTab] = useState<SidebarActiveTabType>('settings');
  
  const [isStatusPanelOpen, setIsStatusPanelOpen] = useState<boolean>(false);
  const statusPanelRef = useRef<HTMLDivElement>(null); 

  const [confirmationModalState, setConfirmationModalState] = useState<ConfirmationModalState>({
    isOpen: false, title: '', message: '', onConfirm: null
  });
  
  const [activeDetailModal, setActiveDetailModal] = useState<DetailModalType | null>(null);

  const [isRenamingHeaderTitle, setIsRenamingHeaderTitle] = useState<boolean>(false);
  const [headerRenameValue, setHeaderRenameValue] = useState<string>('');
  const headerRenameInputRef = useRef<HTMLInputElement>(null);

  const toggleSidebar = useCallback(() => {
    setIsSidebarOpen(prev => !prev);
  }, []);

  const handleRequestConfirmation = useCallback((
    title: string,
    message: string,
    onConfirm: () => void,
    confirmText?: string,
    cancelText?: string,
    children?: React.ReactNode
  ) => {
    setConfirmationModalState({ isOpen: true, title, message, children, onConfirm, confirmText, cancelText });
  }, []);

  const closeConfirmationModal = useCallback(() => {
    setConfirmationModalState(prevState => ({ ...prevState, isOpen: false, onConfirm: null }));
  }, []);

  const closeDetailModal = useCallback(() => setActiveDetailModal(null), []);

  // Header Renaming Logic
  const handleHeaderTitleClick = (currentSaveName?: string) => {
    if (currentSaveName) {
      setHeaderRenameValue(currentSaveName);
      setIsRenamingHeaderTitle(true);
      setTimeout(() => headerRenameInputRef.current?.focus(), 0);
    }
  };

  const handleConfirmHeaderRename = (
    currentQuickSaveId: string | null, 
    renameSaveFn: (saveId: string, newName: string) => void,
    currentSaveName?: string
  ) => {
    if (currentQuickSaveId && headerRenameValue.trim()) {
      renameSaveFn(currentQuickSaveId, headerRenameValue.trim());
    } else if (currentQuickSaveId && !headerRenameValue.trim()) {
      addNotification(UIText.saveErrorInvalidName, "error");
      if (currentSaveName) setHeaderRenameValue(currentSaveName); 
      headerRenameInputRef.current?.focus();
      return; 
    }
    setIsRenamingHeaderTitle(false);
  };

  const handleCancelHeaderRename = (currentSaveName?: string) => {
    setIsRenamingHeaderTitle(false);
    if (currentSaveName) setHeaderRenameValue(currentSaveName);
  };

  const handleHeaderRenameKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement>,
    currentQuickSaveId: string | null,
    renameSaveFn: (saveId: string, newName: string) => void,
    currentSaveName?: string
    ) => {
    if (event.key === 'Enter') handleConfirmHeaderRename(currentQuickSaveId, renameSaveFn, currentSaveName);
    else if (event.key === 'Escape') handleCancelHeaderRename(currentSaveName);
  };


  return {
    isSidebarOpen,
    setIsSidebarOpen, // expose setter if direct control is needed
    toggleSidebar,
    sidebarActiveTab,
    setSidebarActiveTab,
    isStatusPanelOpen,
    setIsStatusPanelOpen,
    statusPanelRef,
    confirmationModalState,
    setConfirmationModalState, // expose setter if needed
    handleRequestConfirmation,
    closeConfirmationModal,
    activeDetailModal,
    setActiveDetailModal,
    closeDetailModal,
    isRenamingHeaderTitle,
    setIsRenamingHeaderTitle,
    headerRenameValue,
    setHeaderRenameValue,
    headerRenameInputRef,
    handleHeaderTitleClick,
    handleConfirmHeaderRename,
    handleCancelHeaderRename,
    handleHeaderRenameKeyDown,
  };
};
