
import { marked } from 'marked';
import DOMPurify from 'dompurify';

// Internal function to handle the recursive parsing logic without re-checking for panel syntax
function parseCustomPanelSyntaxInternal(text: string, isRecursiveCall: boolean = false): string {
  let html = text;

  // Phase 1: Process <section_name title="Optional Title">...</section_name> tags first for structure
  const sectionRegex = /<([a-zA-Z0-9_]+)\s*(?:title="([^"]*)")?\s*(?:\/>|>(.*?)<\/\1>)/gs;
  html = html.replace(sectionRegex, (match, tagName, titleAttr, innerContent) => {
    const sectionClass = `panel-section panel-section-${tagName.toLowerCase().replace(/\s+/g, '-')}`;
    
    let titleElement = '';
    // Condition to skip title: tagName is a single uppercase letter AND (titleAttr is not provided OR titleAttr is empty/whitespace).
    if (!(tagName.length === 1 && tagName === tagName.toUpperCase() && (!titleAttr || titleAttr.trim() === ''))) {
        const titleText = (titleAttr && titleAttr.trim() !== '') ? titleAttr.trim() : tagName.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase());
        titleElement = `<h3 class="panel-section-title">${titleText}</h3>`;
    }
    
    if (innerContent === undefined && match.endsWith('/>')) { // Self-closing tag
        return `<div class="${sectionClass}">${titleElement}<div class="panel-section-content"></div></div>`;
    }
    
    const parsedInnerContent = parseCustomPanelSyntaxInternal(innerContent || '', true);
    return `<div class="${sectionClass}">${titleElement}<div class="panel-section-content">${parsedInnerContent}</div></div>`;
  });

  // Phase 2: Process all bracket-based custom tags
  // Order matters: more specific or structured tags first.

  // [plotstatus|IconTitle|Description|Cost]
  html = html.replace(/\[plotstatus\|([^|]*?)\|([^|]*?)\|([^\]]*?)\]/g, (match, titleAndIcon, description, cost) => {
    const iconMatch = titleAndIcon.match(/^(\p{Emoji})\s*(.*)/u);
    let icon = '';
    let title = titleAndIcon.trim();
    if (iconMatch) {
      icon = iconMatch[1];
      title = iconMatch[2].trim();
    }
    return `<div class="panel-item panel-plotstatus">
              ${icon ? `<span class="plotstatus-icon">${icon}</span>` : ''}
              <div class="plotstatus-content">
                <strong class="plotstatus-title">${title}</strong>
                <p class="plotstatus-description">${description.trim()}</p>
              </div>
              <span class="plotstatus-cost">(${cost.trim()})</span>
            </div>`;
  });

  // New BBS Post tag: [Author|Content|Heat|Replies] (4 parts)
  const bbsPostRegex = /\[([^|\]]+?)\|([\s\S]+?)\|([^|\]]+?)\|([^|\]]+?)\]/g;
  html = html.replace(bbsPostRegex, (_, author, content, heat, replies) =>
      `<div class="panel-item panel-bbs-post">
        <div class="bbs-post-header">
          <strong class="bbs-post-author">${author.trim()}</strong>
          <span class="bbs-post-heat" title="${heat.trim().length} heat entries">${heat.trim()}</span>
        </div>
        <p class="bbs-post-content">${content.trim()}</p>
        <span class="bbs-post-replies">${replies.trim()}</span>
      </div>`);
  
  // [game_numerical|Label|Current|Max]
  html = html.replace(/\[game_numerical\|([^|]*?)\|(\d+)\|(\d+)\]/g, (_, label, current, max) => {
    const numCurrent = parseInt(current, 10);
    const numMax = parseInt(max, 10);
    const percentage = numMax > 0 ? Math.min(100, Math.max(0,(numCurrent / numMax) * 100)) : 0;
    return `<div class="panel-item panel-game-numerical">
              <span class="game-numerical-label">${label.trim()}</span>
              <div class="game-numerical-bar-container">
                <div class="game-numerical-bar-fill" style="width: ${percentage.toFixed(2)}%;"></div>
                <span class="game-numerical-bar-text">${numCurrent}/${numMax}</span>
              </div>
            </div>`;
  });
  
  // [item|Name|Description|Quantity]
  html = html.replace(/\[item\|([^|]*?)\|([^|]*?)\|(\d+)\]/g, (_, name, description, quantity) =>
    `<div class="panel-item panel-item-entry">
        <div class="item-header"><strong class="item-name">${name.trim()}</strong> <span class="item-quantity">(x${quantity})</span></div>
        <p class="item-description">${description.trim()}</p>
     </div>`);

  // [hotelroom|Name|Description|Cost|CurrentGuest (optional)]
  html = html.replace(/\[hotelroom\|([^|]*?)\|([^|]*?)\|([^|]*?)(?:\|([^\]]*?))?\]/g,
    (_, name, desc, cost, guest) =>
    `<div class="panel-item panel-hotelroom">
        <strong class="hotelroom-name">${name.trim()}</strong>
        <p class="hotelroom-detail"><span class="detail-label">描述:</span> ${desc.trim()}</p>
        <p class="hotelroom-detail"><span class="detail-label">费用:</span> ${cost.trim()}</p>
        ${guest && guest.trim() && guest.trim().toLowerCase() !== '无' ? `<p class="hotelroom-detail"><span class="detail-label">房客:</span> ${guest.trim()}</p>` : '<p class="hotelroom-detail"><span class="detail-label">房客:</span> <span class="text-secondary-themed opacity-70">无</span></p>'}
     </div>`);

  // [facilities|Name (StatusInParen)|Description|UnlockStatus]
  html = html.replace(/\[facilities\|([^|]*?)\|([^|]*?)\|([^\]]*?)\]/g, (_, nameAndParenStatus, description, unlockStatusText) => {
     const nameMatch = nameAndParenStatus.trim().match(/(.*)\s*\(([^)]+)\)$/);
     let name = nameAndParenStatus.trim();
     let statusInParen = "";
     if(nameMatch && nameMatch[1] && nameMatch[2]) {
         name = nameMatch[1].trim();
         statusInParen = nameMatch[2].trim();
     }
     const unlockStatusCleaned = unlockStatusText.trim().toLowerCase();
     const statusClass = unlockStatusCleaned.includes('未') || unlockStatusCleaned.includes('lock') || unlockStatusCleaned.includes('损坏') ? 'locked' : 'unlocked';
     
     return `<div class="panel-item panel-facility">
                <div class="facility-header">
                  <strong class="facility-name">${name}</strong>
                  ${statusInParen ? `<span class="facility-status-inline">(${statusInParen})</span>` : ''}
                  <span class="facility-status-main facility-status-${statusClass}">${unlockStatusText.trim()}</span>
                </div>
                <p class="facility-description">${description.trim()}</p>
             </div>`;
  });
  
  // [upgrade_conditions|Condition Text|Status]
  html = html.replace(/\[upgrade_conditions\|([^|]*?)\|([^\]]*?)\]/g, (_, condition, status) => {
    const statusTrimmed = status.trim();
    const statusCleaned = statusTrimmed.toLowerCase().replace(/^\[|\]$/g, '');
    let statusClass = 'condition-incomplete';
    if (statusCleaned.includes('已完成') || statusCleaned.includes('complete')) {
        statusClass = 'condition-complete';
    } else if (statusCleaned.includes('进行中') || statusCleaned.includes('progress')) {
        statusClass = 'condition-inprogress';
    }
    return `<div class="panel-item panel-upgrade-condition ${statusClass}">
              <span class="condition-text">${condition.trim()}</span>
              <span class="condition-status">${statusTrimmed.startsWith('[') && statusTrimmed.endsWith(']') ? statusTrimmed : `[${statusTrimmed}]`}</span>
           </div>`;
  });

  // Key-Value pairs: [funds|Currency|Amount], [other_points|Label|Value], [energy|Current|Max]
  html = html.replace(/\[(funds|other_points|energy)\|([^|]*?)\|([^\]]*?)\]/g, (match, type, label, value) => 
    `<div class="panel-item panel-key-value panel-${type.toLowerCase()}">
        <strong class="kv-label">${label.trim()}:</strong> <span class="kv-value">${value.trim()}</span>
     </div>`);

  // Single value display: [level|Value], [time|Value]
  html = html.replace(/\[(level|time)\|([^\]]*?)\]/g, (match, type, value) =>
    `<div class="panel-item panel-info panel-${type.toLowerCase()}">${value.trim()}</div>`);

  // Guest/Employee with optional second part: [guest|Primary Info|Secondary Info (optional)]
  html = html.replace(/\[(guest|employee)\|([^|]*?)(?:\|([^\]]*?))?\]/g, (match, type, primaryInfo, secondaryInfo) => {
    let labelPrefix = '';
    if (type.toLowerCase() === 'guest') labelPrefix = '客人: ';
    else if (type.toLowerCase() === 'employee') labelPrefix = '员工: ';
    
    let content = `<strong class="info-label">${labelPrefix}</strong>${primaryInfo.trim()}`;
    if(secondaryInfo && secondaryInfo.trim()){
        content += ` <span class="info-separator">|</span> <span class="info-secondary">${secondaryInfo.trim()}</span>`;
    }
    return `<div class="panel-item panel-info panel-${type.toLowerCase()}">${content}</div>`;
  });

  // New Vesperis comment tag: [Vesperis|Comment Text]
  const vesperisCommentRegex = /\[(Vesperis)\|([\s\S]+?)\]/g;
  html = html.replace(vesperisCommentRegex, (_, speaker, comment) =>
      `<p class="panel-item panel-bbs-comment">
        <strong class="bbs-comment-speaker">${speaker.trim()}:</strong> ${comment.trim()}
      </p>`);

  // Text blocks: [description|Text], [system_suggestion|Text], [hotel_warehouse|Text]
  html = html.replace(/\[(description|system_suggestion|hotel_warehouse)\|([\s\S]*?)\]/g, (match, type, text) => {
    let specificClass = '';
    if (type.toLowerCase() === 'system_suggestion') specificClass = 'panel-suggestion';
    if (type.toLowerCase() === 'description') specificClass = 'panel-description';
    return `<p class="panel-item panel-text panel-${type.toLowerCase()} ${specificClass}">${text.trim()}</p>`;
  });
  
  return html;
}

export function parseCustomPanelSyntax(text: string): string {
    const panelSyntaxRegex = /\[(?:level|time|funds|plotstatus|item|game_numerical|hotelroom|facilities|upgrade_conditions|guest|employee|description|system_suggestion|hotel_warehouse|Vesperis|[^|\]]+?\|[\s\S]+?\|[^|\]]+?\|[^|\]]+?)|(?:<([a-zA-Z0-9_]+)\s*(?:title="[^"]*")?\s*(?:\/>|>(?:.|\n)*?<\/\1>))/;
    const containsPanelSyntax = panelSyntaxRegex.test(text);

    if (!containsPanelSyntax) {
        return text;
    }

    let processedHtml = parseCustomPanelSyntaxInternal(text, false);

    if (processedHtml.trim().startsWith('<div class="custom-status-panel">')) {
        return processedHtml;
    }
    
    if (processedHtml.includes('panel-section') || processedHtml.includes('panel-item')) {
        return `<div class="custom-status-panel">${processedHtml}</div>`;
    }
    
    return processedHtml;
}


export function htmlToPlainText(html: string): string {
  try {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    tempDiv.querySelectorAll('br, p').forEach(el => el.replaceWith('\n'));
    return tempDiv.textContent || tempDiv.innerText || "";
  } catch (e) {
    console.error("Error converting HTML to plain text:", e);
    return html.replace(/<[^>]+>/g, ' ').replace(/\s+/g, ' ').trim();
  }
}


export const renderRichTextStatic = (text: string): string => {
    let processedText = text;
    processedText = processedText.replace(/\\n/g, '\n');
    processedText = parseCustomPanelSyntax(processedText);
    
    if (!processedText.includes('class="custom-status-panel"')) {
        processedText = processedText.split('\n').map(line => {
            if (line.trim() && !/^[*-]\s/.test(line.trim()) && !/^>/.test(line.trim()) && !/^#/.test(line.trim()) && !/```/.test(line.trim())) {
            }
            return line;
        }).join('\n');
    }
    
    const rawHtml = marked.parse(processedText, { breaks: true, gfm: true }) as string;
    
    return DOMPurify.sanitize(rawHtml, {
        USE_PROFILES: { html: true },
        ALLOWED_TAGS: [
            'b', 'i', 'u', 'strong', 'em', 'br', 'p', 'span', 'div', 'mark', 
            'del', 'ins', 'sub', 'sup', 'code', 'pre', 'blockquote', 'hr',
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li',
            'table', 'thead', 'tbody', 'tr', 'th', 'td', 'caption',
            'a', 'img', 'choice', // Added 'choice' tag
            'section', 'article', 'header', 'footer', 'nav', 'aside', 'main',
            'figure', 'figcaption', 'details', 'summary',
            'svg', 'path', 'circle', 'rect', 'line', 'text', 'g', 'defs', 'symbol', 'use', 'image' 
        ],
        ALLOWED_ATTR: [
            'style', 'class', 'id', 'title', 'lang', 'dir', 'align',
            'href', 'target', 'rel',
            'src', 'alt', 'width', 'height', 'loading',
            'colspan', 'rowspan', 'scope', 'border', 'cellpadding', 'cellspacing', 'summary',
            'start', 'type', 'value', 
            'open',
            'letter', // Added 'letter' attribute for 'choice' tag
            'viewbox', 'd', 'cx', 'cy', 'r', 'x', 'y', 'rx', 'ry',
            'fill', 'stroke', 'stroke-width', 'transform', 'opacity',
            'points', 'gradienttransform', 'patternunits', 'gradientunits',
            'xlink:href', 'preserveaspectratio',
            'role', 'aria-label', 'aria-labelledby', 'aria-describedby', 'aria-hidden', 'aria-expanded', 'aria-controls', 'aria-live', 'aria-atomic'
        ],
        ALLOW_DATA_ATTR: true,
    });
};

/**
 * Extracts choices from HTML content that uses <choice letter="X">...</choice> tags.
 * @param htmlContent The HTML string to parse.
 * @returns An array of 4 strings, with extracted choices in A, B, C, D order.
 *          If a choice for a specific letter is not found, it will be an empty string.
 */
export const extractTaggedChoicesFromHtml = (htmlContent: string): string[] => {
  const choicesMap: Record<string, string> = { A: '', B: '', C: '', D: '' };
  const choiceOrder = ['A', 'B', 'C', 'D'];

  if (typeof document === 'undefined' || !htmlContent) {
    return choiceOrder.map(letter => choicesMap[letter]);
  }

  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    const choiceElements = doc.querySelectorAll('choice');

    choiceElements.forEach(el => {
      const letterAttr = el.getAttribute('letter');
      if (letterAttr) {
        const letter = letterAttr.toUpperCase();
        if (choiceOrder.includes(letter) && !choicesMap[letter]) { // Take first found for each letter
          // Preserve inner HTML for rich text choices, then convert to plain text if needed by consuming logic
          // For button labels, plain text is usually preferred. If the button itself should render HTML, this would need adjustment.
          choicesMap[letter] = el.textContent?.trim() || '';
        }
      }
    });
  } catch (error) {
    console.error("Error parsing HTML for tagged choices:", error);
    // Return empty choices if parsing fails, to prevent crashes
    return choiceOrder.map(() => '');
  }
  
  return choiceOrder.map(letter => choicesMap[letter]);
};
