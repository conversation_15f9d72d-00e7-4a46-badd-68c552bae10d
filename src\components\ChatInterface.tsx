
import React, { useState, useRef, useEffect, useContext, useCallback } from 'react';
import { DialogueLine, GameSettingsData, PlayerStatus } from '../types'; 
import { Icons, UIText } from '../constants'; 
import DialogueBubble from './DialogueBubble';
import ChatStatusHeader from './ChatStatusHeader'; 
import { ThemeContext } from '../contexts/ThemeContext';
import { useMediaQuery } from '../hooks/useMediaQuery';

interface ChatInterfaceProps {
  dialogueLines: DialogueLine[];
  onSendMessage: (message: string) => void;
  currentChoices: string[]; 
  isLoading: boolean; 
  playerName: string;
  gameSettings: GameSettingsData; 
  playerStatus: PlayerStatus; // This should now be the composite PlayerStatus
  onRegenerateResponse?: (lineId: string) => void;
  onDeleteLine?: (lineId: string) => void; 
  isLoadingBackground?: boolean;
  editingLineId: string | null;
  currentlyEditedContent: string;
  onStartEditLine: (lineId: string, currentHtml: string) => void;
  onSaveEditedLine: () => void;
  onCancelEditLine: () => void;
  onCurrentlyEditedContentChange: (newHtml: string) => void;
}

const MIN_CHAT_WIDTH = 300; 
const DEFAULT_CHAT_WIDTH = 700; 
const MAX_RENDERED_DIALOGUE_LINES = 50; 

const ChatInterface: React.FC<ChatInterfaceProps> = React.memo(({ 
  dialogueLines, 
  onSendMessage, 
  currentChoices, 
  isLoading, 
  playerName, // PlayerName is part of playerStatus, but can be passed for convenience if used elsewhere
  gameSettings,
  playerStatus, 
  onRegenerateResponse,
  onDeleteLine,
  isLoadingBackground,
  editingLineId,
  currentlyEditedContent,
  onStartEditLine,
  onSaveEditedLine,
  onCancelEditLine,
  onCurrentlyEditedContentChange
}) => {
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const themeContext = useContext(ThemeContext);
  const isPortrait = useMediaQuery('(orientation: portrait)');

  const chatWrapperRef = useRef<HTMLDivElement>(null);
  const [isResizing, setIsResizing] = useState(false);
  const resizeStartInfoRef = useRef<{ initialX: number, initialWidth: number } | null>(null);
  const animationFrameRef = useRef<number | null>(null); 
  
  const [dimensions, setDimensions] = useState<{ width: string | number; height: string | number }>({ 
    width: DEFAULT_CHAT_WIDTH, 
    height: '100%' 
  });

  if (!themeContext) throw new Error("ThemeContext not found");

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [dialogueLines, isLoading, isLoadingBackground]);

  const handleSend = () => {
    if (inputValue.trim() && !isLoading) {
      onSendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  const handleFixedChoiceClick = (choiceIndex: number) => {
    if (!isLoading && currentChoices[choiceIndex]) {
      onSendMessage(currentChoices[choiceIndex]); 
    }
  };
  
  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  const placeholderText = isLoading 
    ? UIText.waitingForResponse 
    : UIText.typeYourResponse; 

  const handleResizeMouseDown = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (isPortrait) return; 
    event.preventDefault(); 
    if (chatWrapperRef.current) {
      const currentPixelWidth = chatWrapperRef.current.offsetWidth;
      setIsResizing(true);
      document.body.style.userSelect = 'none'; 
      document.body.style.cursor = 'ew-resize'; 
      resizeStartInfoRef.current = {
        initialX: event.clientX,
        initialWidth: currentPixelWidth, 
      };
    }
  }, [isPortrait, setIsResizing]);


  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (isPortrait || !isResizing || !resizeStartInfoRef.current || !chatWrapperRef.current) return;
    const chatNode = chatWrapperRef.current;
    const parentNode = chatNode.parentElement;
    if (!parentNode) { setIsResizing(false); document.body.style.userSelect = ''; document.body.style.cursor = ''; resizeStartInfoRef.current = null; if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current); return; }
    const dx = event.clientX - resizeStartInfoRef.current.initialX;
    let newWidth = resizeStartInfoRef.current.initialWidth + dx;
    newWidth = Math.max(MIN_CHAT_WIDTH, newWidth);
    newWidth = Math.min(newWidth, parentNode.clientWidth); 
    if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
    animationFrameRef.current = requestAnimationFrame(() => { if (chatNode) chatNode.style.width = `${newWidth}px`; });
  }, [isPortrait, isResizing]); 

  const handleMouseUp = useCallback(() => {
    if (isPortrait || !isResizing) return; 
    if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
    if (chatWrapperRef.current && resizeStartInfoRef.current) {
        const chatNode = chatWrapperRef.current;
        const parentNode = chatNode.parentElement;
        let finalWidth = chatNode.offsetWidth;
        if (parentNode) finalWidth = Math.min(Math.max(MIN_CHAT_WIDTH, finalWidth), parentNode.clientWidth);
        else finalWidth = Math.max(MIN_CHAT_WIDTH, finalWidth);
        chatNode.style.width = `${finalWidth}px`;
        setDimensions(prevDims => ({ ...prevDims, width: finalWidth }));
    }
    setIsResizing(false);
    document.body.style.userSelect = ''; 
    document.body.style.cursor = ''; 
    resizeStartInfoRef.current = null;
  }, [isPortrait, isResizing, setDimensions, setIsResizing]); 

  useEffect(() => {
    if (isPortrait) { 
      if (chatWrapperRef.current) chatWrapperRef.current.style.width = ''; 
      setIsResizing(false); document.body.style.userSelect = ''; document.body.style.cursor = '';
      if (resizeStartInfoRef.current) resizeStartInfoRef.current = null;
      if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      return; 
    }
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    }
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      if (animationFrameRef.current) cancelAnimationFrame(animationFrameRef.current);
      if (isResizing) { document.body.style.userSelect = ''; document.body.style.cursor = ''; if (resizeStartInfoRef.current) resizeStartInfoRef.current = null; }
    };
  }, [isPortrait, isResizing, handleMouseMove, handleMouseUp]);

  useEffect(() => {
    const handleWindowResize = () => {
      if (isPortrait) return;
      if (chatWrapperRef.current && chatWrapperRef.current.parentElement && typeof dimensions.width === 'number') { 
        const parentNode = chatWrapperRef.current.parentElement;
        const newConstrainedWidth = Math.min(Math.max(MIN_CHAT_WIDTH, dimensions.width), parentNode.clientWidth);
        if (newConstrainedWidth !== dimensions.width) { chatWrapperRef.current.style.width = `${newConstrainedWidth}px`; setDimensions(prevDims => ({ ...prevDims, width: newConstrainedWidth })); }
      }
    };
    window.addEventListener('resize', handleWindowResize);
    if (typeof dimensions.width === 'number' && !isPortrait) handleWindowResize(); 
    return () => window.removeEventListener('resize', handleWindowResize);
  }, [isPortrait, dimensions.width, setDimensions]); 

  const dynamicStyles: React.CSSProperties = {
    width: isPortrait ? undefined : (typeof dimensions.width === 'number' ? `${dimensions.width}px` : dimensions.width),
    height: '100%', 
    backgroundColor: `rgba(var(--bg-secondary-rgb), ${gameSettings.chatInterfaceOpacity})`, 
    backdropFilter: gameSettings.enableBackdropBlur && gameSettings.dialogueBlur > 0 ? `blur(${gameSettings.dialogueBlur}px)` : 'none', 
    WebkitBackdropFilter: gameSettings.enableBackdropBlur && gameSettings.dialogueBlur > 0 ? `blur(${gameSettings.dialogueBlur}px)` : 'none', 
  };
  
  const chatInterfaceRootClasses = `flex flex-col p-4 rounded-xl shadow-themed-lg relative ${isPortrait ? 'chat-interface-root-portrait' : ''}`;
  const visibleDialogueLines = dialogueLines.slice(-MAX_RENDERED_DIALOGUE_LINES);
  
  const choiceButtonLabels = ['A', 'B', 'C', 'D'];
  
  return (
    <div ref={chatWrapperRef} className={chatInterfaceRootClasses} style={dynamicStyles}>
      <ChatStatusHeader status={playerStatus} /> {/* Pass the composite playerStatus */}
      <div className={`flex-1 overflow-y-auto mb-3 min-h-0 ${isPortrait ? '' : 'px-2'}`} style={{overscrollBehaviorY: 'contain'}}>
        {visibleDialogueLines.map((line) => (
          <DialogueBubble 
            key={line.id} line={line} gameSettings={gameSettings} 
            onRegenerate={onRegenerateResponse} onDeleteLine={onDeleteLine}
            editingLineId={editingLineId} currentlyEditedContent={currentlyEditedContent}
            onStartEdit={onStartEditLine} onSaveEdit={onSaveEditedLine}
            onCancelEdit={onCancelEditLine} onCurrentlyEditedContentChange={onCurrentlyEditedContentChange}
          />
        ))}
        {isLoading && !isLoadingBackground && (<div className="text-center p-2 text-sm text-accent-themed italic animate-pulse">{UIText.thinking}</div>)}
        {isLoadingBackground && (<div className="text-center p-2 text-sm text-accent-themed italic animate-pulse">{UIText.generatingImage}</div>)}
        <div ref={messagesEndRef} />
      </div>

      <div className="flex items-center border-t border-themed pt-3 flex-shrink-0"> 
        <input
          type="text" value={inputValue} onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress} placeholder={placeholderText} disabled={isLoading} 
          className="flex-grow p-3 bg-element-themed text-primary-themed rounded-l-lg ring-accent-themed focus:outline-none placeholder-themed border border-themed"
          aria-label={UIText.typeYourMessage}
        />
        <button onClick={handleSend} disabled={isLoading || !inputValue.trim()} className="btn-dreamy rounded-l-none" aria-label={UIText.sendMessage}>
          <Icons.Send className="w-6 h-6" />
        </button>
      </div>
      
      {currentChoices && currentChoices.length > 0 && !isLoading && (
        <div className={`mt-2 grid grid-cols-2 gap-2 sm:grid-cols-4 ${isPortrait ? 'grid-cols-2' : ''} flex-shrink-0`}>
          {choiceButtonLabels.map((label, index) => (
            <button
              key={label}
              onClick={() => handleFixedChoiceClick(index)}
              disabled={isLoading || !currentChoices[index]}
              className={`btn-dreamy w-full text-sm ${!currentChoices[index] ? 'opacity-50 cursor-not-allowed' : ''}`}
              aria-label={`选择 ${label}: ${currentChoices[index] || '不可用'}`}
            >
              {label}
            </button>
          ))}
        </div>
      )}

      {!isPortrait && (<div className="resize-handle" style={{ cursor: 'ew-resize' }} onMouseDown={handleResizeMouseDown} role="separator" aria-orientation="vertical" aria-label="Resize dialogue panel width"/>)}
    </div>
  );
});

export default ChatInterface;
