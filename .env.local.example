# MemoryAble Environment Configuration
# Copy this file to .env.local and configure your settings

# =============================================================================
# AI CONFIGURATION
# =============================================================================

# Google Gemini API Key (for cloud AI functionality)
# Get your API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Alternative AI providers (optional)
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# LOCAL SERVER CONFIGURATION
# =============================================================================

# Local server port (default: 3001)
LOCAL_SERVER_PORT=3001

# Local server URL (usually localhost)
LOCAL_SERVER_URL=http://localhost:3001

# Data directory for local storage
LOCAL_DATA_DIR=./local-server/data

# =============================================================================
# IMAGE GENERATION CONFIGURATION
# =============================================================================

# Image generation provider: 'local' | 'cloud' | 'disabled'
IMAGE_PROVIDER=cloud

# For local image generation (Stable Diffusion, etc.)
# LOCAL_SD_URL=http://localhost:7860
# LOCAL_SD_API_KEY=your_local_sd_api_key

# For cloud image generation
POLLINATIONS_API_URL=https://image.pollinations.ai/prompt/

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# Storage provider: 'local' | 'cloud' | 'hybrid'
STORAGE_PROVIDER=local

# For GitHub Gist backup (legacy support)
# GITHUB_PAT=your_github_personal_access_token
# GIST_ID=your_gist_id_for_backups

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Enable caching (true/false)
ENABLE_CACHING=true

# Cache size in MB
CACHE_SIZE_MB=50

# Maximum concurrent requests
MAX_CONCURRENT_REQUESTS=3

# Request timeout in milliseconds
REQUEST_TIMEOUT_MS=30000

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Enable encryption for local storage (true/false)
ENABLE_ENCRYPTION=false

# Encryption key (generate a secure random key)
# ENCRYPTION_KEY=your_32_character_encryption_key

# Allow external connections (true/false)
ALLOW_EXTERNAL_CONNECTIONS=true

# Trusted domains (comma-separated)
TRUSTED_DOMAINS=localhost,127.0.0.1,pollinations.ai

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable experimental features (true/false)
ENABLE_EXPERIMENTAL_FEATURES=false

# Enable local AI integration (true/false)
ENABLE_LOCAL_AI=false

# Enable local image generation (true/false)
ENABLE_LOCAL_IMAGE_GEN=false

# Enable advanced regex features (true/false)
ENABLE_ADVANCED_REGEX=true

# Enable character relationships (true/false)
ENABLE_CHARACTER_RELATIONSHIPS=true

# Enable world book hierarchy (true/false)
ENABLE_WORLDBOOK_HIERARCHY=true

# Enable batch operations (true/false)
ENABLE_BATCH_OPERATIONS=true

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================

# Development mode (true/false)
NODE_ENV=development

# Enable debug logging (true/false)
DEBUG_LOGGING=false

# Enable performance monitoring (true/false)
ENABLE_PERFORMANCE_MONITORING=false

# Hot reload for local server (true/false)
ENABLE_HOT_RELOAD=true

# =============================================================================
# INTEGRATION CONFIGURATION
# =============================================================================

# SillyTavern compatibility mode: 'v1' | 'v2' | 'auto'
SILLYTAVERN_COMPAT_MODE=auto

# Default character card format: 'json' | 'png'
DEFAULT_CHAR_CARD_FORMAT=json

# Default world book format: 'json' | 'yaml'
DEFAULT_WORLDBOOK_FORMAT=json

# =============================================================================
# UI/UX CONFIGURATION
# =============================================================================

# Default theme: 'light' | 'dark' | 'sakura' | 'starry' | 'candy' | 'forest'
DEFAULT_THEME=dark

# Enable animations (true/false)
ENABLE_ANIMATIONS=true

# Enable sound effects (true/false)
ENABLE_SOUND_EFFECTS=false

# Default language: 'en' | 'zh' | 'ja'
DEFAULT_LANGUAGE=zh

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Auto backup interval in minutes
AUTO_BACKUP_INTERVAL_MINUTES=30

# Maximum number of local backups to keep
MAX_LOCAL_BACKUPS=10

# Backup compression (true/false)
ENABLE_BACKUP_COMPRESSION=true

# Include images in backups (true/false)
BACKUP_INCLUDE_IMAGES=false

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: 'error' | 'warn' | 'info' | 'debug'
LOG_LEVEL=info

# Log to file (true/false)
LOG_TO_FILE=false

# Log file path
LOG_FILE_PATH=./logs/memoryable.log

# Maximum log file size in MB
MAX_LOG_FILE_SIZE_MB=10

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Custom CSS file path (optional)
# CUSTOM_CSS_PATH=./custom/styles.css

# Custom JavaScript file path (optional)
# CUSTOM_JS_PATH=./custom/scripts.js

# Plugin directory (optional)
# PLUGIN_DIRECTORY=./plugins

# Custom AI model configurations (JSON format)
# CUSTOM_AI_MODELS={"model1":{"name":"Custom Model","endpoint":"http://localhost:8080"}}

# =============================================================================
# NOTES
# =============================================================================

# 1. Remove the '#' at the beginning of lines to enable settings
# 2. Restart the application after changing environment variables
# 3. Some settings can be overridden in the application UI
# 4. Keep your API keys secure and never commit them to version control
# 5. For production deployment, use a proper secrets management system
