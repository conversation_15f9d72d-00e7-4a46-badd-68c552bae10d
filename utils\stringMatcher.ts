// utils/stringMatcher.ts

// Calculates Levenshtein distance between two strings
export function calculateLevenshteinDistance(a: string, b: string): number {
    const s1 = a.toLowerCase();
    const s2 = b.toLowerCase();

    const dp: number[][] = Array(s1.length + 1).fill(null).map(() => Array(s2.length + 1).fill(0));

    for (let i = 0; i <= s1.length; i++) dp[i][0] = i;
    for (let j = 0; j <= s2.length; j++) dp[0][j] = j;

    for (let i = 1; i <= s1.length; i++) {
        for (let j = 1; j <= s2.length; j++) {
            const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
            dp[i][j] = Math.min(
                dp[i - 1][j] + 1,      // Deletion
                dp[i][j - 1] + 1,      // Insertion
                dp[i - 1][j - 1] + cost // Substitution
            );
        }
    }
    return dp[s1.length][s2.length];
}

// Calculates similarity score between 0 and 1
export function calculateSimilarity(s1: string, s2: string): number {
    const distance = calculateLevenshteinDistance(s1, s2); // Levenshtein is already case-insensitive due to its implementation
    const longerLength = Math.max(s1.length, s2.length);
    if (longerLength === 0) return 1; // Both strings are empty
    return 1 - (distance / longerLength);
}

export const FUZZY_MATCH_THRESHOLD = 0.75; // Threshold for considering a fuzzy match valid

export interface MatchedChoice {
    choice: string; // The original text of the matched choice from availableChoices
    score: number;
    type: 'exact' | 'fuzzy';
}

/**
 * Finds the best match for a player's input from a list of available choices.
 * Prioritizes exact matches (case-insensitive), then falls back to fuzzy matching.
 * @param playerInput The raw input from the player.
 * @param availableChoices An array of strings representing the currently available choices.
 * @returns A MatchedChoice object if a suitable match is found, otherwise null.
 */
export function findBestChoiceMatch(
    playerInput: string,
    availableChoices: string[]
): MatchedChoice | null {
    if (!playerInput.trim() || !availableChoices || availableChoices.length === 0) {
        return null;
    }

    const trimmedInputLower = playerInput.trim().toLowerCase();
    let bestFuzzyMatch: MatchedChoice | null = null;

    for (const choice of availableChoices) {
        if (choice.toLowerCase() === trimmedInputLower) {
            // Exact match (case-insensitive) found, prioritize this
            return { choice, score: 1, type: 'exact' };
        }

        // Calculate similarity for fuzzy matching
        const similarity = calculateSimilarity(trimmedInputLower, choice.toLowerCase());

        if (similarity >= FUZZY_MATCH_THRESHOLD) {
            if (!bestFuzzyMatch || similarity > bestFuzzyMatch.score) {
                bestFuzzyMatch = { choice, score: similarity, type: 'fuzzy' };
            }
        }
    }
    
    // If no exact match was found, return the best fuzzy match (if any)
    return bestFuzzyMatch;
}