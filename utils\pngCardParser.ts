
// PNG parsing utility to extract 'chara' data from tEXt chunks

/**
 * Extracts character data (expected to be a base64 encoded JSON string)
 * from a 'chara' tEXt chunk in a PNG file.
 *
 * @param arrayBuffer The ArrayBuffer of the PNG file.
 * @returns The base64 encoded string if found, otherwise null.
 */
export const extractCharaDataFromPng = (arrayBuffer: ArrayBuffer): string | null => {
  const dataView = new DataView(arrayBuffer);

  // Verify PNG signature
  const PNG_SIGNATURE = [137, 80, 78, 71, 13, 10, 26, 10];
  for (let i = 0; i < PNG_SIGNATURE.length; i++) {
    if (dataView.getUint8(i) !== PNG_SIGNATURE[i]) {
      console.error("Invalid PNG signature.");
      return null;
    }
  }

  let offset = PNG_SIGNATURE.length;

  while (offset < dataView.byteLength) {
    const length = dataView.getUint32(offset, false); // Length is big-endian
    offset += 4;

    const typeChars = [];
    for (let i = 0; i < 4; i++) {
      typeChars.push(String.fromCharCode(dataView.getUint8(offset + i)));
    }
    const type = typeChars.join('');
    offset += 4;

    if (type === 'tEXt') {
      const chunkData = new Uint8Array(arrayBuffer, offset, length);
      let keyword = '';
      let text = ''; // This will be the base64 string

      // Find the null separator
      let nullSeparatorIndex = -1;
      for (let i = 0; i < length; i++) {
        if (chunkData[i] === 0) {
          nullSeparatorIndex = i;
          break;
        }
      }

      if (nullSeparatorIndex !== -1) {
        // Keyword is Latin-1. 'chara' is ASCII, so 'iso-8859-1' is appropriate.
        keyword = new TextDecoder('iso-8859-1').decode(chunkData.slice(0, nullSeparatorIndex));
        // Text (base64 string) is Latin-1. Base64 characters are ASCII, so this is safe and spec-compliant.
        text = new TextDecoder('iso-8859-1').decode(chunkData.slice(nullSeparatorIndex + 1));
      } else {
        // No null separator, could be a malformed tEXt chunk if data is expected after keyword,
        // or the entire chunk is the keyword.
        // For 'chara' data, we expect a separator. If not found, log a warning.
        console.warn("PNG tEXt chunk format warning: No null separator found. Chunk data:", new TextDecoder('iso-8859-1').decode(chunkData));
        keyword = new TextDecoder('iso-8859-1').decode(chunkData); // Interpret whole chunk as keyword
      }
      
      if (keyword === 'chara') {
        return text; // text is the base64 string
      }
    } else if (type === 'IEND') {
      break; // End of PNG
    }

    offset += length + 4; // Skip data and CRC
  }

  return null; // 'chara' tEXt chunk not found
};
