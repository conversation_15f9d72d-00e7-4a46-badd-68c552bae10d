

import { Content } from "@google/genai";

export type { Content }; // Re-export Content

export interface DialogueLine {
  id: string;
  speakerName: string;
  speakerType: 'npc' | 'player' | 'narrator';
  text: string;
  processedHtml: string; // Added for pre-rendered HTML
  timestamp: number;
  storyUpdate?: string;
  storyUpdateForSummary?: string;
}

export interface Character {
  id:string;
  name: string;
  type: 'player' | 'npc';
  portraitKeyword?: string;
  description?: string;
}

export interface InventoryItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
}

export interface VisitedLocation {
  id: string;
  name: string;
  description: string;
  firstVisited: number;
  notes?: string;
}

export interface Quest {
  id: string;
  title: string;
  description: string;
  objectives: string[];
  status: 'active' | 'completed' | 'failed';
  lastUpdated: number;
}

export interface CharacterProfile {
  id: string;
  name: string;
  description: string;
  relationshipLevel: number;
  notableInteractions: string[];
  firstEncountered: number;
}

// New RPG Elements
export interface CoreAttributes {
  strength: number;    // STR: Physical power, combat prowess
  agility: number;     // AGI: Speed, reflexes, evasion
  intelligence: number;// INT: Problem-solving, knowledge, magic affinity
  charisma: number;    // CHA: Social skills, persuasion, leadership
  luck: number;        // LCK: Chance, finding rare items, critical hits
  sanity: number;      // SAN: Mental fortitude, resistance to fear/madness
}

export interface Skill {
  id: string; // e.g., 'persuasion', 'observation', 'empathy'
  name: string;
  level: number;
  description: string;
  xpToNextLevel?: number; // Optional: skills can also level up
  currentXp?: number;     // Optional: skill-specific XP
}

export interface ImportantEvent {
  id: string;
  text: string; // The content of the event/clue
  timestamp: number;
  category?: 'clue' | 'intel' | 'note' | 'lore'; // Optional categorization
  source?: string; // Optional: e.g., "Dialogue with Yuki", "Found document"
}

export interface StatusEffect {
  id: string; // Unique identifier for this specific instance of the effect
  name: string; // Display name, e.g., "力量祝福", "中毒"
  description?: string; // Optional detailed description
  icon?: string; // Optional emoji or SVG path for an icon (e.g., "💪", "☠️")
  durationTurns: number; // Total duration in game turns. -1 for permanent or until removed by tag.
  remainingTurns: number; // Countdown for turns.
  timestampApplied: number; // Timestamp when the effect was applied.
  source?: string; // Origin of the effect, e.g., "Potion of Strength", "Enemy Poison Spell"
  isBuff: boolean; // True if it's a buff, false if it's a debuff
}

export interface PlayerStatus {
  name?: string; // Player's character name
  mood: string;
  timeOfDay: string;
  weather?: string;
  healthEnergy: { current: number; max: number };
  relationshipYuki?: number; // Example, can be more generic
  specialEffects?: string[]; // Keep for general, non-timed effects or legacy
  currentDay: number; 

  inventory: InventoryItem[];
  visitedLocations: VisitedLocation[];
  quests: Quest[];
  characterProfiles: CharacterProfile[];
  importantEvents: ImportantEvent[];

  // RPG Additions
  level: number;
  xp: number;
  xpToNextLevel: number;
  attributePoints: number; 
  skillPoints: number;     
  coreAttributes: CoreAttributes;
  skills: Skill[];

  // Buffs and Debuffs
  buffs: StatusEffect[];
  debuffs: StatusEffect[];
}

// --- Updated Custom Narrative Element Structure ---
export interface CustomNarrativeSubElement {
  id: string;
  key: string;    // Name/Title of the sub-element (e.g., "乐子人面板html样式")
  value: string;  // Definition/Content of the sub-element
  isActive: boolean; // Individual toggle for this sub-element
}

export interface CustomNarrativePrimaryElement {
  id: string;
  name: string; // Name of the Element Set (e.g., "乐子人系统")
  isActive: boolean; // Master toggle for this entire set and its sub-elements
  subElements: CustomNarrativeSubElement[];
}
// --- End of Updated Structure ---

// Character Card data structure
export interface CharacterCardData {
  characterName: string;
  characterDescription: string; 
  characterOpeningMessage: string;
  characterPersonality: string; 
  characterScenario: string;
  characterExampleDialogue: string;
  characterPortraitKeywords: string; 
}

// Regex Replacement Types
export type RegexRuleScope = 'input' | 'output' | 'all';

export interface RegexRule {
  id: string;
  name: string; // Added: Rule name for user identification
  pattern: string;
  replacement: string;
  flags: string; // e.g., "gi", "g", "i"
  scope: RegexRuleScope;
  isActive: boolean;
  isDisplayOnly?: boolean; // Added: If true, only affects displayed text, not text sent to AI or used for logic
  trimInput?: string; // Added: Textarea for strings to trim from match before replacement
}

export interface GameSettingsData extends CharacterCardData { 
  userRole: string;
  systemRole: string;
  customNarrativeElements: CustomNarrativePrimaryElement[];
  selectedModelId: string;
  selectedImagePromptStyleId: string;
  selectedSummaryModelId: string;
  chatInterfaceOpacity: number;
  dialogueBubbleOpacity: number;
  dialogueBlur: number;
  fontSizeScale: number;
  enableBackdropBlur: boolean;
  enableImageGeneration: boolean;
  minOutputChars: number;
  maxOutputChars: number;
  imageGenerationInterval: number;
  enableStreamMode?: boolean;
  enablePseudoStreamMode?: boolean; 

  githubPat?: string; 
  gistId?: string;    
  saveGithubPat?: boolean; 
  enableGistAutoBackup: boolean; 
  gistAutoBackupIntervalHours: number; 
  gistUseSystemProxy: boolean; 

  // Regex Replacement Settings
  enableRegexReplacement: boolean;
  regexRules: RegexRule[];
}

export interface GameSaveData extends GameSettingsData {
  id: string;
  name: string;
  timestamp: number;
  playerName: string;
  dialogueLog: DialogueLine[];
  currentSceneImageKeyword: string;
  currentPlayerStatus: PlayerStatus;
  geminiChatHistory: Content[];
}

export interface GeminiResponseFormat {
  dialogue: string;
  speakerName: string;
  speakerType: 'npc' | 'player' | 'narrator';
  sceneImageKeyword: string;
  choices?: string[];
  storyUpdate?: string;
  mood: string;
  timeOfDay: string;
}

export enum GamePhase {
  StartScreen,
  Playing,
  Paused,
}

export enum Theme {
  Light = 'light',
  Dark = 'dark',
  Sakura = 'sakura',
  Starry = 'starry',
  Candy = 'candy',
  Forest = 'forest',
}

export interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  getThemeName: (theme: Theme) => string;
}

export interface ImageGenerationApiConfig {
  url: string;
  apiKeyEnvVar?: string;
  defaultStyleTags: string[];
  resolution: string;
  mobileResolution: string;
  negativePrompt: string;
  model?: string;
  seed?: string;
}

export type PresetType = 'userRole' | 'systemRole' | 'characterCard';

export interface SettingPreset {
  id: string;
  name: string;
  value: string | CustomNarrativePrimaryElement[] | CharacterCardData; 
  type: PresetType;
}

export interface AvailableModel {
  id: string;
  name: string;
}

export interface AvailableImagePromptStyle {
    id: string;
    name: string;
}

export type NotificationType = 'success' | 'error' | 'info' | 'warning' | 'achievement';

export interface NotificationMessage {
  id: string;
  message: string;
  type: NotificationType;
  duration?: number;
  icon?: React.ReactNode;
  title?: string;
}

export interface RPGNotification extends Omit<NotificationMessage, 'id' | 'icon'> {
    displayToUser?: boolean; 
}


export interface NotificationContextType {
  notifications: NotificationMessage[];
  addNotification: (message: string, type: NotificationType, duration?: number, title?: string) => void;
  removeNotification: (id: string) => void;
}

export type AchievementCategory =
  | '万象初窥' 
  | '命运织锦' 
  | '秘境行者' 
  | '我即风格' 
  | '魂之器量' 
  | '技艺臻境' 
  | '尘世羁绊' 
  | '逆境砺刃'; 

export type AchievementTier =
  | "青铜印记" 
  | "白银之辉" 
  | "黄金之冠" 
  | "史诗刻痕" 
  | "不朽传说"; 

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  tier: AchievementTier;
}

export interface UserPreferences {
  fontSizeScale: number;
  unlockedAchievements: Record<string, number>;
}

export type OpeningLineStyle = 'comedy' | 'horror';

export interface BackupData {
  version: string;
  exportedAt: number;
  gameSaves: GameSaveData[];
  gameSettings: GameSettingsData;
  userPreferences: UserPreferences;
  theme: Theme;
  userRolePresets: SettingPreset[];
  aiStylePresets: SettingPreset[]; 
  characterCardPresets: SettingPreset[];
  openingLineHistory: string[];
  lastOpeningStyle: OpeningLineStyle | null;
  cachedBackgrounds: string[];
}

export enum LocalStorageKeys {
  GAME_SAVES = 'memoryAbleGameSaves_v3_zh',
  GAME_SETTINGS = 'memoryAbleGameSettings_v3_zh',
  USER_PREFERENCES = 'memoryAbleUserPreferences_v1_zh',
  THEME = 'memoryAbleTheme_v1_zh',
  OPENING_LINE_HISTORY = 'memoryAbleOpeningLineHistory_v1',
  LAST_OPENING_STYLE = 'memoryAbleLastOpeningStyle_v1',
  USER_ROLE_PRESETS = 'memoryAbleUserRolePresets_v1',
  AI_STYLE_PRESETS = 'memoryAbleAiStylePresets_v1',
  CUSTOM_NARRATIVE_ELEMENTS_PRESETS = 'memoryAbleCustomElementsPresets_v1',
  CHARACTER_CARD_PRESETS = 'memoryAbleCharacterCardPresets_v1',
  CACHED_BACKGROUNDS = 'memoryAbleCachedBackgrounds_v1',
  LAST_SESSION_DATA = 'memoryAbleLastSessionData_v1',
}

export interface SillyTavernCharCardEntry { 
    keys: string[];
    comment: string;
    content: string;
    selective?: boolean;
    constant?: boolean;
    position?: "before_char" | "after_char" | "after_story";
    disable?: boolean;
}
export interface SillyTavernCharBook {
    name?: string;
    description?: string;
    scan_depth?: number;
    token_budget?: number;
    recursive_scanning?: boolean;
    entries: SillyTavernCharCardEntry[];
}


export interface SillyTavernCharCard {
  name?: string;
  description?: string;
  personality?: string;
  scenario?: string;
  first_mes?: string;
  mes_example?: string;
  char_name?: string; 
  char_persona?: string; 
  char_greeting?: string; 
  world_scenario?: string; 
  example_dialogue?: string; 
  tags?: string[]; 
  character_book?: SillyTavernCharBook; 
  lorebook?: SillyTavernCharBook; 
  entries?: Record<string, SillyTavernEntry> | SillyTavernCharCardEntry[]; 
}

export interface SillyTavernEntry { 
    key?: string[]; 
    keysecondary?: string[];
    comment?: string;
    content?: string;
    disable?: boolean;
}

export interface GistFile {
  filename: string;
  type: string;
  language: string;
  raw_url: string;
  size: number;
  truncated?: boolean;
  content?: string;
}

export interface GistOwner {
  login: string;
  id: number;
  avatar_url: string;
  html_url: string;
}

export interface GistApiResponse {
  url: string;
  forks_url: string;
  commits_url: string;
  id: string;
  node_id: string;
  git_pull_url: string;
  git_push_url: string;
  html_url: string;
  files: { [filename: string]: GistFile };
  public: boolean;
  created_at: string;
  updated_at: string;
  description: string | null;
  comments: number;
  user: GistOwner | null; 
  owner?: GistOwner; 
  truncated?: boolean;
}

export interface GistCreatePayload {
  description?: string;
  public?: boolean;
  files: {
    [filename: string]: {
      content: string;
    };
  };
}

export interface GistUpdatePayload {
  description?: string;
  files: {
    [filename: string]: {
      content?: string; 
      filename?: string; 
    } | null; 
  };
}

export type DetailModalType = 'inventory' | 'locations' | 'quests' | 'characters' | 'importantEvents';

// Types for RPG Tag Parsing in usePlayerStatus
export type RPGAttributeChangeParams = { attrKey: keyof CoreAttributes; changeValue: number; reason: string };
export type RPGSkillUpdateParams = { skillId: string; newLevel: number; reason: string };
export type RPGSkillXPGainParams = { skillId: string; xpAmount: number; reason: string };
export type RPGAwardPointsParams = { amount: number; reason: string };
export type RPGHealthEnergyChangeParams = { change: number; reason: string };
export type RPGHealthEnergySetParams = { current: number; max: number; reason: string };
export type RPGSpecialEffectParams = { effectName: string; duration?: string; description?: string };
export type StatusUpdateKeyValueParams = { key: keyof PlayerStatus | 'currentDay' | 'mood' | 'timeOfDay' | 'weather'; value: string }; // Made key more specific
export type RPGEffectAddParams = { type: 'buff' | 'debuff'; name: string; duration: number; description?: string; icon?: string; source?: string; };