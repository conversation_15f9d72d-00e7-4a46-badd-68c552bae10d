
// App.tsx

import React, { useState, useEffect, useCallback, useContext, useRef } from 'react';
import { 
    GameSaveData, GameSettingsData, UserPreferences, NotificationType, OpeningLineStyle, 
    GamePhase, Theme, PlayerStatus, PlayerStatus_Session, CustomNarrativePrimaryElement, 
    SettingPreset, PresetType, DialogueLine, Content, CharacterCardData, 
    SillyTavernCharCard, SillyTavernCharCardEntry, SillyTavernEntry, 
    RegexRule, DetailModalType, Achievement, GeminiResponseFormat 
} from './types';
import { 
    AppTitleStyled, UIText, DefaultGameSettings, IMAGE_GENERATION_API_CONFIG, 
    START_SCREEN_BACKGROUND_KEYWORDS, APP_TITLE_CN, Icons, 
    GEMINI_MODEL_TEXT_FALLBACK, AUTO_SUMMARY_TURN_INTERVAL_RPG, AVAILABLE_ACHIEVEMENTS, InitialPlayerStatus 
} from './constants';

import ChatInterface from './components/ChatInterface';
import ImageDisplay from './components/ImageDisplay';
import SidebarMenu, { ActiveTab as SidebarActiveTabType } from './components/SidebarMenu';
import { RPGStatusPanel } from './rpgSystem/components/RPGStatusPanel'; 
import StartScreen from '@/components/StartScreen';
import NotificationsContainer from './components/NotificationsContainer';
import ConfirmationModal from './components/ConfirmationModal';
import InventoryModal from './components/InventoryModal';
import LocationsModal from './components/LocationsModal';
import QuestsModal from './components/QuestsModal';
import CharactersModal from './components/CharactersModal';
import ImportantEventsModal from './components/ImportantEventsModal';
import Dropdown from './components/Dropdown'; 

import { ThemeContext, ThemeContextType } from './contexts/ThemeContext';
import { NotificationContext, NotificationContextType as AppNotificationContextType } from './contexts/NotificationContext';
import { useGameSettings } from './hooks/useGameSettings';
import { useUserPreferences } from './hooks/useUserPreferences';

import { useGameSaves } from './hooks/useGameSaves';
import { useRPGSystem } from './rpgSystem/hooks/useRPGSystem'; 
import { PlayerStatus_RPG, RPGNotificationDetails, CoreAttributes_RPG as CoreAttributes_RPG_Type, RPGProcessingResult } from './rpgSystem/types_rpg';
import { InitialPlayerStatus_RPG } from './rpgSystem/constants_rpg'; 

import { useOpeningSequence } from './hooks/useOpeningSequence';
import { usePresets } from './hooks/usePresets';
import { useUIState } from './hooks/useUIState';
import { useGameSession } from './hooks/useGameSession';
import { useDataManagement } from './hooks/useDataManagement';

import { renderRichTextStatic } from './utils/textUtils';
import { convertDialogueLogToGeminiHistory } from './utils/geminiUtils';
import { summarizeStoryForContinuation } from './services/geminiService';
import { extractCharaDataFromPng } from './utils/pngCardParser';


export const App: React.FC = () => {
  const { gameSettings, setGameSettings, persistGameSettings } = useGameSettings();
  
  const notificationCtx = useContext(NotificationContext) as AppNotificationContextType;
  if (!notificationCtx || !notificationCtx.addNotification) {
    throw new Error("NotificationContext not found in App or is not providing expected value.");
  }
  const { addNotification: addNotificationGlobal } = notificationCtx;

  const { userPreferences, setUserPreferences, persistUserPreferences, unlockAchievement: unlockAchievementGlobal, defaultUserPreferences } = useUserPreferences(addNotificationGlobal);
  
  const themeCtx = useContext(ThemeContext) as ThemeContextType;
  if (!themeCtx || typeof themeCtx.theme === 'undefined' || typeof themeCtx.setTheme === 'undefined') {
     throw new Error("ThemeContext not found in App or is not providing expected value.");
  }
  const { theme, setTheme } = themeCtx;

  const { 
    sidebarActiveTab, setSidebarActiveTab,
    isSidebarOpen, setIsSidebarOpen: setIsSidebarOpenFromHook, toggleSidebar: toggleSidebarFromHook, 
    isStatusPanelOpen, statusPanelRef, setIsStatusPanelOpen,
    confirmationModalState, handleRequestConfirmation, closeConfirmationModal,
    activeDetailModal, setActiveDetailModal, closeDetailModal,
    isRenamingHeaderTitle, headerRenameValue, headerRenameInputRef,
    handleHeaderTitleClick, handleConfirmHeaderRename: handleConfirmHeaderRenameFromHook, 
    handleCancelHeaderRename: handleCancelHeaderRenameFromHook,   
    handleHeaderRenameKeyDown: handleHeaderRenameKeyDownFromHook, 
    setIsRenamingHeaderTitle, setHeaderRenameValue,
  } = useUIState(addNotificationGlobal);

  const [compositePlayerStatus, setCompositePlayerStatus] = useState<PlayerStatus>(InitialPlayerStatus);

  const handleRPGNotifications = useCallback((notifications: RPGNotificationDetails[]) => {
    notifications.forEach(n => { if (n.displayToUser) addNotificationGlobal(n.message, n.type, n.duration, n.title); });
  }, [addNotificationGlobal]);

  const handleRPGAchievementsUnlock = useCallback((achievementIds: string[]) => {
    achievementIds.forEach(id => {
      const achievement: Achievement | undefined = AVAILABLE_ACHIEVEMENTS.find(ach => ach.id === id);
      if (achievement) unlockAchievementGlobal(id);
      else console.warn(`Attempted to unlock non-existent achievement ID: ${id}`);
    });
  }, [unlockAchievementGlobal]);
  
  const rpgSystem = useRPGSystem(handleRPGNotifications, handleRPGAchievementsUnlock, setActiveDetailModal);
  
  const { 
    gameSaves, currentQuickSaveId, saveGame, updateSave, 
    loadGame: loadGameFromHook, deleteSave, renameSave, 
    setCurrentQuickSaveId, setGameSaves: setGameSavesFromHook, 
  } = useGameSaves(gameSettings, userPreferences.unlockedAchievements, unlockAchievementGlobal, addNotificationGlobal);
  
  const { 
    dynamicOpeningLine, fetchAndSetOpeningLine, openingLineHistory, 
    lastOpeningStyle, setOpeningLineHistory, setLastOpeningStyle    
  } = useOpeningSequence();
  
  const {
    characterCardPresets, userRolePresets, aiStylePresets, 
    savePreset, loadPreset, deletePreset,
    setCharacterCardPresets, setUserRolePresets, setAiStylePresets, 
  } = usePresets(gameSettings, setGameSettings, addNotificationGlobal, unlockAchievementGlobal);

  const processAndApplyAiTurnResults = useCallback((geminiResponse: GeminiResponseFormat) => {
    setCompositePlayerStatus(prevStatus => {
      const oldTimeOfDayFromSession = prevStatus.timeOfDay; 
      const newTimeOfDayFromAI = geminiResponse.timeOfDay;

      const rpgProcessingResult = rpgSystem.processStoryUpdateRPG(
        prevStatus, 
        geminiResponse.storyUpdate,
        newTimeOfDayFromAI,
        oldTimeOfDayFromSession
      );

      // Callbacks for notifications and achievements are now handled inside rpgSystem.processStoryUpdateRPG
      // or via the callbacks passed to useRPGSystem constructor if those notifications are specific to useRPGSystem's direct actions.
      // For results from processStoryUpdateRPG, handle them here:
      if (rpgProcessingResult.notifications.length > 0) handleRPGNotifications(rpgProcessingResult.notifications);
      if (rpgProcessingResult.achievementsToUnlock.length > 0) handleRPGAchievementsUnlock(rpgProcessingResult.achievementsToUnlock);
      
      return {
        ...prevStatus, // Start with the full previous state
        ...rpgProcessingResult.statusChangesDelta, // Apply RPG deltas
        name: prevStatus.name, // Ensure session name is preserved if not in delta
        mood: geminiResponse.mood || prevStatus.mood,
        timeOfDay: newTimeOfDayFromAI || prevStatus.timeOfDay,
        weather: geminiResponse.weather || prevStatus.weather,
      };
    });
  }, [rpgSystem, handleRPGNotifications, handleRPGAchievementsUnlock]);


  const handleSummarizeAllForSession = useCallback(async (dialogueLogForSummary: DialogueLine[], summaryModelId: string) => {
    if (Object.values(rpgSystem.isSummarizingRPG).some(status => status)) return;
    addNotificationGlobal(UIText.summarizingInfo, 'info', 2500);

    const updateAndNotify = (delta: Partial<PlayerStatus_RPG>) => {
        setCompositePlayerStatus(prev => ({ ...prev, ...delta }));
    };

    await Promise.all([
        rpgSystem.handleSummarizeInventoryAndShowDetailsRPG(dialogueLogForSummary, summaryModelId, compositePlayerStatus, updateAndNotify),
        rpgSystem.handleSummarizeLocationsAndShowDetailsRPG(dialogueLogForSummary, summaryModelId, compositePlayerStatus, updateAndNotify),
        rpgSystem.handleSummarizeQuestsAndShowDetailsRPG(dialogueLogForSummary, summaryModelId, compositePlayerStatus, updateAndNotify),
        rpgSystem.handleSummarizeCharactersAndShowDetailsRPG(dialogueLogForSummary, summaryModelId, compositePlayerStatus, updateAndNotify),
        rpgSystem.handleSummarizeImportantEventsAndShowDetailsRPG(dialogueLogForSummary, summaryModelId, compositePlayerStatus, updateAndNotify),
    ]);
    addNotificationGlobal(UIText.statusAutoUpdated, 'success', 2000);
  }, [rpgSystem, compositePlayerStatus, addNotificationGlobal]);


  const {
    gamePhase, setGamePhase,
    playerName, setPlayerName, 
    dialogueLog, setDialogueLog,
    currentChoices, setCurrentChoices,
    isLoading, setIsLoading,
    currentSceneImageKeyword, setCurrentSceneImageKeyword,
    finalBackgroundUrl, setFinalBackgroundUrl,
    isLoadingBackground, setIsLoadingBackground,
    dialogueTurnCounterRef,
    initializeNewGame: initializeNewGameSession, 
    handleSendMessage: handleSendMessageFromSession, 
    handleDeleteDialogueLine,
    handleRegenerateResponse,
    editingLineId, currentlyEditedContent, handleStartEditLine,
    handleSaveEditedLine, handleCancelEditLine,
    handleCurrentlyEditedContentChange,
  } = useGameSession(
    gameSettings, 
    setGameSettings,
    compositePlayerStatus, 
    (newSessionStatusUpdate: PlayerStatus_Session) => { 
      setCompositePlayerStatus(prev => ({...prev, ...newSessionStatusUpdate}));
    },
    unlockAchievementGlobal, 
    addNotificationGlobal, 
    processAndApplyAiTurnResults, 
    handleSummarizeAllForSession, 
    InitialPlayerStatus 
  );
  
  const { 
    exportAllData, importAllData, handleResetAllSettingsToDefaults,
    handleInitializeGistBackup, handleBackupToGist, handleRestoreFromGist,
    isGistLoading,
  } = useDataManagement({
      gameSaves, gameSettings, userPreferences, theme, 
      characterCardPresets, userRolePresets, aiStylePresets, 
      openingLineHistory, lastOpeningStyle,   
      addNotification: addNotificationGlobal, unlockAchievement: unlockAchievementGlobal, handleRequestConfirmation,
      setGameSaves: setGameSavesFromHook, 
      setGameSettings, setUserPreferences, setTheme,
      setCharacterCardPresets, setUserRolePresets, setAiStylePresets, 
      setOpeningLineHistory, setLastOpeningStyle,   
      defaultUserPreferences
  });

  const initialDataFetchedOnStartScreenRef = useRef(false);
  const autoGistBackupIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const [isRestartDropdownOpen, setIsRestartDropdownOpen] = useState(false);
  const restartDropdownRef = useRef<HTMLDivElement>(null);

  const importCharCardFileRef = useRef<HTMLInputElement>(null);
  const importUserRoleFileRef = useRef<HTMLInputElement>(null);
  const importWorldBookFileRef = useRef<HTMLInputElement>(null);

  const prevGistAutoBackupEnabledRef = useRef<boolean | undefined>(gameSettings.enableGistAutoBackup);
  const prevGistAutoBackupIntervalRef = useRef<number | undefined>(gameSettings.gistAutoBackupIntervalHours);

  const persistAllSettings = useCallback(() => {
    persistGameSettings();
    persistUserPreferences();
  }, [persistGameSettings, persistUserPreferences]);

  useEffect(() => {
    const handleClickOutsideStatusPanel = (event: MouseEvent) => {
      if (isStatusPanelOpen && statusPanelRef.current && !statusPanelRef.current.contains(event.target as Node)) {
        const toggleButton = document.querySelector('header button[title="' + UIText.statusPanelToggle + '"]');
        if (toggleButton && toggleButton.contains(event.target as Node)) return;
        setIsStatusPanelOpen(false);
      }
    };
    if (isStatusPanelOpen) document.addEventListener('mousedown', handleClickOutsideStatusPanel);
    return () => document.removeEventListener('mousedown', handleClickOutsideStatusPanel);
  }, [isStatusPanelOpen, setIsStatusPanelOpen, statusPanelRef]);

  const startNewGame = useCallback(() => {
    if (!playerName.trim()) { addNotificationGlobal(UIText.storyStartError, 'error'); return; }
    const fullInitialStatus: PlayerStatus = { ...InitialPlayerStatus, name: playerName }; 
    setCompositePlayerStatus(fullInitialStatus); 
    initializeNewGameSession(playerName, gameSettings, fullInitialStatus); 
  }, [playerName, gameSettings, initializeNewGameSession, addNotificationGlobal, setCompositePlayerStatus]);

  const loadGame = useCallback((saveId: string) => {
    const saveData = loadGameFromHook(saveId);
    if (saveData) {
      const { currentPlayerStatus, playerName: savedPlayerName, dialogueLog: savedDialogueLog, currentSceneImageKeyword: savedSceneKeyword, ...gameSpecificSettings } = saveData;
      setCompositePlayerStatus(currentPlayerStatus); 
      initializeNewGameSession(savedPlayerName, gameSpecificSettings as GameSettingsData, currentPlayerStatus, savedDialogueLog, savedSceneKeyword);
    }
  }, [loadGameFromHook, initializeNewGameSession, setCompositePlayerStatus]);

  const restartFromSaveSettings = useCallback((saveId: string) => {
    const settingsSourceSave = gameSaves.find(s => s.id === saveId);
    if (settingsSourceSave) {
        handleRequestConfirmation(UIText.restartGameTooltip, `${UIText.restartGameConfirmMessage} 这将使用存档《${settingsSourceSave.name}》中的叙事设定。`, () => {
            const { currentPlayerStatus, dialogueLog, currentSceneImageKeyword, playerName: oldPlayerNameFromSave, ...relevantSaveData } = settingsSourceSave; 
            const fullInitialStatus: PlayerStatus = { ...InitialPlayerStatus, name: playerName }; 
            setCompositePlayerStatus(fullInitialStatus);
            initializeNewGameSession(playerName, relevantSaveData as GameSettingsData, fullInitialStatus); 
            addNotificationGlobal(`已使用《${relevantSaveData.name}》的设定重新开始游戏。`, "info");
        });
    } else { addNotificationGlobal("无法找到源存档的设定。", "error"); }
  }, [gameSaves, playerName, initializeNewGameSession, addNotificationGlobal, handleRequestConfirmation, setCompositePlayerStatus]);
  
  const handleQuickSaveGame = useCallback(async () => {
    const currentGeminiHistory = convertDialogueLogToGeminiHistory(dialogueLog);
    const statusToSave: PlayerStatus = { ...compositePlayerStatus }; 
    if (currentQuickSaveId) {
        if (gameSaves.find(s => s.id === currentQuickSaveId)) updateSave(currentQuickSaveId, playerName, dialogueLog, currentSceneImageKeyword, statusToSave, currentGeminiHistory);
        else { const newId = await saveGame(`${UIText.saveGame.split(' ')[0]} - ${new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`, playerName, dialogueLog, currentSceneImageKeyword, statusToSave, currentGeminiHistory); if (newId) setCurrentQuickSaveId(newId); }
    } else { const newId = await saveGame(`${UIText.saveGame.split(' ')[0]} - ${new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`, playerName, dialogueLog, currentSceneImageKeyword, statusToSave, currentGeminiHistory); if (newId) setCurrentQuickSaveId(newId); }
  }, [currentQuickSaveId, saveGame, updateSave, gameSaves, playerName, dialogueLog, currentSceneImageKeyword, compositePlayerStatus, setCurrentQuickSaveId]);

  const handleRestartGameRequest = useCallback(() => {
    handleRequestConfirmation(UIText.restartGameConfirmTitle, UIText.restartGameConfirmMessage, () => {
        const fullInitialStatus: PlayerStatus = { ...InitialPlayerStatus, name: playerName };
        setCompositePlayerStatus(fullInitialStatus);
        initializeNewGameSession(playerName, gameSettings, fullInitialStatus);
        addNotificationGlobal("游戏已使用当前设定重新开始。", "info");
    });
  }, [playerName, gameSettings, initializeNewGameSession, addNotificationGlobal, handleRequestConfirmation, setCompositePlayerStatus]);

  const handleDefaultRestartGameRequest = useCallback(() => {
    handleRequestConfirmation(UIText.restartWithDefaultSettingsConfirmTitle, UIText.restartWithDefaultSettingsConfirmMessage, () => {
        const fullInitialStatus: PlayerStatus = { ...InitialPlayerStatus, name: playerName };
        setCompositePlayerStatus(fullInitialStatus);
        initializeNewGameSession(playerName, { ...DefaultGameSettings }, fullInitialStatus);
        addNotificationGlobal("游戏已使用默认设定重新开始。", "info");
      }
    );
  }, [playerName, initializeNewGameSession, addNotificationGlobal, handleRequestConfirmation, setCompositePlayerStatus]);
  
  const handleOpenLoadMenu = useCallback(() => { setSidebarActiveTab('saveload'); setIsSidebarOpenFromHook(true); }, [setSidebarActiveTab, setIsSidebarOpenFromHook]);

  const handleRestartWithSummary = useCallback(async () => {
    handleRequestConfirmation(UIText.restartWithSummaryConfirmTitle, UIText.restartWithSummaryConfirmMessage, async () => {
        setIsLoading(true); 
        try {
            const statusForSummary: PlayerStatus = { ...InitialPlayerStatus, name: playerName };
            const summary = await summarizeStoryForContinuation(dialogueLog, statusForSummary, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK);
            setCompositePlayerStatus(statusForSummary);
            const initialDialog: DialogueLine[] = [{
                id: 'summary-restart-0', speakerName: UIText.narrator, speakerType: 'narrator',
                text: summary, processedHtml: renderRichTextStatic(summary), timestamp: Date.now(),
                storyUpdate: "游戏从AI生成的剧情摘要继续。"
            }];
            initializeNewGameSession(playerName, gameSettings, statusForSummary, initialDialog, START_SCREEN_BACKGROUND_KEYWORDS[0] );
            addNotificationGlobal("游戏已从AI生成的剧情摘要重新开始。", "success");
        } catch (error: any) {
            console.error("Error restarting with summary:", error);
            addNotificationGlobal(UIText.errorSummarizationFailed + (error.message ? `: ${error.message}` : ''), "error", 7000);
        } finally { setIsLoading(false); }
    });
  }, [playerName, gameSettings, dialogueLog, initializeNewGameSession, addNotificationGlobal, handleRequestConfirmation, setIsLoading, setCompositePlayerStatus]);

  const currentSaveForHeader = gameSaves.find(s => s.id === currentQuickSaveId);
  const displayHeaderTitle = (gamePhase === GamePhase.Playing && currentSaveForHeader) ? currentSaveForHeader.name : APP_TITLE_CN;
  const canSpendPoints = (compositePlayerStatus.attributePoints || 0) > 0 || (compositePlayerStatus.skillPoints || 0) > 0;

  useEffect(() => {
    if (gamePhase === GamePhase.StartScreen && !initialDataFetchedOnStartScreenRef.current) {
      fetchAndSetOpeningLine(); initialDataFetchedOnStartScreenRef.current = true;
    }
  }, [gamePhase, fetchAndSetOpeningLine]);
  
  const handleSidebarClose = useCallback(() => {
    persistAllSettings(); setIsSidebarOpenFromHook(false);
  }, [setIsSidebarOpenFromHook, persistAllSettings]);

  const { enableGistAutoBackup, gistAutoBackupIntervalHours, githubPat, gistId } = gameSettings;
  useEffect(() => {
    if (autoGistBackupIntervalRef.current) { clearInterval(autoGistBackupIntervalRef.current); autoGistBackupIntervalRef.current = null; }
    const canSetupAutoBackup = gamePhase === GamePhase.Playing && enableGistAutoBackup && gistAutoBackupIntervalHours && gistAutoBackupIntervalHours > 0 && githubPat && gistId;
    if (canSetupAutoBackup) {
      const justEnabled = prevGistAutoBackupEnabledRef.current === false && enableGistAutoBackup === true;
      const intervalChangedWhileEnabled = enableGistAutoBackup === true && prevGistAutoBackupIntervalRef.current !== undefined && prevGistAutoBackupIntervalRef.current !== gistAutoBackupIntervalHours;
      if (justEnabled || intervalChangedWhileEnabled) addNotificationGlobal(UIText.gistAutoBackupInfo(gistAutoBackupIntervalHours), 'success', 5000);
      const intervalMilliseconds = gistAutoBackupIntervalHours * 60 * 60 * 1000;
      const performAutoBackup = async () => { if (isGistLoading) return; addNotificationGlobal(UIText.gistAutoBackupInfo(gistAutoBackupIntervalHours), 'info', 4000); await handleBackupToGist(true); };
      autoGistBackupIntervalRef.current = setInterval(performAutoBackup, intervalMilliseconds);
    } else if (enableGistAutoBackup && (!githubPat || !gistId || !gistAutoBackupIntervalHours || gistAutoBackupIntervalHours <= 0)) {
      addNotificationGlobal(UIText.gistAutoBackupConfigNeeded, 'warning', 7000);
    }
    prevGistAutoBackupEnabledRef.current = enableGistAutoBackup; prevGistAutoBackupIntervalRef.current = gistAutoBackupIntervalHours;
    return () => { if (autoGistBackupIntervalRef.current) clearInterval(autoGistBackupIntervalRef.current); };
  }, [ gamePhase, enableGistAutoBackup, gistAutoBackupIntervalHours, githubPat, gistId, handleBackupToGist, addNotificationGlobal, isGistLoading ]);

  useEffect(() => {
    const handleClickOutsideRestartDropdown = (event: MouseEvent) => {
      if (isRestartDropdownOpen && restartDropdownRef.current && !restartDropdownRef.current.contains(event.target as Node)) {
        const triggerButton = document.getElementById('restart-game-button-trigger');
        if (triggerButton && triggerButton.contains(event.target as Node)) return;
        setIsRestartDropdownOpen(false);
      }
    };
    if (isRestartDropdownOpen) document.addEventListener('mousedown', handleClickOutsideRestartDropdown);
    return () => document.removeEventListener('mousedown', handleClickOutsideRestartDropdown);
  }, [isRestartDropdownOpen]);

  const toggleRestartDropdown = () => setIsRestartDropdownOpen(prev => !prev);
  const selectRestartOption = (action: () => void) => { action(); setIsRestartDropdownOpen(false); };

  useEffect(() => {
    const rootEl = document.documentElement;
    if (!gameSettings.enableImageGeneration) rootEl.classList.add('no-image-bg');
    else rootEl.classList.remove('no-image-bg');
  }, [gameSettings.enableImageGeneration]);

  const getFilenameWithoutExtension = (filename: string): string => filename.replace(/\.(jsonc?|png)$/i, '');
  const importWorldBookEntries = useCallback(( entries: SillyTavernCharCardEntry[] | Record<string, SillyTavernEntry>, sourceName: string, importSourceType: 'character_book' | 'lorebook' | 'direct_entries_array' | 'direct_entries_object_map' ) => {
      const worldBookSetName = sourceName; const newPrimaryElement: CustomNarrativePrimaryElement = { id: `primary_wbimport_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`, name: worldBookSetName, isActive: true, subElements: [] };
      let subElementIndex = 0;
      const processEntry = (entryData: SillyTavernCharCardEntry | SillyTavernEntry, objectKeyName?: string) => {
          const keySource = (entryData as SillyTavernCharCardEntry).keys || (entryData as SillyTavernEntry).key;
          const subElementKey = entryData.comment || (keySource && keySource.length > 0 ? keySource[0] : (objectKeyName || `规则 ${subElementIndex + 1}`));
          newPrimaryElement.subElements.push({ id: `sub_wbimport_${Date.now()}_${subElementIndex++}_${Math.random().toString(36).substring(2,5)}`, key: subElementKey.trim(), value: (entryData.content || '').trim(), isActive: typeof entryData.disable === 'boolean' ? !entryData.disable : true });
      };
      if (Array.isArray(entries)) (entries as Array<SillyTavernCharCardEntry | SillyTavernEntry>).forEach(entry => processEntry(entry));
      else for (const entryId in entries) if (Object.prototype.hasOwnProperty.call(entries, entryId)) processEntry(entries[entryId], entryId);
      if (newPrimaryElement.subElements.length > 0) {
          setGameSettings(prevSettings => ({ ...prevSettings, customNarrativeElements: [...(prevSettings.customNarrativeElements || []), newPrimaryElement] }));
          const sourceDetailMap = {'character_book': "character_book 区块", 'lorebook': "lorebook 区块", 'direct_entries_array': "直接 entries 数组", 'direct_entries_object_map': "直接 entries 对象"};
          addNotificationGlobal(`${UIText.sillyTavernCharCardWorldBookImportSuccess(sourceName.split(' ')[0].replace('[','').replace(']',''), newPrimaryElement.name)} (源自: ${sourceDetailMap[importSourceType] || importSourceType})`, "success", 6000);
      } else { addNotificationGlobal(UIText.sillyTavernCharCardWorldBookImportNotFound(sourceName), "info"); }
  }, [setGameSettings, addNotificationGlobal]);

  const processImportedCharacterCardData = useCallback((cardData: SillyTavernCharCard, fileName: string) => {
    const newCharCardSettings: Partial<CharacterCardData> = {
        characterName: cardData.name || cardData.char_name || gameSettings.characterName,
        characterDescription: (cardData.description || (cardData.char_persona && cardData.char_persona.length > (cardData.description || "").length ? cardData.char_persona : cardData.description)) || gameSettings.characterDescription,
        characterPersonality: (cardData.personality || (cardData.char_persona && cardData.char_persona !== (cardData.description || "") && cardData.personality ? cardData.personality : "")) || gameSettings.characterPersonality,
        characterOpeningMessage: cardData.first_mes || cardData.char_greeting || gameSettings.characterOpeningMessage,
        characterScenario: cardData.scenario || cardData.world_scenario || gameSettings.characterScenario,
        characterExampleDialogue: cardData.mes_example || cardData.example_dialogue || gameSettings.characterExampleDialogue,
        characterPortraitKeywords: ((cardData.name ? `${cardData.name}, ` : "") + (cardData.tags?.join(', ') || "")).trim().length > 0 ? ((cardData.name ? `${cardData.name}, ` : "") + (cardData.tags?.join(', ') || "")) : gameSettings.characterPortraitKeywords
    };
    const fullImportedCardData: CharacterCardData = { characterName: newCharCardSettings.characterName!, characterDescription: newCharCardSettings.characterDescription!, characterOpeningMessage: newCharCardSettings.characterOpeningMessage!, characterPersonality: newCharCardSettings.characterPersonality!, characterScenario: newCharCardSettings.characterScenario!, characterExampleDialogue: newCharCardSettings.characterExampleDialogue!, characterPortraitKeywords: newCharCardSettings.characterPortraitKeywords! };
    setGameSettings(prev => ({ ...prev, ...fullImportedCardData })); savePreset('characterCard', fullImportedCardData, getFilenameWithoutExtension(fileName));
    addNotificationGlobal(UIText.sillyTavernCharCardImportSuccess(fileName), "success");
    const charNameForWBSource = newCharCardSettings.characterName || fileName; let worldBookImported = false;
    if (cardData.character_book?.entries?.length) { importWorldBookEntries(cardData.character_book.entries, `[${charNameForWBSource}] ${UIText.worldBookSectionTitle}`, 'character_book'); worldBookImported = true; }
    else if (cardData.lorebook?.entries?.length) { importWorldBookEntries(cardData.lorebook.entries, `[${charNameForWBSource}] ${UIText.worldBookSectionTitle}`, 'lorebook'); worldBookImported = true; }
    else if (cardData.entries) {
        if (Array.isArray(cardData.entries) && cardData.entries.length > 0) { importWorldBookEntries(cardData.entries as SillyTavernCharCardEntry[], `[${charNameForWBSource}] ${UIText.worldBookSectionTitle}`, 'direct_entries_array'); worldBookImported = true; }
        else if (typeof cardData.entries === 'object' && !Array.isArray(cardData.entries) && Object.keys(cardData.entries).length > 0) { importWorldBookEntries(cardData.entries as Record<string, SillyTavernEntry>, `[${charNameForWBSource}] ${UIText.worldBookSectionTitle}`, 'direct_entries_object_map'); worldBookImported = true; }
    }
  }, [gameSettings, setGameSettings, addNotificationGlobal, importWorldBookEntries, savePreset]);

  const handleImportSillyTavernCharCard = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files; if (!files || files.length === 0) return;
    Array.from(files).forEach(file => {
        const reader = new FileReader(); const fileName = file.name;
        const processFileContent = (content: string | ArrayBuffer | null, isPng: boolean) => {
            try { let cardData: SillyTavernCharCard;
                if (isPng && content instanceof ArrayBuffer) {
                    const base64Data = extractCharaDataFromPng(content); if (!base64Data) { addNotificationGlobal(`${UIText.sillyTavernCharCardImportError} (${fileName}: PNG数据无效)`, "error"); return; }
                    const decodedJsonString = new TextDecoder('utf-8').decode(Uint8Array.from(atob(base64Data), c => c.charCodeAt(0))); cardData = JSON.parse(decodedJsonString);
                } else if (!isPng && typeof content === 'string') { cardData = JSON.parse(content); } else { throw new Error("无法读取文件内容"); }
                processImportedCharacterCardData(cardData, fileName);
            } catch (error: any) { addNotificationGlobal(`${UIText.sillyTavernCharCardImportError} (${fileName}: ${error.message})`, "error"); }
        };
        if (fileName.toLowerCase().endsWith('.png')) { reader.onload = (e) => processFileContent(e.target?.result as ArrayBuffer, true); reader.readAsArrayBuffer(file); }
        else if (fileName.toLowerCase().endsWith('.json') || fileName.toLowerCase().endsWith('.jsonc')) { reader.onload = (e) => processFileContent(e.target?.result as string, false); reader.readAsText(file, 'UTF-8'); }
        else { addNotificationGlobal(`${UIText.sillyTavernCharCardImportError} (${fileName}: 不支持的文件格式)`, "error"); }
    }); if (event.target) event.target.value = "";
  }, [processImportedCharacterCardData, addNotificationGlobal]);

  const handleImportUserRoleCharCard = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]; if (!file) return; const reader = new FileReader();
    const processCardDataForUserRole = (cardData: SillyTavernCharCard, fileName: string) => {
        let userRoleText = (cardData.description || cardData.char_persona || "").replace(/\{\{char\}\}/gi, "{{user}}").replace(/<CHAR>/gi, "{{user}}");
        if (cardData.personality?.trim()) userRoleText += (userRoleText ? "\n" : "") + `性格：${cardData.personality}`;
        if (cardData.first_mes?.trim()) userRoleText += (userRoleText ? "\n" : "") + `初次见面时可能会说：${cardData.first_mes}`;
        setGameSettings(prev => ({ ...prev, userRole: userRoleText.trim() })); savePreset('userRole', userRoleText.trim(), getFilenameWithoutExtension(fileName));
        addNotificationGlobal(`已从角色卡 [${fileName}] 的设定更新玩家角色描述。`, "success");
    };
    if (file.name.toLowerCase().endsWith('.png')) {
        reader.onload = (e) => { try { const data = extractCharaDataFromPng(e.target?.result as ArrayBuffer); if (data) processCardDataForUserRole(JSON.parse(new TextDecoder('utf-8').decode(Uint8Array.from(atob(data), c => c.charCodeAt(0)))), file.name); else throw new Error("PNG数据无效"); } catch (err:any) { addNotificationGlobal(UIText.sillyTavernCharCardImportError + ` (${err.message})`, "error"); } finally { if (event.target) event.target.value = ""; } };
        reader.readAsArrayBuffer(file);
    } else if (file.name.toLowerCase().endsWith('.json') || file.name.toLowerCase().endsWith('.jsonc')) {
        reader.onload = (e) => { try { processCardDataForUserRole(JSON.parse(e.target?.result as string), file.name); } catch (err:any) { addNotificationGlobal(UIText.sillyTavernCharCardImportError + ` (${err.message})`, "error"); } finally { if (event.target) event.target.value = ""; } };
        reader.readAsText(file, 'UTF-8');
    } else { addNotificationGlobal(UIText.sillyTavernCharCardImportError + " (不支持的文件格式)", "error"); if (event.target) event.target.value = ""; }
  }, [setGameSettings, addNotificationGlobal, savePreset]);

  const handleImportSillyTavernWorldBook = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files; if (!files || files.length === 0) return;
    Array.from(files).forEach(file => {
        const reader = new FileReader(); const fileName = file.name;
        if (fileName.toLowerCase().endsWith('.json')) {
            reader.onload = (e) => { try { const worldBookData = JSON.parse(e.target?.result as string); if (worldBookData?.entries && typeof worldBookData.entries === 'object') importWorldBookEntries(worldBookData.entries, getFilenameWithoutExtension(fileName), 'direct_entries_object_map'); else addNotificationGlobal(`${UIText.sillyTavernImportError} (${fileName}: 格式无效)`, "error"); }
                catch (error: any) { addNotificationGlobal(`${UIText.sillyTavernImportError} (${fileName}: ${error.message})`, "error"); } }; reader.readAsText(file, 'UTF-8');
        } else { addNotificationGlobal(`${UIText.sillyTavernImportError} (${fileName}: 请选择.json文件)`, "error"); }
    }); if (event.target) event.target.value = "";
  }, [addNotificationGlobal, importWorldBookEntries]);

  const globalImportDropdownItems = [
    { id: 'importCharCard', label: (<>{UIText.importSillyTavernCharCard}<small className="block text-xs text-secondary-themed/70 opacity-80 mt-0.5">(.json, .png, .jsonc)</small></>), icon: () => <span role="img" aria-label="Character Card">🃏</span>, onClick: () => importCharCardFileRef.current?.click() },
    { id: 'importUserRoleFromCard', label: (<>{UIText.importUserRoleFromCharCardButtonLabel}<small className="block text-xs text-secondary-themed/70 opacity-80 mt-0.5">(.json, .png, .jsonc)</small></>), icon: () => <span role="img" aria-label="User Settings">🧑‍🔧</span>, onClick: () => importUserRoleFileRef.current?.click() },
    { id: 'importWorldBook', label: (<>{UIText.importSillyTavernWorldBook}<small className="block text-xs text-secondary-themed/70 opacity-80 mt-0.5">(.json)</small></>), icon: () => <span role="img" aria-label="World Book">📚</span>, onClick: () => importWorldBookFileRef.current?.click() },
  ];

  const callAllocateAttributePoint = (attrKey: keyof CoreAttributes_RPG_Type) => {
    const result = rpgSystem.allocateAttributePointRPG(compositePlayerStatus, attrKey);
    setCompositePlayerStatus(prev => ({ ...prev, ...result.statusChangesDelta }));
  };
  const callAllocateSkillPoint = (skillId: string) => {
    const result = rpgSystem.allocateSkillPointRPG(compositePlayerStatus, skillId);
    setCompositePlayerStatus(prev => ({ ...prev, ...result.statusChangesDelta }));
  };

  if (gamePhase === GamePhase.StartScreen) {
    return (
      <StartScreen
        playerName={playerName} onPlayerNameChange={setPlayerName}
        dynamicOpeningLine={dynamicOpeningLine} finalBackgroundUrl={finalBackgroundUrl}
        isLoadingBackground={isLoadingBackground} onStartGame={startNewGame}
        gameSaves={gameSaves}
        onSaveGame={(name) => saveGame(name, playerName, dialogueLog, currentSceneImageKeyword, compositePlayerStatus, convertDialogueLogToGeminiHistory(dialogueLog))}
        onLoadGame={loadGame} onDeleteSave={deleteSave} onRenameSave={renameSave}
        addNotification={addNotificationGlobal} onRequestConfirmation={handleRequestConfirmation}
        gameSettings={gameSettings} userPreferences={userPreferences}
        onGameSettingsChange={setGameSettings} onUserPreferencesChange={setUserPreferences} 
        onExportData={exportAllData} onImportData={importAllData}
        onResetAllSettingsToDefaults={() => handleResetAllSettingsToDefaults()} 
      />
    );
  }

  const { name: currentSessionPlayerName, mood: currentSessionMood, timeOfDay: currentSessionTimeOfDay, weather: currentSessionWeather, ...playerStatusRPGPart } = compositePlayerStatus;

  return (
    <div className="app-container">
      <div className="absolute inset-0 -z-10"><ImageDisplay finalImageUrl={finalBackgroundUrl} /></div>
      <header className="app-header">
        <button onClick={toggleSidebarFromHook} className="menu-button" title={UIText.menu}><Icons.Menu className="w-7 h-7" /></button>
        <div className="app-title-header flex-grow text-center overflow-hidden">
          {isRenamingHeaderTitle && currentSaveForHeader ? (
            <div className="flex items-center justify-center max-w-xs mx-auto">
              <input ref={headerRenameInputRef} type="text" value={headerRenameValue} onChange={(e) => setHeaderRenameValue(e.target.value)} onKeyDown={(e) => handleHeaderRenameKeyDownFromHook(e, currentQuickSaveId, renameSave, currentSaveForHeader?.name)} onBlur={() => handleCancelHeaderRenameFromHook(currentSaveForHeader?.name)} className="header-rename-input" maxLength={50}/>
              <button onClick={() => handleConfirmHeaderRenameFromHook(currentQuickSaveId, renameSave, currentSaveForHeader?.name)} className="header-rename-button ml-1.5" title={UIText.saveGame}><Icons.Save className="w-4 h-4"/></button>
              <button onClick={() => handleCancelHeaderRenameFromHook(currentSaveForHeader?.name)} className="header-rename-button ml-1" title={UIText.cancelAction}><Icons.Close className="w-4 h-4"/></button>
            </div>
          ) : ( <span className={`truncate ${currentSaveForHeader ? 'cursor-pointer hover:underline' : ''}`} title={currentSaveForHeader ? `${UIText.renameSave}: ${displayHeaderTitle}` : displayHeaderTitle} onClick={currentSaveForHeader ? () => handleHeaderTitleClick(currentSaveForHeader.name) : undefined}> {displayHeaderTitle === APP_TITLE_CN ? <AppTitleStyled /> : displayHeaderTitle} </span> )}
        </div>
        <div className="header-actions">
            <button onClick={handleQuickSaveGame} className="action-button" title={UIText.saveArchiveTooltip}><Icons.Save className="w-5 h-5"/></button>
            <button onClick={handleOpenLoadMenu} className="action-button" title={UIText.loadArchiveTooltip}><Icons.Load className="w-5 h-5"/></button>
            <Dropdown items={globalImportDropdownItems} dropdownAlign="right" menuWidthClass="w-72" triggerButton={(toggleDropdown, isOpen, buttonRef) => (<button ref={buttonRef} onClick={toggleDropdown} className="action-button" title={UIText.globalImportsSectionTitle} aria-haspopup="true" aria-expanded={isOpen}><Icons.ClipboardList className="w-5 h-5" /></button>)}/>
            <input type="file" ref={importCharCardFileRef} onChange={handleImportSillyTavernCharCard} accept=".json,.png,.jsonc" className="hidden" multiple />
            <input type="file" ref={importUserRoleFileRef} onChange={handleImportUserRoleCharCard} accept=".json,.png,.jsonc" className="hidden" />
            <input type="file" ref={importWorldBookFileRef} onChange={handleImportSillyTavernWorldBook} accept=".json" className="hidden" multiple />
            <div className="relative">
              <button id="restart-game-button-trigger" onClick={toggleRestartDropdown} className="action-button" title={UIText.restartGameTooltip} aria-haspopup="true" aria-expanded={isRestartDropdownOpen}><Icons.ArrowPath className="w-5 h-5"/></button>
              {isRestartDropdownOpen && ( <div ref={restartDropdownRef} className="absolute right-0 mt-2 w-52 bg-secondary-themed rounded-md shadow-themed-lg border border-themed z-50 py-1 animate-fadeIn" role="menu">
                  <button onClick={() => selectRestartOption(handleRestartWithSummary)} disabled={isLoading} className="w-full text-left px-3 py-1.5 text-sm text-primary-themed hover:bg-element-themed transition-colors duration-150 flex items-center" role="menuitem">{isLoading && dialogueLog.length > 0 && <Icons.ArrowPath className="w-4 h-4 mr-2 animate-spin" />}{UIText.restartWithSummaryButton}</button>
                  <button onClick={() => selectRestartOption(handleRestartGameRequest)} disabled={isLoading} className="w-full text-left px-3 py-1.5 text-sm text-primary-themed hover:bg-element-themed transition-colors duration-150" role="menuitem">{UIText.restartGameTooltip} (当前设定)</button>
                  <button onClick={() => selectRestartOption(handleDefaultRestartGameRequest)} disabled={isLoading} className="w-full text-left px-3 py-1.5 text-sm text-primary-themed hover:bg-element-themed transition-colors duration-150" role="menuitem">{UIText.restartWithDefaultSettingsButton}</button>
              </div> )}
            </div>
            <div className="relative">
                <button onClick={() => setIsStatusPanelOpen(!isStatusPanelOpen)} className="action-button" title={UIText.statusPanelToggle} aria-expanded={isStatusPanelOpen}><Icons.Profile className="w-5 h-5"/></button>
                {canSpendPoints && (<span className="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-[var(--bg-secondary)] pointer-events-none"></span>)}
            </div>
        </div>
      </header>
      <SidebarMenu
        isOpen={isSidebarOpen} toggleSidebar={handleSidebarClose} gameSaves={gameSaves}
        onSaveGame={(name) => saveGame(name, playerName, dialogueLog, currentSceneImageKeyword, compositePlayerStatus, convertDialogueLogToGeminiHistory(dialogueLog))}
        onLoadGame={loadGame} onDeleteSave={deleteSave} onRenameSave={renameSave} 
        onUpdateSave={(id) => updateSave(id, playerName, dialogueLog, currentSceneImageKeyword, compositePlayerStatus, convertDialogueLogToGeminiHistory(dialogueLog))}
        gameSettings={gameSettings} userPreferences={userPreferences} playerStatus={compositePlayerStatus} 
        onGameSettingsChange={setGameSettings} onUserPreferencesChange={setUserPreferences}
        onPersistGameSettings={persistGameSettings} onPersistUserPreferences={persistUserPreferences}
        onRestartFromSave={restartFromSaveSettings} onRestartWithSummary={handleRestartWithSummary}
        onDefaultRestartGameRequest={handleDefaultRestartGameRequest} 
        characterCardPresets={characterCardPresets} userRolePresets={userRolePresets} aiStylePresets={aiStylePresets} 
        onSavePreset={savePreset} onLoadPreset={loadPreset} onDeletePreset={deletePreset}
        addNotification={addNotificationGlobal} onRequestConfirmation={handleRequestConfirmation}
        onExportData={exportAllData} onImportData={importAllData} onResetAllSettingsToDefaults={() => handleResetAllSettingsToDefaults()}
        onAllocateAttributePoint={callAllocateAttributePoint} onAllocateSkillPoint={callAllocateSkillPoint} 
        activeTab={sidebarActiveTab} onTabChange={setSidebarActiveTab}
        onInitializeGistBackup={handleInitializeGistBackup} onBackupToGist={() => handleBackupToGist(false)} onRestoreFromGist={handleRestoreFromGist} isGistLoading={isGistLoading}
      />
      <main className="app-main">
        <div className="chat-interface-container">
          <ChatInterface
            dialogueLines={dialogueLog} onSendMessage={handleSendMessageFromSession} 
            currentChoices={currentChoices} isLoading={isLoading} playerName={playerName} 
            gameSettings={gameSettings} playerStatus={compositePlayerStatus} 
            onRegenerateResponse={handleRegenerateResponse} onDeleteLine={handleDeleteDialogueLine}
            isLoadingBackground={isLoadingBackground}
            editingLineId={editingLineId} currentlyEditedContent={currentlyEditedContent}
            onStartEditLine={handleStartEditLine} onSaveEditedLine={handleSaveEditedLine}
            onCancelEditLine={handleCancelEditLine} onCurrentlyEditedContentChange={handleCurrentlyEditedContentChange}
          />
        </div>
      </main>
      {isStatusPanelOpen && (<div className="fixed inset-0 bg-black/30 backdrop-blur-sm z-20" onClick={() => setIsStatusPanelOpen(false)} aria-hidden="true" />)}
      <div ref={statusPanelRef} className={`fixed top-0 right-0 h-full z-30 transform transition-transform duration-300 ease-in-out w-80 md:w-96 ${isStatusPanelOpen ? 'translate-x-0' : 'translate-x-full'}`}>
        {isStatusPanelOpen && ( 
          <RPGStatusPanel
            statusRPG={playerStatusRPGPart} 
            playerName={currentSessionPlayerName || "Player"} 
            className="h-full" 
            onSummarizeInventoryAndShowDetails={() => rpgSystem.handleSummarizeInventoryAndShowDetailsRPG(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK, playerStatusRPGPart, (delta) => setCompositePlayerStatus(prev => ({...prev, ...delta})) )}
            onSummarizeLocationsAndShowDetails={() => rpgSystem.handleSummarizeLocationsAndShowDetailsRPG(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK, playerStatusRPGPart, (delta) => setCompositePlayerStatus(prev => ({...prev, ...delta})))}
            onSummarizeQuestsAndShowDetails={() => rpgSystem.handleSummarizeQuestsAndShowDetailsRPG(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK, playerStatusRPGPart, (delta) => setCompositePlayerStatus(prev => ({...prev, ...delta})))}
            onSummarizeCharactersAndShowDetails={() => rpgSystem.handleSummarizeCharactersAndShowDetailsRPG(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK, playerStatusRPGPart, (delta) => setCompositePlayerStatus(prev => ({...prev, ...delta})))}
            onSummarizeImportantEventsAndShowDetails={() => rpgSystem.handleSummarizeImportantEventsAndShowDetailsRPG(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK, playerStatusRPGPart, (delta) => setCompositePlayerStatus(prev => ({...prev, ...delta})))}
            isSummarizing={rpgSystem.isSummarizingRPG} 
            enableBackdropBlur={gameSettings.enableBackdropBlur}
            onClosePanel={() => setIsStatusPanelOpen(false)}
            onAllocateAttribute={callAllocateAttributePoint} 
            onAllocateSkill={callAllocateSkillPoint}
            currentMood={currentSessionMood}
            currentTimeOfDay={currentSessionTimeOfDay}
            currentWeather={currentSessionWeather}
          />
        )}
      </div>
      <NotificationsContainer />
      <ConfirmationModal {...confirmationModalState} onCancel={closeConfirmationModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />
      {activeDetailModal === 'inventory' && <InventoryModal isOpen={true} items={playerStatusRPGPart.inventory} onClose={closeDetailModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />}
      {activeDetailModal === 'locations' && <LocationsModal isOpen={true} locations={playerStatusRPGPart.visitedLocations} onClose={closeDetailModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />}
      {activeDetailModal === 'quests' && <QuestsModal isOpen={true} quests={playerStatusRPGPart.quests} onClose={closeDetailModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />}
      {activeDetailModal === 'characters' && <CharactersModal isOpen={true} characters={playerStatusRPGPart.characterProfiles} onClose={closeDetailModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />}
      {activeDetailModal === 'importantEvents' && <ImportantEventsModal isOpen={true} events={playerStatusRPGPart.importantEvents} onClose={closeDetailModal} enableBackdropBlur={gameSettings.enableBackdropBlur} />}
    </div>
  );
};
