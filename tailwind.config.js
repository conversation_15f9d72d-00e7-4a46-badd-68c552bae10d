/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./services/**/*.{js,ts,jsx,tsx}",
    "./*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      colors: {
        // Custom theme colors that work with CSS variables
        'primary-themed': 'var(--text-primary)',
        'secondary-themed': 'var(--text-secondary)',
        'accent-themed': 'var(--accent-color)',
        'element-themed': 'var(--bg-element)',
        'themed': 'var(--border-color)',
      },
      fontFamily: {
        'mochiy': ['Mochiy Pop One', 'LXGW WenKai Screen', 'sans-serif'],
        'great-vibes': ['Great Vibes', 'LXGW WenKai Screen', 'cursive'],
        'noto-sc': ['Noto Sans SC', 'LXGW WenKai Screen', 'sans-serif'],
      },
      animation: {
        'spin': 'spin 1s linear infinite',
      }
    },
  },
  plugins: [],
  // Disable preflight to avoid conflicts with existing CSS
  corePlugins: {
    preflight: false,
  }
}
