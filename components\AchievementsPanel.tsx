


import React, { useState, useMemo } from 'react';
import { Achievement, UserPreferences, AchievementCategory, AchievementTier } from '../types';
import { UIText, AVAILABLE_ACHIEVEMENTS, Icons, ACHIEVEMENT_TIERS } from '../constants';

interface AchievementsPanelProps {
  userPreferences: UserPreferences;
  enableBackdropBlur?: boolean; 
}

type AchievementFilter = "all" | "unlocked" | "locked";

const AchievementsPanel: React.FC<AchievementsPanelProps> = ({ 
  userPreferences, 
  enableBackdropBlur = true 
}) => {
  const [filter, setFilter] = useState<AchievementFilter>("all");
  const [selectedCategory, setSelectedCategory] = useState<AchievementCategory | "all">("all");

  const getAchievementIcon = (ach: Achievement) => {
    if (ach.icon && ach.icon.length === 1) return ach.icon; 
    return '🏆'; 
  };

  const achievementCategories: AchievementCategory[] = useMemo(() => {
    const cats = new Set(AVAILABLE_ACHIEVEMENTS.map(ach => ach.category));
    return Array.from(cats).sort((a,b) => a.localeCompare(b));
  }, []);


  const unlockedCount = useMemo(() => {
    return AVAILABLE_ACHIEVEMENTS.filter(ach => userPreferences.unlockedAchievements[ach.id]).length;
  }, [userPreferences.unlockedAchievements]);

  const filteredAchievements = useMemo(() => {
    return AVAILABLE_ACHIEVEMENTS.filter(ach => {
      const isUnlocked = !!userPreferences.unlockedAchievements[ach.id];
      const categoryMatch = selectedCategory === "all" || ach.category === selectedCategory;
      if (!categoryMatch) return false;
      
      if (filter === "unlocked") return isUnlocked;
      if (filter === "locked") return !isUnlocked;
      return true; 
    }).sort((a, b) => {
      const aUnlocked = !!userPreferences.unlockedAchievements[a.id];
      const bUnlocked = !!userPreferences.unlockedAchievements[b.id];
      if (aUnlocked && !bUnlocked) return -1;
      if (!aUnlocked && bUnlocked) return 1;
      // Sort by tier (higher tiers first - need a tier ranking map)
      const tierOrder: AchievementTier[] = ["不朽传说", "史诗刻痕", "黄金之冠", "白银之辉", "青铜印记"];
      const tierAIndex = tierOrder.indexOf(a.tier);
      const tierBIndex = tierOrder.indexOf(b.tier);
      if (tierAIndex < tierBIndex) return -1;
      if (tierAIndex > tierBIndex) return 1;
      return a.name.localeCompare(b.name); // Then by name
    });
  }, [userPreferences.unlockedAchievements, filter, selectedCategory]);

  const renderFilterButton = (filterType: AchievementFilter, label: string) => (
    <button
      onClick={() => setFilter(filterType)}
      className={`px-3 py-1.5 text-xs rounded-md transition-colors 
                  ${filter === filterType 
                    ? 'btn-dreamy btn-dreamy-xs filter brightness-105' 
                    : 'bg-element-themed hover:bg-opacity-80 border border-themed text-secondary-themed hover:text-primary-themed'}`}
    >
      {label}
    </button>
  );
  
  const baseClasses = "p-3 bg-secondary-themed rounded-xl shadow-themed-md mb-4 transition-colors duration-300";
  const blurClass = enableBackdropBlur ? "backdrop-blur-sm" : "";

  return (
    <div className={`${baseClasses} ${blurClass}`}>
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-lg font-semibold text-accent-themed flex items-center">
          <Icons.Trophy className="w-5 h-5 mr-2" />
          {UIText.achievementsTitle}
        </h3>
        <span className="text-sm text-secondary-themed">
          {unlockedCount} / {AVAILABLE_ACHIEVEMENTS.length}
        </span>
      </div>
      
      <div className="flex space-x-2 mb-1 pb-1 border-b border-themed/30">
        {renderFilterButton("all", UIText.filterAchievementsAll)}
        {renderFilterButton("unlocked", UIText.filterAchievementsUnlocked)}
        {renderFilterButton("locked", UIText.filterAchievementsLocked)}
      </div>

      <div className="mb-3">
        <label htmlFor="achCategoryFilter" className="sr-only">按类别筛选成就</label>
        <select 
            id="achCategoryFilter"
            value={selectedCategory} 
            onChange={(e) => setSelectedCategory(e.target.value as AchievementCategory | "all")}
            className="w-full text-xs p-1.5 bg-element-themed text-primary-themed rounded-md border border-themed focus:ring-1 focus:ring-accent-themed focus:border-transparent appearance-none"
        >
            <option value="all">所有类别</option>
            {achievementCategories.map(cat => (
                <option key={cat} value={cat}>{cat}</option>
            ))}
        </select>
      </div>


      {filteredAchievements.length === 0 ? (
        <p className="text-secondary-themed italic text-sm text-center py-4">
          {filter === 'unlocked' ? UIText.noUnlockedAchievements : 
           filter === 'locked' && unlockedCount === AVAILABLE_ACHIEVEMENTS.length ? UIText.allAchievementsUnlockedMessage :
           UIText.noItemsFound}
        </p>
      ) : (
        <div className="space-y-2 max-h-60 overflow-y-auto pr-1">
          {filteredAchievements.map(ach => {
            const isUnlocked = !!userPreferences.unlockedAchievements[ach.id];
            const unlockTimestamp = userPreferences.unlockedAchievements[ach.id];
            const tierInfo = ACHIEVEMENT_TIERS[ach.tier];
            return (
              <div 
                key={ach.id}
                className={`flex items-start p-2.5 rounded-lg border transition-all duration-300 ease-in-out group
                            ${isUnlocked 
                              ? 'bg-element-themed/80 border-accent-themed/50 shadow-sm' 
                              : 'bg-element-themed/40 border-themed/30 opacity-60 hover:opacity-100'}`}
              >
                <div className={`text-2xl mr-3 ${isUnlocked ? 'filter-none' : 'filter grayscale'}`}>
                  {getAchievementIcon(ach)}
                </div>
                <div className="flex-grow">
                  <div className="flex justify-between items-start">
                    <p className={`font-semibold ${isUnlocked ? 'text-primary-themed' : 'text-secondary-themed'}`}>
                      {ach.name}
                    </p>
                    {tierInfo && (
                        <span className={`text-xs font-bold px-1.5 py-0.5 rounded-full ${tierInfo.colorClass} bg-opacity-20 ${isUnlocked ? tierInfo.colorClass.replace('text-', 'bg-') + '/20' : 'bg-gray-500/20'}`}>
                            {tierInfo.name}
                        </span>
                    )}
                  </div>
                  <p className="text-xs text-secondary-themed mt-0.5">{ach.description}</p>
                  <p className="text-xs text-secondary-themed/70 mt-0.5">类别: {ach.category}</p>
                  {isUnlocked && unlockTimestamp && (
                    <p className="text-xs text-accent-themed/80 mt-1">
                      {UIText.achievementsUnlockedOn} 
                      {new Date(unlockTimestamp).toLocaleDateString(navigator.language || 'zh-CN', { year: 'numeric', month: 'short', day: 'numeric' })}
                    </p>
                  )}
                  {!isUnlocked && (
                     <p className="text-xs text-red-500/70 dark:text-red-400/70 mt-1">{UIText.achievementsLocked}</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default AchievementsPanel;