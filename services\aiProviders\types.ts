// AI Provider Types and Interfaces

export interface AIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  name?: string;
}

export interface AIModelInfo {
  id: string;
  name: string;
  provider: AIProviderType;
  maxTokens?: number;
  supportsStreaming?: boolean;
  costPer1kTokens?: {
    input: number;
    output: number;
  };
}

export interface AIGenerationConfig {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  topK?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  stream?: boolean;
}

export interface AIResponse {
  content: string;
  finishReason?: 'stop' | 'length' | 'content_filter' | 'tool_calls';
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model?: string;
  provider: AIProviderType;
}

export interface AIStreamChunk {
  content: string;
  isComplete: boolean;
  usage?: AIResponse['usage'];
}

export type AIProviderType = 
  | 'openai' 
  | 'gemini' 
  | 'anthropic' 
  | 'local' 
  | 'azure-openai'
  | 'cohere'
  | 'huggingface';

export interface AIProviderConfig {
  type: AIProviderType;
  apiKey?: string;
  baseUrl?: string;
  organization?: string;
  project?: string;
  defaultModel?: string;
  timeout?: number;
  maxRetries?: number;
  customHeaders?: Record<string, string>;
}

export interface AIProviderCapabilities {
  supportsStreaming: boolean;
  supportsSystemMessages: boolean;
  supportsToolCalls: boolean;
  supportsImageInput: boolean;
  maxContextLength: number;
  supportedFormats: string[];
}

// Abstract base class for AI providers
export abstract class BaseAIProvider {
  protected config: AIProviderConfig;
  protected capabilities: AIProviderCapabilities;

  constructor(config: AIProviderConfig) {
    this.config = config;
    this.capabilities = this.getCapabilities();
  }

  abstract getCapabilities(): AIProviderCapabilities;
  abstract getAvailableModels(): Promise<AIModelInfo[]>;
  abstract generateResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): Promise<AIResponse>;
  abstract generateStreamResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): AsyncGenerator<AIStreamChunk, void, unknown>;
  abstract validateConfig(): Promise<boolean>;

  // Test connection method with detailed response
  async testConnection(): Promise<{ success: boolean; responseTime: number; error?: string; modelCount?: number }> {
    const startTime = Date.now();
    try {
      await this.validateConfig();
      const models = await this.getAvailableModels();
      return {
        success: true,
        responseTime: Date.now() - startTime,
        modelCount: models.length
      };
    } catch (error) {
      return {
        success: false,
        responseTime: Date.now() - startTime,
        error: error.message || '连接测试失败'
      };
    }
  }

  // Common utility methods
  protected formatMessages(messages: AIMessage[]): AIMessage[] {
    return messages.filter(msg => msg.content.trim().length > 0);
  }

  protected validateGenerationConfig(config?: AIGenerationConfig): AIGenerationConfig {
    return {
      temperature: Math.max(0, Math.min(2, config?.temperature ?? 0.7)),
      maxTokens: Math.max(1, Math.min(this.capabilities.maxContextLength, config?.maxTokens ?? 2048)),
      topP: Math.max(0, Math.min(1, config?.topP ?? 1)),
      ...config
    };
  }

  getProviderType(): AIProviderType {
    return this.config.type;
  }

  getDefaultModel(): string {
    return this.config.defaultModel || '';
  }
}

// Error types
export class AIProviderError extends Error {
  constructor(
    message: string,
    public provider: AIProviderType,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'AIProviderError';
  }
}

export class AIRateLimitError extends AIProviderError {
  constructor(provider: AIProviderType, retryAfter?: number) {
    super(`Rate limit exceeded for ${provider}`, provider, 'RATE_LIMIT');
    this.retryAfter = retryAfter;
  }
  retryAfter?: number;
}

export class AIQuotaExceededError extends AIProviderError {
  constructor(provider: AIProviderType) {
    super(`Quota exceeded for ${provider}`, provider, 'QUOTA_EXCEEDED');
  }
}

// Legacy compatibility types for Gemini
export interface GeminiResponseFormat {
  dialogue: string;
  speakerName: string;
  speakerType: 'npc' | 'player' | 'narrator';
  sceneImageKeyword: string;
  choices: string[];
  storyUpdate: string;
  mood: string;
  timeOfDay: string;
}

// Conversion utilities
export function convertToGeminiFormat(response: AIResponse): GeminiResponseFormat {
  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(response.content);
    if (parsed.dialogue && parsed.speakerName && parsed.speakerType) {
      return parsed as GeminiResponseFormat;
    }
  } catch {
    // If not JSON, create a basic response
  }

  return {
    dialogue: response.content,
    speakerName: '旁白',
    speakerType: 'narrator',
    sceneImageKeyword: 'keep_current',
    choices: ['继续', '查看状态', '思考一下', '环顾四周'],
    storyUpdate: '故事继续发展...',
    mood: 'neutral',
    timeOfDay: 'unknown'
  };
}

export function convertFromGeminiMessages(messages: any[]): AIMessage[] {
  return messages.map(msg => ({
    role: msg.role === 'model' ? 'assistant' : msg.role,
    content: msg.parts?.[0]?.text || msg.content || '',
    name: msg.name
  }));
}

export function convertToGeminiMessages(messages: AIMessage[]): any[] {
  return messages.map(msg => ({
    role: msg.role === 'assistant' ? 'model' : msg.role,
    parts: [{ text: msg.content }]
  }));
}
