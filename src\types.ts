

import { Content } from "@google/genai";
// Import RPG specific types to be re-exported or composed
import { 
    CoreAttributes_RPG, 
    Skill_RPG, 
    PlayerStatus_RPG as RPGSystemStatus, // Aliased for clarity in composite PlayerStatus
    InventoryItem_RPG, 
    VisitedLocation_RPG, 
    Quest_RPG, 
    CharacterProfile_RPG, 
    ImportantEvent_RPG, 
    StatusEffect_RPG 
} from './rpgSystem/types_rpg';


export type { Content }; // Re-export Content

export interface DialogueLine {
  id: string;
  speakerName: string;
  speakerType: 'npc' | 'player' | 'narrator';
  text: string;
  processedHtml: string; 
  timestamp: number;
  storyUpdate?: string;
  storyUpdateForSummary?: string; 
}

export interface Character {
  id:string;
  name: string;
  type: 'player' | 'npc';
  portraitKeyword?: string;
  description?: string;
}

// Global types now alias the RPG-specific source definitions for data structures
export type InventoryItem = InventoryItem_RPG;
export type VisitedLocation = VisitedLocation_RPG;
export type Quest = Quest_RPG;
export type CharacterProfile = CharacterProfile_RPG;
export type ImportantEvent = ImportantEvent_RPG;
export type CoreAttributes = CoreAttributes_RPG;
export type Skill = Skill_RPG;
export type StatusEffect = StatusEffect_RPG;


// PlayerStatus is now a composite type.
// This defines the part of the player's status managed by the game session (non-RPG core logic).
export interface PlayerStatus_Session {
  name?: string; // Player's character name (managed by game session)
  mood: string;   // Mood (managed by game session, but can be influenced by RPG)
  timeOfDay: string; // Time of day (managed by game session, but can be influenced by RPG)
  weather?: string; // Weather (managed by game session)
}

// The global PlayerStatus will be a combination of session-specific and RPG-system-specific states.
export interface PlayerStatus extends PlayerStatus_Session, RPGSystemStatus {}


// --- Updated Custom Narrative Element Structure ---
export interface CustomNarrativeSubElement {
  id: string;
  key: string;    
  value: string;  
  isActive: boolean; 
}

export interface CustomNarrativePrimaryElement {
  id: string;
  name: string; 
  isActive: boolean; 
  subElements: CustomNarrativeSubElement[];
}
// --- End of Updated Structure ---

// Character Card data structure
export interface CharacterCardData {
  characterName: string;
  characterDescription: string; 
  characterOpeningMessage: string;
  characterPersonality: string; 
  characterScenario: string;
  characterExampleDialogue: string;
  characterPortraitKeywords: string; 
}

// Regex Replacement Types
export type RegexRuleScope = 'input' | 'output' | 'all';

export interface RegexRule {
  id: string;
  name: string; 
  pattern: string;
  replacement: string;
  flags: string; 
  scope: RegexRuleScope;
  isActive: boolean;
  isDisplayOnly?: boolean; 
  trimInput?: string; 
  description?: string; // Added for Story 2
}

export interface GameSettingsData extends CharacterCardData { 
  userRole: string;
  systemRole: string;
  customNarrativeElements: CustomNarrativePrimaryElement[];
  selectedModelId: string;
  selectedImagePromptStyleId: string;
  selectedSummaryModelId: string;
  chatInterfaceOpacity: number;
  dialogueBubbleOpacity: number;
  dialogueBlur: number;
  fontSizeScale: number;
  enableBackdropBlur: boolean;
  enableImageGeneration: boolean;
  minOutputChars: number;
  maxOutputChars: number;
  imageGenerationInterval: number;
  enableStreamMode?: boolean;
  enablePseudoStreamMode?: boolean; 

  githubPat?: string; 
  gistId?: string;    
  saveGithubPat?: boolean; 
  enableGistAutoBackup: boolean; 
  gistAutoBackupIntervalHours: number; 
  gistUseSystemProxy: boolean; 

  enableRegexReplacement: boolean;
  regexRules: RegexRule[];
}

export interface GameSaveData extends GameSettingsData {
  id: string;
  name: string; // Name of the save file itself
  timestamp: number;
  playerName: string; // Player's character name associated with this save
  dialogueLog: DialogueLine[];
  currentSceneImageKeyword: string;
  currentPlayerStatus: PlayerStatus; // This will use the composite PlayerStatus
  geminiChatHistory: Content[];
}

export interface GeminiResponseFormat {
  dialogue: string;
  speakerName: string;
  speakerType: 'npc' | 'player' | 'narrator';
  sceneImageKeyword: string;
  choices?: string[]; 
  storyUpdate?: string;
  mood: string; // Mood from AI, influences PlayerStatus_Session.mood
  timeOfDay: string; // Time of day from AI, influences PlayerStatus_Session.timeOfDay
  weather?: string; // Weather from AI, influences PlayerStatus_Session.weather
}

export enum GamePhase {
  StartScreen,
  Playing,
  Paused,
}

export enum Theme {
  Light = 'light',
  Dark = 'dark',
  Sakura = 'sakura',
  Starry = 'starry',
  Candy = 'candy',
  Forest = 'forest',
}

export interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  getThemeName: (theme: Theme) => string;
}

export interface ImageGenerationApiConfig {
  url: string;
  apiKeyEnvVar?: string;
  defaultStyleTags: string[];
  resolution: string;
  mobileResolution: string;
  negativePrompt: string;
  model?: string;
  seed?: string;
}

export type PresetType = 'userRole' | 'systemRole' | 'characterCard';

export interface SettingPreset {
  id: string;
  name: string;
  value: string | CustomNarrativePrimaryElement[] | CharacterCardData; 
  type: PresetType;
}

export interface AvailableModel {
  id: string;
  name: string;
}

export interface AvailableImagePromptStyle {
    id: string;
    name: string;
}

export type NotificationType = 'success' | 'error' | 'info' | 'warning' | 'achievement';

export interface NotificationMessage {
  id: string;
  message: string;
  type: NotificationType;
  duration?: number;
  icon?: React.ReactNode;
  title?: string;
}

// This type is specifically for RPG system's internal notifications.
// App.tsx will use the global NotificationMessage when calling addNotification.
export interface RPGNotification extends Omit<NotificationMessage, 'id' | 'icon'> {
    displayToUser?: boolean; 
}


export interface NotificationContextType {
  notifications: NotificationMessage[];
  addNotification: (message: string, type: NotificationType, duration?: number, title?: string) => void;
  removeNotification: (id: string) => void;
}

export type AchievementCategory =
  | '万象初窥' 
  | '命运织锦' 
  | '秘境行者' 
  | '我即风格' 
  | '魂之器量' 
  | '技艺臻境' 
  | '尘世羁绊' 
  | '逆境砺刃'; 

export type AchievementTier =
  | "青铜印记" 
  | "白银之辉" 
  | "黄金之冠" 
  | "史诗刻痕" 
  | "不朽传说"; 

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  tier: AchievementTier;
}

export interface UserPreferences {
  fontSizeScale: number;
  unlockedAchievements: Record<string, number>;
}

export type OpeningLineStyle = 'comedy' | 'horror';

export interface BackupData {
  version: string;
  exportedAt: number;
  gameSaves: GameSaveData[];
  gameSettings: GameSettingsData;
  userPreferences: UserPreferences;
  theme: Theme;
  userRolePresets: SettingPreset[];
  aiStylePresets: SettingPreset[]; 
  characterCardPresets: SettingPreset[];
  openingLineHistory: string[];
  lastOpeningStyle: OpeningLineStyle | null;
  cachedBackgrounds: string[];
}

export enum LocalStorageKeys {
  GAME_SAVES = 'memoryAbleGameSaves_v3_zh',
  GAME_SETTINGS = 'memoryAbleGameSettings_v3_zh',
  USER_PREFERENCES = 'memoryAbleUserPreferences_v1_zh',
  THEME = 'memoryAbleTheme_v1_zh',
  OPENING_LINE_HISTORY = 'memoryAbleOpeningLineHistory_v1',
  LAST_OPENING_STYLE = 'memoryAbleLastOpeningStyle_v1',
  USER_ROLE_PRESETS = 'memoryAbleUserRolePresets_v1',
  AI_STYLE_PRESETS = 'memoryAbleAiStylePresets_v1',
  CUSTOM_NARRATIVE_ELEMENTS_PRESETS = 'memoryAbleCustomElementsPresets_v1',
  CHARACTER_CARD_PRESETS = 'memoryAbleCharacterCardPresets_v1',
  CACHED_BACKGROUNDS = 'memoryAbleCachedBackgrounds_v1',
  LAST_SESSION_DATA = 'memoryAbleLastSessionData_v1',
}

export interface SillyTavernCharCardEntry { 
    keys: string[];
    comment: string;
    content: string;
    selective?: boolean;
    constant?: boolean;
    position?: "before_char" | "after_char" | "after_story";
    disable?: boolean;
}
export interface SillyTavernCharBook {
    name?: string;
    description?: string;
    scan_depth?: number;
    token_budget?: number;
    recursive_scanning?: boolean;
    entries: SillyTavernCharCardEntry[];
}


export interface SillyTavernCharCard {
  name?: string;
  description?: string;
  personality?: string;
  scenario?: string;
  first_mes?: string;
  mes_example?: string;
  char_name?: string; 
  char_persona?: string; 
  char_greeting?: string; 
  world_scenario?: string; 
  example_dialogue?: string; 
  tags?: string[]; 
  character_book?: SillyTavernCharBook; 
  lorebook?: SillyTavernCharBook; 
  entries?: Record<string, SillyTavernEntry> | SillyTavernCharCardEntry[]; 
}

export interface SillyTavernEntry { 
    key?: string[]; 
    keysecondary?: string[];
    comment?: string;
    content?: string;
    disable?: boolean;
}

export interface GistFile {
  filename: string;
  type: string;
  language: string;
  raw_url: string;
  size: number;
  truncated?: boolean;
  content?: string;
}

export interface GistOwner {
  login: string;
  id: number;
  avatar_url: string;
  html_url: string;
}

export interface GistApiResponse {
  url: string;
  forks_url: string;
  commits_url: string;
  id: string;
  node_id: string;
  git_pull_url: string;
  git_push_url: string;
  html_url: string;
  files: { [filename: string]: GistFile };
  public: boolean;
  created_at: string;
  updated_at: string;
  description: string | null;
  comments: number;
  user: GistOwner | null; 
  owner?: GistOwner; 
  truncated?: boolean;
}

export interface GistCreatePayload {
  description?: string;
  public?: boolean;
  files: {
    [filename: string]: {
      content: string;
    };
  };
}

export interface GistUpdatePayload {
  description?: string;
  files: {
    [filename: string]: {
      content?: string; 
      filename?: string; 
    } | null; 
  };
}

export type DetailModalType = 'inventory' | 'locations' | 'quests' | 'characters' | 'importantEvents';

// Tag parsing parameter types - these are fine as they refer to the global types
export type RPGAttributeChangeParams = { attrKey: keyof CoreAttributes; changeValue: number; reason: string };
export type RPGSkillUpdateParams = { skillId: string; newLevel: number; reason: string };
export type RPGSkillXPGainParams = { skillId: string; xpAmount: number; reason: string };
export type RPGAwardPointsParams = { amount: number; reason: string };
export type RPGHealthEnergyChangeParams = { change: number; reason: string };
export type RPGHealthEnergySetParams = { current: number; max: number; reason: string };
export type StatusUpdateKeyValueParams = { key: 'currentDay' | 'mood' | 'timeOfDay' | 'weather'; value: string }; // Keys relevant to session status
export type RPGEffectAddParams = { type: 'buff' | 'debuff'; name: string; duration: number; description?: string; icon?: string; source?: string; };