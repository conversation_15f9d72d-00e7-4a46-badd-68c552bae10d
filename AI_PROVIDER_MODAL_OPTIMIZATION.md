# AI提供商设置弹窗优化

## 优化内容

### 1. 添加勾选功能
- 在左侧供应商列表中为每个提供商添加了复选框
- 勾选复选框可以直接设置该提供商为默认提供商
- 复选框状态与当前使用的提供商同步
- 只有状态为"已连接"的提供商才能被勾选

### 2. 模型列表持久化存储
- 获取的模型列表现在会自动保存到localStorage中
- 下次打开弹窗时会自动加载之前获取的模型列表
- 即使没有重新获取模型，也能看到之前缓存的模型
- 在模型选择区域会显示是否使用了缓存的模型列表

### 3. 修复"设为默认"功能
- 改进了handleProviderSwitch函数，确保状态更新的一致性
- 添加了立即状态更新，提供更好的用户体验
- 确保切换提供商后UI状态正确同步

## 技术实现

### 持久化存储
```typescript
// 保存模型到localStorage
const savePersistedModels = (models: Record<string, any[]>) => {
  try {
    localStorage.setItem('ai-provider-models', JSON.stringify(models));
    setPersistedModels(models);
  } catch (error) {
    console.error('Failed to save persisted models:', error);
  }
};

// 加载持久化的模型
const loadPersistedModels = () => {
  try {
    const saved = localStorage.getItem('ai-provider-models');
    if (saved) {
      const parsed = JSON.parse(saved);
      setPersistedModels(parsed);
    }
  } catch (error) {
    console.error('Failed to load persisted models:', error);
  }
};
```

### 勾选功能
```typescript
<input
  type="checkbox"
  checked={info.current}
  onChange={(e) => {
    e.stopPropagation();
    if (!info.current && info.status === 'ok') {
      handleProviderSwitch(provider as AIProviderType);
    }
  }}
  disabled={info.status !== 'ok'}
  className="w-3 h-3 rounded border border-themed/30 text-accent-themed focus:ring-accent-themed focus:ring-1"
  title={info.status === 'ok' ? '点击设为默认提供商' : '请先配置并测试连接'}
/>
```

### 改进的提供商切换
```typescript
const handleProviderSwitch = async (provider: AIProviderType) => {
  setLoading(true);
  try {
    const success = await switchAIProvider(provider);
    if (success) {
      setCurrentProvider(provider);
      
      // Update local state immediately for better UX
      setProviders(prev => {
        const updated = { ...prev };
        // Set all providers to not current
        Object.keys(updated).forEach(key => {
          updated[key] = { ...updated[key], current: false };
        });
        // Set the selected provider as current
        if (updated[provider]) {
          updated[provider] = { ...updated[provider], current: true };
        }
        return updated;
      });
      
      // Reload data to ensure consistency
      await loadProviderData();
    }
  } catch (error) {
    console.error('Provider switch failed:', error);
  } finally {
    setLoading(false);
  }
};
```

## 用户体验改进

1. **快速切换**: 用户可以通过勾选复选框快速切换默认提供商
2. **持久化模型**: 模型列表会被保存，避免重复获取
3. **状态同步**: 提供商状态在UI中实时更新
4. **缓存提示**: 明确显示是否使用了缓存的模型列表
5. **错误处理**: 改进了错误处理和用户反馈

## 文件修改

- `components/AIProviderModal.tsx`: 主要修改文件，添加了所有新功能
- 新增状态管理: `persistedModels`, `loadPersistedModels`, `savePersistedModels`
- 改进函数: `handleProviderSwitch`, `handleFetchModels`, `loadProviderData`
- UI改进: 添加复选框，改进模型选择显示逻辑

## 测试建议

1. 测试勾选功能是否正确切换默认提供商
2. 验证模型列表是否正确持久化和加载
3. 确认"设为默认"按钮功能正常
4. 检查UI状态同步是否正确
5. 测试错误情况下的用户体验
