import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Icons } from '../constants';
import { 
  switchAIProvider, 
  validateAIProviders,
  updateAIProviderConfig,
  getCurrentAIProvider,
  testAIProviderConnection
} from '../services/aiService';
import { getAIServiceManager } from '../services/geminiClient';
import { AIProviderType } from '../services/aiProviders/types';
import { localConfigService } from '../services/localConfigService';

interface AIProviderModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ProviderInfo {
  available: boolean;
  current: boolean;
  models: any[];
  status: 'ok' | 'error';
  error?: string;
}

interface TestResult {
  success: boolean;
  responseTime: number;
  error?: string;
  modelCount?: number;
  testedModel?: string;
}

const providerNames: Record<AIProviderType, string> = {
  gemini: 'Gemini',
  openai: 'OpenAI GPT',
  anthropic: 'Claude',
  local: '本地AI',
  'azure-openai': 'Azure OpenAI',
  cohere: 'Cohere',
  huggingface: 'Hugging Face'
};

const providerIcons: Record<AIProviderType, string> = {
  gemini: '🤖',
  openai: '🧠',
  anthropic: '🎭',
  local: '🏠',
  'azure-openai': '☁️',
  cohere: '🔗',
  huggingface: '🤗'
};

const providerColors: Record<AIProviderType, string> = {
  gemini: 'bg-blue-500',
  openai: 'bg-green-500',
  anthropic: 'bg-purple-500',
  local: 'bg-gray-500',
  'azure-openai': 'bg-cyan-500',
  cohere: 'bg-orange-500',
  huggingface: 'bg-yellow-500'
};

export const AIProviderModal: React.FC<AIProviderModalProps> = ({ isOpen, onClose }) => {
  const [providers, setProviders] = useState<Record<AIProviderType, ProviderInfo>>({} as any);
  const [selectedProvider, setSelectedProvider] = useState<AIProviderType | null>(null);
  const [currentProvider, setCurrentProvider] = useState<AIProviderType | null>(null);
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<Record<AIProviderType, TestResult>>({} as any);
  const [testingProvider, setTestingProvider] = useState<AIProviderType | null>(null);
  const [loadingModels, setLoadingModels] = useState<Record<string, boolean>>({});
  const [availableModels, setAvailableModels] = useState<Record<string, any[]>>({});
  const [persistedModels, setPersistedModels] = useState<Record<string, any[]>>({});
  const [showAddProvider, setShowAddProvider] = useState(false);
  const [newProviderName, setNewProviderName] = useState('');
  const [newProviderUrl, setNewProviderUrl] = useState('');
  const [customProviders, setCustomProviders] = useState<Record<string, { name: string; url: string; apiKey: string }>>({});
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({
    gemini: '',
    openai: '',
    anthropic: '',
    'azure-openai': '',
    local: '',
    cohere: '',
    huggingface: ''
  });
  const [customEndpoints, setCustomEndpoints] = useState<Record<string, string>>({
    openai: 'https://api.openai.com/v1',
    'azure-openai': 'https://your-resource.openai.azure.com',
    local: 'http://localhost:11434/v1',
    cohere: 'https://api.cohere.ai/v1',
    huggingface: 'https://api-inference.huggingface.co/models'
  });
  const [selectedModel, setSelectedModel] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen) {
      loadProviderData();
      loadPersistedModels();
    }
  }, [isOpen]);

  const loadPersistedModels = () => {
    try {
      const saved = localStorage.getItem('ai-provider-models');
      if (saved) {
        const parsed = JSON.parse(saved);
        setPersistedModels(parsed);
      }
    } catch (error) {
      console.error('Failed to load persisted models:', error);
    }
  };

  const savePersistedModels = (models: Record<string, any[]>) => {
    try {
      localStorage.setItem('ai-provider-models', JSON.stringify(models));
      setPersistedModels(models);
    } catch (error) {
      console.error('Failed to save persisted models:', error);
    }
  };

  const loadProviderData = async () => {
    setLoading(true);
    try {
      const config = localConfigService.getConfig();

      // 只在初始化时设置API密钥，避免覆盖用户输入
      setApiKeys(prev => ({
        gemini: config.ai?.geminiApiKey || prev.gemini || '',
        openai: config.ai?.openaiApiKey || prev.openai || '',
        anthropic: config.ai?.anthropicApiKey || prev.anthropic || '',
        'azure-openai': config.ai?.openaiApiKey || prev['azure-openai'] || '',
        local: prev.local || '',
        cohere: prev.cohere || '',
        huggingface: prev.huggingface || ''
      }));

      setCustomEndpoints(prev => ({
        ...prev,
        openai: config.ai?.openaiBaseUrl || prev.openai || 'https://api.openai.com/v1',
        local: config.ai?.localEndpoint || prev.local || 'http://localhost:11434/v1'
      }));

      const aiService = getAIServiceManager();
      const [providerStatus, validation, allModels] = await Promise.all([
        Promise.resolve(aiService.getProviderStatus()),
        validateAIProviders().catch(() => ({} as Record<AIProviderType, boolean>)),
        aiService.getAllAvailableModels().catch(() => ({} as Record<AIProviderType, any[]>))
      ]);

      const combinedProviders: Record<string, ProviderInfo> = {};
      const supportedProviders: AIProviderType[] = ['gemini', 'openai', 'anthropic', 'azure-openai', 'local'];

      for (const provider of supportedProviders) {
        const hasApiKey = apiKeys[provider] || (provider === 'local');
        combinedProviders[provider] = {
          available: hasApiKey,
          current: provider === getCurrentAIProvider(),
          models: allModels[provider] || [],
          status: validation[provider] ? 'ok' : 'error',
          error: validation[provider] ? undefined : hasApiKey ? '连接测试失败' : '需要配置API密钥'
        };
      }

      // 加载自定义供应商
      const customProvidersFromConfig = config.customProviders || {};
      setCustomProviders(customProvidersFromConfig);

      for (const [providerId, providerInfo] of Object.entries(customProvidersFromConfig)) {
        const hasApiKey = providerInfo.apiKey && providerInfo.apiKey.trim() !== '';
        combinedProviders[providerId] = {
          available: hasApiKey,
          current: false, // 自定义供应商暂时不支持设为当前
          models: [],
          status: hasApiKey ? 'ok' : 'error',
          error: hasApiKey ? undefined : '需要配置API密钥'
        };
      }

      setProviders(combinedProviders as Record<AIProviderType, ProviderInfo>);
      setCurrentProvider(getCurrentAIProvider());

      // Load persisted models into available models
      setAvailableModels(prev => ({ ...prev, ...persistedModels }));

      if (!selectedProvider && supportedProviders.length > 0) {
        setSelectedProvider(supportedProviders[0]);
      }
      
    } catch (error) {
      console.error('Failed to load provider data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProviderSwitch = async (provider: AIProviderType) => {
    setLoading(true);
    try {
      const success = await switchAIProvider(provider);
      if (success) {
        setCurrentProvider(provider);

        // Update local state immediately for better UX
        setProviders(prev => {
          const updated = { ...prev };
          // Set all providers to not current
          Object.keys(updated).forEach(key => {
            updated[key] = { ...updated[key], current: false };
          });
          // Set the selected provider as current
          if (updated[provider]) {
            updated[provider] = { ...updated[provider], current: true };
          }
          return updated;
        });

        // Reload data to ensure consistency
        await loadProviderData();
      }
    } catch (error) {
      console.error('Provider switch failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApiKeyUpdate = async (provider: AIProviderType, apiKey: string) => {
    try {
      const config = localConfigService.getConfig();
      const updatedConfig = { ...config };
      
      switch (provider) {
        case 'gemini':
          updatedConfig.ai = { ...updatedConfig.ai, geminiApiKey: apiKey };
          break;
        case 'openai':
          updatedConfig.ai = { ...updatedConfig.ai, openaiApiKey: apiKey };
          break;
        case 'anthropic':
          updatedConfig.ai = { ...updatedConfig.ai, anthropicApiKey: apiKey };
          break;
        case 'azure-openai':
          updatedConfig.ai = { ...updatedConfig.ai, openaiApiKey: apiKey };
          break;
      }
      
      localConfigService.updateConfig(updatedConfig);
      
      const providerConfig: any = { apiKey };
      if ((provider === 'openai' || provider === 'azure-openai') && customEndpoints[provider]) {
        providerConfig.baseUrl = customEndpoints[provider];
      }
      
      updateAIProviderConfig(provider, providerConfig);
      setApiKeys(prev => ({ ...prev, [provider]: apiKey }));
      
      setTimeout(() => loadProviderData(), 500);
    } catch (error) {
      console.error('API key update failed:', error);
    }
  };

  const handleEndpointUpdate = async (provider: AIProviderType, endpoint: string) => {
    try {
      const config = localConfigService.getConfig();
      const updatedConfig = { ...config };
      
      if (provider === 'openai') {
        updatedConfig.ai = { ...updatedConfig.ai, openaiBaseUrl: endpoint };
      } else if (provider === 'local') {
        updatedConfig.ai = { ...updatedConfig.ai, localEndpoint: endpoint };
      }
      
      localConfigService.updateConfig(updatedConfig);
      setCustomEndpoints(prev => ({ ...prev, [provider]: endpoint }));
      
      updateAIProviderConfig(provider, { 
        baseUrl: endpoint,
        apiKey: apiKeys[provider] || ''
      });
      
      setTimeout(() => loadProviderData(), 500);
    } catch (error) {
      console.error('Endpoint update failed:', error);
    }
  };

  const handleFetchModels = async (provider: AIProviderType) => {
    setLoadingModels(prev => ({ ...prev, [provider]: true }));
    try {
      // 检查API密钥是否已配置
      const apiKey = customProviders[provider] ? customProviders[provider].apiKey : apiKeys[provider];
      if (!apiKey && provider !== 'local') {
        throw new Error('请先配置API密钥');
      }

      // 创建临时配置来获取模型列表
      const tempConfig: any = {};

      if (customProviders[provider]) {
        // 自定义供应商
        tempConfig.baseUrl = customProviders[provider].url;
        tempConfig.apiKey = customProviders[provider].apiKey;
      } else {
        // 内置供应商
        tempConfig.apiKey = apiKeys[provider];
        if (customEndpoints[provider]) {
          tempConfig.baseUrl = customEndpoints[provider];
        }
      }

      // 临时更新配置
      updateAIProviderConfig(provider, tempConfig);

      // 获取模型列表
      const aiService = getAIServiceManager();

      // 先切换到目标提供商
      const originalProvider = aiService.getCurrentProvider()?.getProviderType();
      const switched = aiService.switchProvider(provider);

      if (!switched) {
        throw new Error('无法切换到指定提供商');
      }

      // 获取当前提供商的模型列表
      const models = await aiService.getCurrentProviderModels();

      // 切换回原提供商
      if (originalProvider && originalProvider !== provider) {
        aiService.switchProvider(originalProvider);
      }

      setAvailableModels(prev => ({ ...prev, [provider]: models }));

      // Save to persistent storage
      const updatedModels = { ...persistedModels, [provider]: models };
      savePersistedModels(updatedModels);

      // 如果没有选择模型，自动选择第一个
      if (models.length > 0 && !selectedModel[provider]) {
        setSelectedModel(prev => ({ ...prev, [provider]: models[0].id }));
      }

    } catch (error: any) {
      console.error(`Failed to fetch models for ${provider}:`, error);
      setAvailableModels(prev => ({ ...prev, [provider]: [] }));
      // 显示错误信息
      setTestResults(prev => ({
        ...prev,
        [provider]: {
          success: false,
          responseTime: 0,
          error: `获取模型失败: ${error.message}`
        }
      }));
    } finally {
      setLoadingModels(prev => ({ ...prev, [provider]: false }));
    }
  };

  const handleTestConnection = async (provider: AIProviderType, modelId?: string) => {
    setTestingProvider(provider);
    const startTime = Date.now();

    try {
      // 使用指定的模型进行测试
      const testModel = modelId || selectedModel[provider];

      if (!testModel) {
        throw new Error('请先选择一个模型进行测试');
      }

      // 检查API密钥
      const apiKey = customProviders[provider] ? customProviders[provider].apiKey : apiKeys[provider];
      if (!apiKey && provider !== 'local') {
        throw new Error('请先配置API密钥');
      }

      // 创建测试配置
      const testConfig: any = {
        defaultModel: testModel
      };

      if (customProviders[provider]) {
        testConfig.baseUrl = customProviders[provider].url;
        testConfig.apiKey = customProviders[provider].apiKey;
      } else {
        testConfig.apiKey = apiKeys[provider];
        if (customEndpoints[provider]) {
          testConfig.baseUrl = customEndpoints[provider];
        }
      }

      // 临时更新配置
      updateAIProviderConfig(provider, testConfig);

      // 获取AI服务管理器
      const aiService = getAIServiceManager();

      // 切换到目标提供商
      const originalProvider = aiService.getCurrentProvider()?.getProviderType();
      const switched = aiService.switchProvider(provider);

      if (!switched) {
        throw new Error('无法切换到指定提供商');
      }

      // 进行实际的API测试 - 发送一个简单的测试消息
      try {
        const testMessages = [
          { role: 'user' as const, content: 'Hello' }
        ];

        const response = await aiService.generateResponse(testMessages, {
          maxTokens: 10,
          temperature: 0.1
        });

        const responseTime = Date.now() - startTime;

        // 切换回原提供商
        if (originalProvider && originalProvider !== provider) {
          aiService.switchProvider(originalProvider);
        }

        setTestResults(prev => ({
          ...prev,
          [provider]: {
            success: true,
            responseTime,
            testedModel: testModel,
            modelCount: availableModels[provider]?.length || 0
          }
        }));

      } catch (apiError: any) {
        // 切换回原提供商
        if (originalProvider && originalProvider !== provider) {
          aiService.switchProvider(originalProvider);
        }

        const responseTime = Date.now() - startTime;
        throw new Error(`API调用失败: ${apiError.message}`);
      }

    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      setTestResults(prev => ({
        ...prev,
        [provider]: {
          success: false,
          responseTime,
          error: error.message || '测试失败',
          testedModel: modelId || selectedModel[provider]
        }
      }));
    } finally {
      setTestingProvider(null);
    }
  };

  const handleAddProvider = () => {
    setShowAddProvider(true);
    setNewProviderName('');
    setNewProviderUrl('');
  };

  const handleSaveNewProvider = () => {
    const trimmedName = newProviderName.trim();
    const trimmedUrl = newProviderUrl.trim();

    if (!trimmedName || !trimmedUrl) {
      alert('请填写完整的供应商名称和URL地址');
      return;
    }

    // 验证URL格式
    try {
      new URL(trimmedUrl);
    } catch {
      alert('请输入有效的URL地址');
      return;
    }

    const providerId = `custom_${Date.now()}`;
    const fullUrl = trimmedUrl.endsWith('/v1') ? trimmedUrl : `${trimmedUrl}/v1`;

    const newProvider = {
      name: trimmedName,
      url: fullUrl,
      apiKey: ''
    };

    setCustomProviders(prev => ({
      ...prev,
      [providerId]: newProvider
    }));

    // 保存到本地配置
    const config = localConfigService.getConfig();
    const updatedConfig = {
      ...config,
      customProviders: {
        ...config.customProviders,
        [providerId]: newProvider
      }
    };
    localConfigService.updateConfig(updatedConfig);

    setShowAddProvider(false);
    setNewProviderName('');
    setNewProviderUrl('');

    // 重新加载数据
    setTimeout(() => loadProviderData(), 500);
  };

  const handleDeleteProvider = (providerId: string) => {
    if (!customProviders[providerId]) {
      alert('只能删除自定义供应商');
      return;
    }

    if (!confirm(`确定要删除供应商 "${customProviders[providerId].name}" 吗？`)) {
      return;
    }

    // 从状态中移除
    setCustomProviders(prev => {
      const newProviders = { ...prev };
      delete newProviders[providerId];
      return newProviders;
    });

    // 从本地配置中移除
    const config = localConfigService.getConfig();
    const updatedConfig = {
      ...config,
      customProviders: {
        ...config.customProviders
      }
    };
    delete updatedConfig.customProviders[providerId];
    localConfigService.updateConfig(updatedConfig);

    // 清理相关状态
    setAvailableModels(prev => {
      const newModels = { ...prev };
      delete newModels[providerId];
      return newModels;
    });

    setSelectedModel(prev => {
      const newSelected = { ...prev };
      delete newSelected[providerId];
      return newSelected;
    });

    setTestResults(prev => {
      const newResults = { ...prev };
      delete newResults[providerId];
      return newResults;
    });

    // 如果删除的是当前选中的供应商，切换到第一个可用的
    if (selectedProvider === providerId) {
      const remainingProviders = Object.keys(providers).filter(p => p !== providerId);
      if (remainingProviders.length > 0) {
        setSelectedProvider(remainingProviders[0] as AIProviderType);
      } else {
        setSelectedProvider(null);
      }
    }

    // 重新加载数据
    setTimeout(() => loadProviderData(), 500);
  };

  if (!isOpen) return null;

  const selectedProviderInfo = selectedProvider ? providers[selectedProvider] : null;
  const testResult = selectedProvider ? testResults[selectedProvider] : null;
  const isTesting = testingProvider === selectedProvider;

  // 获取供应商显示名称
  const getProviderName = (providerId: string) => {
    if (customProviders[providerId]) {
      return customProviders[providerId].name;
    }
    return providerNames[providerId as AIProviderType] || providerId;
  };

  // 获取供应商颜色
  const getProviderColor = (providerId: string) => {
    if (customProviders[providerId]) {
      return 'bg-purple-500'; // 自定义供应商使用紫色
    }
    return providerColors[providerId as AIProviderType] || 'bg-gray-500';
  };

  // 获取供应商图标
  const getProviderIcon = (providerId: string) => {
    if (customProviders[providerId]) {
      return '🔗'; // 自定义供应商使用链接图标
    }
    return providerIcons[providerId as AIProviderType] || '?';
  };

  return createPortal(
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
      <div className="bg-element-themed rounded-lg shadow-xl w-[50vw] max-w-md h-auto max-h-[60vh] flex overflow-hidden">
        {/* Left Sidebar - Provider List */}
        <div className="w-28 bg-element-themed border-r border-themed/20 flex flex-col">
          {/* Header */}
          <div className="p-3 border-b border-themed/20">
            <h2 className="text-base font-semibold text-primary-themed">AI 提供商设置</h2>
          </div>
          
          {/* Provider List */}
          <div className="flex-1 overflow-y-auto">
            {Object.entries(providers).map(([provider, info]) => (
              <div
                key={provider}
                onClick={() => setSelectedProvider(provider as AIProviderType)}
                className={`p-2 cursor-pointer border-b border-themed/10 hover:bg-themed/10 transition-colors ${
                  selectedProvider === provider ? 'bg-accent-themed/20 border-r-2 border-r-accent-themed' : ''
                }`}
              >
                <div className="flex items-center space-x-2">
                  {/* Checkbox for quick selection */}
                  <input
                    type="checkbox"
                    checked={info.current}
                    onChange={(e) => {
                      e.stopPropagation();
                      if (!info.current && info.status === 'ok') {
                        handleProviderSwitch(provider as AIProviderType);
                      }
                    }}
                    disabled={info.status !== 'ok'}
                    className="w-3 h-3 rounded border border-themed/30 text-accent-themed focus:ring-accent-themed focus:ring-1"
                    title={info.status === 'ok' ? '点击设为默认提供商' : '请先配置并测试连接'}
                  />
                  <div className={`w-6 h-6 rounded-full ${getProviderColor(provider)} flex items-center justify-center text-white text-xs`}>
                    {getProviderIcon(provider)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium text-primary-themed truncate">
                        {getProviderName(provider)}
                      </span>
                      {info.current && (
                        <span className="text-xs bg-green-500 text-white px-1 py-0.5 rounded text-[10px]">
                          ON
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-1 mt-0.5">
                      <div className={`w-1.5 h-1.5 rounded-full ${info.status === 'ok' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-[10px] text-secondary-themed">
                        {info.status === 'ok' ? '已连接' : '未连接'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Add Provider Button */}
          <div className="p-2 border-t border-themed/20">
            <button
              onClick={handleAddProvider}
              className="w-full p-2 border border-dashed border-themed/50 rounded text-secondary-themed hover:border-accent-themed hover:text-accent-themed transition-colors text-xs"
            >
              ＋ 新增供应商
            </button>
          </div>
        </div>

        {/* Right Content - Provider Configuration */}
        <div className="flex-1 flex flex-col min-h-0">
          {/* Header */}
          <div className="p-3 border-b border-themed/20 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {selectedProvider && (
                <>
                  <div className={`w-6 h-6 rounded-full ${getProviderColor(selectedProvider)} flex items-center justify-center text-white text-xs`}>
                    {getProviderIcon(selectedProvider)}
                  </div>
                  <h3 className="text-base font-semibold text-primary-themed">
                    {getProviderName(selectedProvider)}
                  </h3>
                </>
              )}
            </div>
            <div className="flex items-center space-x-2">
              {/* Delete button for custom providers */}
              {selectedProvider && customProviders[selectedProvider] && (
                <button
                  onClick={() => handleDeleteProvider(selectedProvider)}
                  className="p-1 hover:bg-red-500/10 rounded transition-colors text-red-500"
                  title="删除此自定义供应商"
                >
                  <Icons.Trash className="w-4 h-4" />
                </button>
              )}
              <button
                onClick={onClose}
                className="p-1 hover:bg-themed/10 rounded transition-colors"
              >
                <Icons.XMark className="w-4 h-4 text-secondary-themed" />
              </button>
            </div>
          </div>

          {/* Content */}
          {selectedProvider && selectedProviderInfo ? (
            <div className="flex-1 p-3 overflow-y-auto space-y-3 max-h-[400px]">
              {/* Connection Status */}
              <div className="bg-themed/5 rounded-lg p-3">
                <h4 className="text-sm font-medium text-primary-themed mb-2">连接状态</h4>

                {testResult && (
                  <div className={`text-sm p-2 rounded ${
                    testResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {testResult.success ? (
                      <div>
                        <div>✅ 连接成功 - 响应时间: {testResult.responseTime}ms</div>
                        {testResult.testedModel && (
                          <div className="text-xs mt-1">测试模型: {testResult.testedModel}</div>
                        )}
                        {testResult.modelCount && (
                          <div className="text-xs mt-1">可用模型: {testResult.modelCount} 个</div>
                        )}
                      </div>
                    ) : (
                      <span>❌ 连接失败: {testResult.error}</span>
                    )}
                  </div>
                )}

                {!testResult && (
                  <div className="text-xs text-secondary-themed">
                    请先配置API密钥和地址，然后获取模型列表并测试连接
                  </div>
                )}
              </div>

              {/* API Configuration */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-xs font-medium text-primary-themed">API 配置</h4>
                  <button
                    onClick={() => handleFetchModels(selectedProvider)}
                    disabled={loadingModels[selectedProvider] || (!apiKeys[selectedProvider] && !customProviders[selectedProvider]?.apiKey && selectedProvider !== 'local')}
                    className="px-2 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
                    title={(!apiKeys[selectedProvider] && !customProviders[selectedProvider]?.apiKey && selectedProvider !== 'local') ? '请先配置API密钥' : '获取可用模型列表'}
                  >
                    {loadingModels[selectedProvider] ? '获取中...' : '获取模型'}
                  </button>
                </div>
                
                {/* API Key */}
                {selectedProvider !== 'local' && (
                  <div>
                    <label className="block text-xs text-secondary-themed mb-1">API 密钥</label>
                    <div className="flex space-x-2">
                      <input
                        type="password"
                        value={customProviders[selectedProvider] ? customProviders[selectedProvider].apiKey : (apiKeys[selectedProvider] || '')}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          if (customProviders[selectedProvider]) {
                            setCustomProviders(prev => ({
                              ...prev,
                              [selectedProvider]: {
                                ...prev[selectedProvider],
                                apiKey: newValue
                              }
                            }));

                            // 实时保存自定义供应商的API密钥
                            const config = localConfigService.getConfig();
                            const updatedConfig = {
                              ...config,
                              customProviders: {
                                ...config.customProviders,
                                [selectedProvider]: {
                                  ...config.customProviders?.[selectedProvider],
                                  ...customProviders[selectedProvider],
                                  apiKey: newValue
                                }
                              }
                            };
                            localConfigService.updateConfig(updatedConfig);
                          } else {
                            setApiKeys(prev => ({ ...prev, [selectedProvider]: newValue }));
                            // 实时保存内置供应商的API密钥
                            handleApiKeyUpdate(selectedProvider, newValue);
                          }
                        }}
                        placeholder="输入API密钥"
                        className="flex-1 p-1.5 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs"
                      />
                      <button
                        onClick={() => {
                          // 显示保存成功提示
                          setTestResults(prev => ({
                            ...prev,
                            [selectedProvider]: {
                              success: true,
                              responseTime: 0,
                              error: '配置已保存'
                            }
                          }));

                          // 清除提示
                          setTimeout(() => {
                            setTestResults(prev => {
                              const newResults = { ...prev };
                              delete newResults[selectedProvider];
                              return newResults;
                            });
                          }, 2000);
                        }}
                        className="px-3 py-1.5 bg-green-500 text-white rounded text-xs hover:bg-green-600"
                        title="配置已自动保存，点击确认"
                      >
                        已保存
                      </button>
                    </div>
                  </div>
                )}

                {/* Custom Provider URL Display */}
                {customProviders[selectedProvider] && (
                  <div>
                    <label className="block text-xs text-secondary-themed mb-1">API 地址</label>
                    <div className="p-1.5 bg-themed/5 border border-themed/20 rounded text-primary-themed text-xs">
                      {customProviders[selectedProvider].url}
                    </div>
                  </div>
                )}

                {/* Custom Endpoint */}
                {(selectedProvider === 'openai' || selectedProvider === 'local' || selectedProvider === 'azure-openai') && !customProviders[selectedProvider] && (
                  <div>
                    <label className="block text-sm text-secondary-themed mb-2">API 地址</label>
                    <div className="flex space-x-2">
                      <input
                        type="url"
                        value={customEndpoints[selectedProvider] || ''}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          setCustomEndpoints(prev => ({ ...prev, [selectedProvider]: newValue }));
                          // 实时保存自定义端点
                          handleEndpointUpdate(selectedProvider, newValue);
                        }}
                        placeholder="输入API地址"
                        className="flex-1 p-2 bg-element-themed border border-themed/30 rounded text-primary-themed text-sm"
                      />
                      <button
                        onClick={() => {
                          // 显示保存成功提示
                          setTestResults(prev => ({
                            ...prev,
                            [selectedProvider]: {
                              success: true,
                              responseTime: 0,
                              error: '端点已保存'
                            }
                          }));

                          // 清除提示
                          setTimeout(() => {
                            setTestResults(prev => {
                              const newResults = { ...prev };
                              delete newResults[selectedProvider];
                              return newResults;
                            });
                          }, 2000);
                        }}
                        className="px-4 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                        title="端点已自动保存，点击确认"
                      >
                        已保存
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Model Selection */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="text-xs font-medium text-primary-themed">模型选择</h4>
                  {availableModels[selectedProvider] && availableModels[selectedProvider].length > 0 && (
                    <button
                      onClick={() => handleTestConnection(selectedProvider)}
                      disabled={isTesting || !selectedModel[selectedProvider]}
                      className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
                      title={!selectedModel[selectedProvider] ? '请先选择一个模型' : '使用选定模型测试API连接'}
                    >
                      {isTesting ? '测试中...' : '测试模型'}
                    </button>
                  )}
                </div>

                {(availableModels[selectedProvider] && availableModels[selectedProvider].length > 0) ||
                 (persistedModels[selectedProvider] && persistedModels[selectedProvider].length > 0) ? (
                  <>
                    <select
                      value={selectedModel[selectedProvider] || ''}
                      onChange={(e) => setSelectedModel(prev => ({ ...prev, [selectedProvider]: e.target.value }))}
                      className="w-full p-2 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs"
                    >
                      <option value="">请选择模型...</option>
                      {(availableModels[selectedProvider] || persistedModels[selectedProvider] || []).map((model) => (
                        <option key={model.id} value={model.id}>
                          {model.name || model.id} {model.maxTokens ? `(${model.maxTokens.toLocaleString()} tokens)` : ''}
                        </option>
                      ))}
                    </select>

                    {selectedModel[selectedProvider] && (
                      <div className="text-xs text-secondary-themed bg-themed/5 p-2 rounded">
                        <div>已选择: {(availableModels[selectedProvider] || persistedModels[selectedProvider] || []).find(m => m.id === selectedModel[selectedProvider])?.name || selectedModel[selectedProvider]}</div>
                        {(availableModels[selectedProvider] || persistedModels[selectedProvider] || []).find(m => m.id === selectedModel[selectedProvider])?.maxTokens && (
                          <div>最大tokens: {(availableModels[selectedProvider] || persistedModels[selectedProvider] || []).find(m => m.id === selectedModel[selectedProvider])?.maxTokens?.toLocaleString()}</div>
                        )}
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-xs text-secondary-themed bg-themed/5 p-2 rounded border border-dashed border-themed/30">
                    {loadingModels[selectedProvider] ? (
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 border border-accent-themed border-t-transparent rounded-full animate-spin"></div>
                        <span>正在获取模型列表...</span>
                      </div>
                    ) : persistedModels[selectedProvider] && persistedModels[selectedProvider].length > 0 ? (
                      <div>
                        <div>使用缓存的模型列表 ({persistedModels[selectedProvider].length} 个模型)</div>
                        <div className="mt-1">点击"获取模型"按钮刷新最新列表</div>
                      </div>
                    ) : (
                      <div>
                        <div>暂无可用模型</div>
                        <div className="mt-1">请先配置API密钥，然后点击"获取模型"按钮</div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between pt-3 border-t border-themed/20">
                <button
                  onClick={() => handleProviderSwitch(selectedProvider)}
                  disabled={loading || selectedProviderInfo.status === 'error' || selectedProviderInfo.current}
                  className={`px-4 py-1.5 rounded text-xs font-medium ${
                    selectedProviderInfo.current
                      ? 'bg-green-500 text-white cursor-default'
                      : selectedProviderInfo.status === 'ok'
                      ? 'bg-accent-themed text-white hover:bg-accent-themed/80'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  {selectedProviderInfo.current ? '当前使用' : '设为默认'}
                </button>

                <button
                  onClick={onClose}
                  className="px-4 py-1.5 border border-themed/30 rounded text-xs text-secondary-themed hover:bg-themed/10"
                >
                  关闭
                </button>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center text-secondary-themed">
              请选择一个AI提供商进行配置
            </div>
          )}
        </div>
      </div>

      {/* Add Provider Modal */}
      {showAddProvider && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-60">
          <div className="bg-element-themed rounded-lg shadow-xl w-[70vw] max-w-sm p-4">
            <h3 className="text-base font-semibold text-primary-themed mb-3">新增 OpenAI 兼容供应商</h3>

            <div className="space-y-3">
              <div>
                <label className="block text-xs text-secondary-themed mb-1">供应商名称</label>
                <input
                  type="text"
                  value={newProviderName}
                  onChange={(e) => setNewProviderName(e.target.value)}
                  placeholder="例如：DeepSeek、智谱AI、月之暗面等"
                  className="w-full p-2 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs"
                />
              </div>

              <div>
                <label className="block text-xs text-secondary-themed mb-1">API 地址</label>
                <input
                  type="url"
                  value={newProviderUrl}
                  onChange={(e) => setNewProviderUrl(e.target.value)}
                  placeholder="例如：https://api.deepseek.com"
                  className="w-full p-2 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs"
                />
                <p className="text-[10px] text-secondary-themed mt-1">
                  只需输入 /v1 前面的部分，系统会自动添加 /v1
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-4">
              <button
                onClick={() => setShowAddProvider(false)}
                className="px-3 py-1.5 border border-themed/30 rounded text-xs text-secondary-themed hover:bg-themed/10"
              >
                取消
              </button>
              <button
                onClick={handleSaveNewProvider}
                className="px-3 py-1.5 bg-accent-themed text-white rounded text-xs hover:bg-accent-themed/80"
              >
                添加
              </button>
            </div>
          </div>
        </div>
      )}
    </div>,
    document.body
  );
};
