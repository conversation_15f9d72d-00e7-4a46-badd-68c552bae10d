
import React, { useState, useEffect, useContext, useRef } from 'react';
import { UIText, IMAGE_GENERATION_API_CONFIG } from '../constants'; 
import { ThemeContext } from '../contexts/ThemeContext';

interface ImageDisplayProps {
  finalImageUrl: string; 
}

const ImageDisplay: React.FC<ImageDisplayProps> = ({ finalImageUrl }) => {
  const errorPlaceholderUrl = `https://via.placeholder.com/${IMAGE_GENERATION_API_CONFIG.resolution.replace('x','/')}/cc0000/ffffff?text=${encodeURIComponent(UIText.errorImagePlaceholder)}`;
  
  const [currentImageSrc, setCurrentImageSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const lastSuccessfulUrlRef = useRef<string>('');
  
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  useEffect(() => {
    if (!finalImageUrl || finalImageUrl.trim() === "") {
      setCurrentImageSrc(''); 
      setIsLoading(false);
      // Keep lastSuccessfulUrlRef.current as is, it represents the last valid image.
      // If it should be cleared when finalImageUrl is empty, that logic could be added here.
      return;
    }

    // Only proceed if the finalImageUrl is different from what we last successfully loaded or are currently trying to load
    // This helps prevent re-loading the same image unnecessarily.
    if (finalImageUrl === currentImageSrc && !isLoading) { // If it's already displayed and not loading
      return;
    }
    if (finalImageUrl === lastSuccessfulUrlRef.current && currentImageSrc === finalImageUrl && !isLoading) { // Defensive check
        return;
    }


    setIsLoading(true);
    // setCurrentImageSrc(finalImageUrl); // Set src immediately for optimistic display, error/load will handle actual content

    const img = new window.Image();
    img.onload = () => {
      setCurrentImageSrc(finalImageUrl); // Set to final image on successful load
      lastSuccessfulUrlRef.current = finalImageUrl;
      setIsLoading(false);
    };
    img.onerror = () => {
      console.warn(`ImageDisplay: Failed to load image: ${finalImageUrl}`);
      if (lastSuccessfulUrlRef.current && lastSuccessfulUrlRef.current !== errorPlaceholderUrl && lastSuccessfulUrlRef.current.trim() !== "") {
        setCurrentImageSrc(lastSuccessfulUrlRef.current);
      } else {
        setCurrentImageSrc(errorPlaceholderUrl);
      }
      setIsLoading(false);
    };
    img.src = finalImageUrl; // Start loading

    return () => { 
      img.onload = null;
      img.onerror = null;
    };
  }, [finalImageUrl, errorPlaceholderUrl]); // Effect only re-runs if finalImageUrl or errorPlaceholderUrl changes


  const imageTransitionClasses = isLoading && currentImageSrc !== errorPlaceholderUrl
    ? 'opacity-0 scale-105 blur-md' 
    : 'opacity-100 scale-100 blur-0';

  return (
    <div 
      className="w-full h-full overflow-hidden relative transition-colors duration-300"
      style={{ backgroundColor: 'var(--image-placeholder-bg)' }} 
    >
      {currentImageSrc && currentImageSrc.trim() !== "" && ( 
        <img
          key={currentImageSrc} 
          src={currentImageSrc}
          alt={UIText.sceneAltText}
          className={`w-full h-full object-cover transition-all duration-700 ease-in-out ${imageTransitionClasses}`}
          // The img.onerror in useEffect handles the primary error logic.
          // This in-tag onError is a final fallback, though less likely to be hit with the new logic.
          onError={() => { 
            if (currentImageSrc !== errorPlaceholderUrl) {
                setCurrentImageSrc(errorPlaceholderUrl);
            }
          }}
        />
      )}
    </div>
  );
};

export default ImageDisplay;
