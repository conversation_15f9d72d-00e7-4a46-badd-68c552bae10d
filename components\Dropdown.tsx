// components/Dropdown.tsx

import React, { useState, useRef, useEffect, useCallback } from 'react';

interface DropdownItem {
  id: string;
  label: string;
  icon?: React.FC<React.SVGProps<SVGSVGElement>>;
  onClick: () => void;
  disabled?: boolean;
}

interface DropdownProps {
  triggerButton: (toggleDropdown: () => void, isOpen: boolean, buttonRef: React.RefObject<HTMLButtonElement>) => React.ReactNode;
  items: DropdownItem[];
  dropdownAlign?: 'left' | 'right';
  menuWidthClass?: string; // e.g., 'w-56'
}

const Dropdown: React.FC<DropdownProps> = ({ 
    triggerButton, 
    items, 
    dropdownAlign = 'right',
    menuWidthClass = 'w-60' // Default width increased
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null); // Ref for the trigger button

  const toggleDropdown = useCallback(() => setIsOpen(prev => !prev), []);

  const closeDropdown = useCallback(() => setIsOpen(false), []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        closeDropdown();
      }
    };
    const handleEscapeKey = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
            closeDropdown();
        }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, closeDropdown]);

  return (
    <div className="relative inline-block text-left">
      {triggerButton(toggleDropdown, isOpen, buttonRef)}
      {isOpen && (
        <div
          ref={dropdownRef}
          className={`origin-top-${dropdownAlign} absolute ${dropdownAlign === 'right' ? 'right-0' : 'left-0'} mt-2 ${menuWidthClass} rounded-md shadow-lg bg-secondary-themed ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border border-themed animate-fadeIn`}
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="options-menu" // Consider making this dynamic if multiple dropdowns use it
        >
          <div className="py-1" role="none">
            {items.map((item) => (
              <button
                key={item.id}
                onClick={() => { item.onClick(); closeDropdown(); }}
                disabled={item.disabled}
                className="w-full text-left flex items-center px-3 py-2 text-sm text-primary-themed hover:bg-element-themed hover:text-accent-themed disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
                role="menuitem"
                title={item.label}
              >
                {item.icon && <item.icon className="w-4 h-4 mr-2.5 flex-shrink-0" />}
                <span className="truncate flex-grow">{item.label}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dropdown;