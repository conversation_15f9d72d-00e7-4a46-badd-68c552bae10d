
import { LocalStorageKeys } from '../types';
import { CACHED_BACKGROUNDS_MAX_SIZE } from '../constants';

export const loadCachedBackgrounds = (): string[] => {
  const cached = localStorage.getItem(LocalStorageKeys.CACHED_BACKGROUNDS);
  try {
    const parsed = cached ? JSON.parse(cached) : [];
    return Array.isArray(parsed) ? parsed.filter(item => typeof item === 'string') : [];
  } catch (e) {
    console.error("Failed to parse cached backgrounds:", e);
    return [];
  }
};

export const saveToBackgroundCache = (newUrl: string): void => {
  if (typeof newUrl !== 'string' || !newUrl.startsWith('http')) return; // Basic validation
  let cachedUrls = loadCachedBackgrounds();
  // Add new URL to the beginning and remove duplicates
  cachedUrls = [newUrl, ...cachedUrls.filter(url => url !== newUrl)];
  // Limit cache size
  if (cachedUrls.length > CACHED_BACKGROUNDS_MAX_SIZE) {
    cachedUrls = cachedUrls.slice(0, CACHED_BACKGROUNDS_MAX_SIZE);
  }
  localStorage.setItem(LocalStorageKeys.CACHED_BACKGROUNDS, JSON.stringify(cachedUrls));
};

export const getRandomFromBackgroundCache = (): string | null => {
  const cachedUrls = loadCachedBackgrounds();
  if (cachedUrls.length === 0) return null;
  return cachedUrls[Math.floor(Math.random() * cachedUrls.length)];
};
