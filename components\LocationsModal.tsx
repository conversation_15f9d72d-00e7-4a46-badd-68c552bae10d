
import React, { useContext } from 'react';
import { VisitedLocation } from '../types';
import { Icons, UIText } from '../constants';
import { ThemeContext } from '../contexts/ThemeContext';

interface LocationsModalProps {
  isOpen: boolean;
  locations: VisitedLocation[];
  onClose: () => void;
  enableBackdropBlur?: boolean;
}

const LocationsModal: React.FC<LocationsModalProps> = ({ isOpen, locations, onClose, enableBackdropBlur = true }) => {
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  if (!isOpen) return null;

  const mainBgClasses = "fixed inset-0 bg-black/60 flex items-center justify-center z-[70] p-4";
  const mainBgBlurClass = enableBackdropBlur ? "backdrop-blur-sm" : "";
  const contentAreaId = "locations-modal-content";

  return (
    <div
      className={`${mainBgClasses} ${mainBgBlurClass}`}
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="locations-modal-title"
      aria-describedby={contentAreaId}
    >
      <div
        className="bg-secondary-themed p-5 md:p-6 rounded-xl shadow-themed-xl w-full max-w-lg border border-themed flex flex-col max-h-[80vh]"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4 flex-shrink-0">
          <h3 id="locations-modal-title" className="text-lg font-semibold text-accent-themed flex items-center">
            <Icons.Map className="w-5 h-5 mr-2" />
            {UIText.map}
          </h3>
          <button
            onClick={onClose}
            className="p-1 text-primary-themed hover:text-accent-themed rounded-md hover:bg-element-themed/50 transition-colors"
            aria-label={UIText.closeModal}
          >
            <Icons.Close className="w-5 h-5" />
          </button>
        </div>
        
        <div id={contentAreaId} className="text-primary-themed text-sm overflow-y-auto flex-grow pr-1">
          {locations.length === 0 ? (
            <p className="italic text-secondary-themed text-center py-4">{UIText.noLocations}</p>
          ) : (
            <ul className="space-y-3">
              {locations.sort((a,b) => b.firstVisited - a.firstVisited).map(loc => (
                <li 
                  key={loc.id} 
                  className="p-2.5 rounded-md shadow-sm"
                  style={{
                    backgroundColor: 'var(--rpg-slot-bg)',
                    border: '1px solid var(--rpg-slot-border)',
                  }}
                >
                  <p className="font-semibold text-[var(--rpg-text-primary)]">{loc.name}</p>
                  <p className="text-xs text-[var(--rpg-text-secondary)] mt-0.5">首次访问: {new Date(loc.firstVisited).toLocaleDateString()}</p>
                  <p className="text-xs text-[var(--rpg-text-secondary)] mt-1">{loc.description}</p>
                  {loc.notes && <p className="text-xs text-[var(--rpg-text-accent)] opacity-80 mt-1 italic">笔记: {loc.notes}</p>}
                </li>
              ))}
            </ul>
          )}
        </div>
         <div className="mt-6 flex justify-end flex-shrink-0">
            <button
              onClick={onClose}
              className="btn-dreamy btn-dreamy-sm"
            >
              {UIText.closeModal}
            </button>
        </div>
      </div>
    </div>
  );
};

export default LocationsModal;
