#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 MemoryAble Setup Script');
console.log('==========================\n');

// Step 1: Clean npm cache and node_modules
console.log('Step 1: Cleaning previous installation...');
try {
  if (fs.existsSync('node_modules')) {
    console.log('  - Removing node_modules directory...');
    execSync('rm -rf node_modules', { stdio: 'inherit' });
  }
  
  if (fs.existsSync('package-lock.json')) {
    console.log('  - Removing package-lock.json...');
    fs.unlinkSync('package-lock.json');
  }
  
  console.log('  - Cleaning npm cache...');
  execSync('npm cache clean --force', { stdio: 'inherit' });
  console.log('✅ Cleanup completed\n');
} catch (error) {
  console.log('⚠️  Cleanup had some issues, continuing...\n');
}

// Step 2: Create necessary directories
console.log('Step 2: Creating directories...');
const directories = [
  'local-server',
  'local-server/data',
  'local-server/data/saves',
  'local-server/data/characters',
  'local-server/data/worldbooks',
  'local-server/data/regex',
  'local-server/data/images',
  'local-server/data/backups',
  'local-server/uploads',
  'logs'
];

directories.forEach(dir => {
  try {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`  ✅ Created ${dir}`);
  } catch (error) {
    if (error.code !== 'EEXIST') {
      console.log(`  ⚠️  Could not create ${dir}: ${error.message}`);
    }
  }
});
console.log('✅ Directories created\n');

// Step 3: Setup environment file
console.log('Step 3: Setting up environment configuration...');
const envPath = '.env.local';
const envExamplePath = '.env.local.example';

if (!fs.existsSync(envPath)) {
  if (fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath);
    console.log('  ✅ Copied .env.local.example to .env.local');
  } else {
    // Create basic .env.local
    const basicEnv = `# MemoryAble Configuration
GEMINI_API_KEY=
LOCAL_SERVER_PORT=3001
LOCAL_SERVER_URL=http://localhost:3001
STORAGE_PROVIDER=local
IMAGE_PROVIDER=cloud
ENABLE_ADVANCED_REGEX=true
ENABLE_CHARACTER_RELATIONSHIPS=true
ENABLE_WORLDBOOK_HIERARCHY=true
NODE_ENV=development
`;
    fs.writeFileSync(envPath, basicEnv);
    console.log('  ✅ Created basic .env.local file');
  }
} else {
  console.log('  ✅ .env.local already exists');
}
console.log('✅ Environment configuration ready\n');

// Step 4: Install dependencies
console.log('Step 4: Installing dependencies...');
try {
  console.log('  - Installing main dependencies...');
  execSync('npm install --no-optional', { stdio: 'inherit' });
  console.log('✅ Dependencies installed successfully\n');
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  console.log('\nTroubleshooting tips:');
  console.log('1. Make sure you have Node.js 16+ installed');
  console.log('2. Try running: npm cache clean --force');
  console.log('3. Check your internet connection');
  console.log('4. Try deleting node_modules and package-lock.json, then run npm install again');
  process.exit(1);
}

// Step 5: Verify installation
console.log('Step 5: Verifying installation...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log(`  ✅ Package: ${packageJson.name} v${packageJson.version}`);
  
  // Check if key dependencies are installed
  const keyDeps = ['react', 'react-dom', '@google/genai', 'vite'];
  keyDeps.forEach(dep => {
    try {
      require.resolve(dep);
      console.log(`  ✅ ${dep} installed`);
    } catch (error) {
      console.log(`  ⚠️  ${dep} not found`);
    }
  });
  
  console.log('✅ Installation verified\n');
} catch (error) {
  console.log('⚠️  Could not verify installation completely\n');
}

// Step 6: Show completion message
console.log('🎉 Setup Complete!');
console.log('==================\n');

console.log('Next steps:');
console.log('1. Configure your API key in .env.local (optional):');
console.log('   GEMINI_API_KEY=your_api_key_here\n');

console.log('2. Start the development servers:');
console.log('   npm run dev:local    # Starts both local server and web app');
console.log('   # OR start them separately:');
console.log('   npm run local-server # Terminal 1');
console.log('   npm run dev         # Terminal 2\n');

console.log('3. Open your browser to:');
console.log('   http://localhost:5173\n');

console.log('4. Local server will be available at:');
console.log('   http://localhost:3001\n');

console.log('Configuration files:');
console.log('• .env.local - Environment configuration');
console.log('• local-server/data/ - Local data storage');
console.log('• logs/ - Application logs\n');

console.log('Need help?');
console.log('• Check README.md for detailed usage instructions');
console.log('• Review .env.local.example for configuration options');
console.log('• Submit issues on GitHub for support\n');

console.log('Happy storytelling! 📚✨');
