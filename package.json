{"name": "memoryable", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "local-server": "node local-server/index.js", "dev:local": "concurrently \"npm run local-server\" \"npm run dev\"", "install:setup": "node install.js", "postinstall": "node install.js", "test": "echo \"Tests not yet implemented\" && exit 0", "test:integration": "echo \"Integration tests not yet implemented\" && exit 0", "test:performance": "echo \"Performance tests not yet implemented\" && exit 0", "lint": "echo \"Linting not yet configured\" && exit 0", "format": "echo \"Formatting not yet configured\" && exit 0"}, "dependencies": {"@": "latest", "react": "^19.1.0", "react-dom": "^19.1.0", "@google/genai": "^1.1.0", "marked": "^15.0.12", "dompurify": "^3.2.6", "axios": "^1.6.0", "express": "^4.18.0", "cors": "^2.8.5", "multer": "^1.4.5", "uuid": "^9.0.0", "jszip": "^3.10.0", "file-saver": "^2.0.5", "react-beautiful-dnd": "^13.1.1", "react-select": "^5.8.0", "react-virtualized": "^9.22.5"}, "devDependencies": {"@types/node": "^22.14.0", "@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/multer": "^1.4.0", "@types/uuid": "^9.0.0", "@types/file-saver": "^2.0.0", "@types/react-beautiful-dnd": "^13.1.0", "typescript": "~5.7.2", "vite": "^6.2.0", "concurrently": "^8.2.0"}}