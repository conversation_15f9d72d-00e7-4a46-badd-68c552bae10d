{"name": "memoryable", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "local-server": "node local-server/index.js", "dev:local": "concurrently \"npm run local-server\" \"npm run dev\"", "setup": "node setup.js", "clean": "rm -rf node_modules package-lock.json && npm cache clean --force", "reinstall": "npm run clean && npm install"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@google/genai": "^1.1.0", "marked": "^15.0.12", "dompurify": "^3.2.6", "axios": "^1.6.0", "express": "^4.18.0", "cors": "^2.8.5", "multer": "^1.4.5", "uuid": "^9.0.0", "jszip": "^3.10.0", "file-saver": "^2.0.5", "react-beautiful-dnd": "^13.1.1", "react-select": "^5.8.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/multer": "^1.4.0", "@types/uuid": "^9.0.0", "@types/file-saver": "^2.0.0", "@types/react-beautiful-dnd": "^13.1.0", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.0.0", "vite": "^5.0.0", "concurrently": "^8.2.0"}}