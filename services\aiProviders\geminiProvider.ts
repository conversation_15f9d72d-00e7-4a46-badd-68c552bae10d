import { GoogleGenAI, GenerateContentResponse, Content } from "@google/genai";
import {
  BaseAIProvider,
  AIProviderConfig,
  AIProviderCapabilities,
  AIModelInfo,
  AIMessage,
  AIResponse,
  AIStreamChunk,
  AIGenerationConfig,
  AIProviderError,
  AIRateLimitError,
  AIQuotaExceededError,
  convertFromGeminiMessages,
  convertToGeminiMessages
} from './types';

export class GeminiProvider extends BaseAIProvider {
  private client: GoogleGenAI;

  constructor(config: AIProviderConfig) {
    super(config);
    if (!config.apiKey) {
      throw new AIProviderError('Gemini API key is required', 'gemini', 'MISSING_API_KEY');
    }
    this.client = new GoogleGenAI({ apiKey: config.apiKey });
  }

  getCapabilities(): AIProviderCapabilities {
    return {
      supportsStreaming: true,
      supportsSystemMessages: true,
      supportsToolCalls: false,
      supportsImageInput: true,
      maxContextLength: 1000000, // Gemini 1.5 Pro context length
      supportedFormats: ['text', 'json']
    };
  }

  async getAvailableModels(): Promise<AIModelInfo[]> {
    return [
      {
        id: 'gemini-2.5-pro-preview-06-05',
        name: 'Gemini 2.5 Pro (最新)',
        provider: 'gemini',
        maxTokens: 1000000,
        supportsStreaming: true
      },
      {
        id: 'gemini-2.5-flash-preview-05-20',
        name: 'Gemini 2.5 Flash (均衡)',
        provider: 'gemini',
        maxTokens: 1000000,
        supportsStreaming: true
      },
      {
        id: 'gemini-2.5-pro-preview-05-06',
        name: 'Gemini 2.5 Pro',
        provider: 'gemini',
        maxTokens: 1000000,
        supportsStreaming: true
      },
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        provider: 'gemini',
        maxTokens: 2000000,
        supportsStreaming: true
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        provider: 'gemini',
        maxTokens: 1000000,
        supportsStreaming: true
      }
    ];
  }

  async validateConfig(): Promise<boolean> {
    if (!this.config.apiKey) {
      throw new AIProviderError('Gemini API key is required', 'gemini', 'MISSING_API_KEY');
    }

    try {
      // Test with a simple request using the correct API
      const model = this.client.getGenerativeModel({
        model: this.config.defaultModel || 'gemini-2.5-flash-preview-05-20'
      });

      const result = await model.generateContent({
        contents: [{ role: 'user', parts: [{ text: 'Hello' }] }],
        generationConfig: { maxOutputTokens: 1 }
      });

      return !!result.response;
    } catch (error: any) {
      if (error.message?.includes('API_KEY_INVALID') || error.status === 401) {
        throw new AIProviderError('Invalid Gemini API key', 'gemini', 'INVALID_API_KEY', 401);
      }
      throw new AIProviderError(`Gemini validation failed: ${error.message}`, 'gemini', 'VALIDATION_ERROR');
    }
  }

  async testConnection(): Promise<{ success: boolean; responseTime: number; error?: string; modelCount?: number }> {
    const startTime = Date.now();

    try {
      const isValid = await this.validateConfig();
      const models = await this.getAvailableModels();
      const responseTime = Date.now() - startTime;

      return {
        success: isValid,
        responseTime,
        modelCount: models.length
      };
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      return {
        success: false,
        responseTime,
        error: error.message || 'Connection test failed'
      };
    }
  }

  async generateResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): Promise<AIResponse> {
    const validatedConfig = this.validateGenerationConfig(config);
    const formattedMessages = this.formatMessages(messages);

    // Convert to Gemini format
    const geminiMessages = convertToGeminiMessages(formattedMessages);
    
    // Extract system message if present
    let systemInstruction = '';
    const contents: Content[] = [];
    
    for (const msg of geminiMessages) {
      if (msg.role === 'system') {
        systemInstruction = msg.parts[0].text;
      } else {
        contents.push(msg);
      }
    }

    try {
      const model = this.client.getGenerativeModel({
        model: this.config.defaultModel || 'gemini-2.5-flash-preview-05-20',
        ...(systemInstruction && { systemInstruction })
      });

      const request: any = {
        contents,
        generationConfig: {
          temperature: validatedConfig.temperature,
          maxOutputTokens: validatedConfig.maxTokens,
          topP: validatedConfig.topP,
          topK: validatedConfig.topK,
          stopSequences: validatedConfig.stopSequences
        }
      };

      const response: GenerateContentResponse = await model.generateContent(request);

      if (!response.text) {
        throw new AIProviderError('No response from Gemini', 'gemini', 'NO_RESPONSE');
      }

      return {
        content: response.text,
        finishReason: this.mapGeminiFinishReason(response.finishReason),
        usage: response.usageMetadata ? {
          promptTokens: response.usageMetadata.promptTokenCount || 0,
          completionTokens: response.usageMetadata.candidatesTokenCount || 0,
          totalTokens: response.usageMetadata.totalTokenCount || 0
        } : undefined,
        model: this.config.defaultModel,
        provider: 'gemini'
      };
    } catch (error: any) {
      this.handleGeminiError(error);
    }
  }

  async* generateStreamResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): AsyncGenerator<AIStreamChunk, void, unknown> {
    const validatedConfig = this.validateGenerationConfig(config);
    const formattedMessages = this.formatMessages(messages);

    // Convert to Gemini format
    const geminiMessages = convertToGeminiMessages(formattedMessages);
    
    // Extract system message if present
    let systemInstruction = '';
    const contents: Content[] = [];
    
    for (const msg of geminiMessages) {
      if (msg.role === 'system') {
        systemInstruction = msg.parts[0].text;
      } else {
        contents.push(msg);
      }
    }

    try {
      const model = this.client.getGenerativeModel({
        model: this.config.defaultModel || 'gemini-2.5-flash-preview-05-20',
        ...(systemInstruction && { systemInstruction })
      });

      const request: any = {
        contents,
        generationConfig: {
          temperature: validatedConfig.temperature,
          maxOutputTokens: validatedConfig.maxTokens,
          topP: validatedConfig.topP,
          topK: validatedConfig.topK,
          stopSequences: validatedConfig.stopSequences
        }
      };

      const stream = model.generateContentStream(request);

      for await (const chunk of stream) {
        if (chunk.text) {
          yield {
            content: chunk.text,
            isComplete: false,
            usage: chunk.usageMetadata ? {
              promptTokens: chunk.usageMetadata.promptTokenCount || 0,
              completionTokens: chunk.usageMetadata.candidatesTokenCount || 0,
              totalTokens: chunk.usageMetadata.totalTokenCount || 0
            } : undefined
          };
        }
      }

      yield { content: '', isComplete: true };
    } catch (error: any) {
      this.handleGeminiError(error);
    }
  }

  private mapGeminiFinishReason(reason?: string): AIResponse['finishReason'] {
    switch (reason) {
      case 'STOP':
        return 'stop';
      case 'MAX_TOKENS':
        return 'length';
      case 'SAFETY':
        return 'content_filter';
      default:
        return 'stop';
    }
  }

  private handleGeminiError(error: any): never {
    const message = error.message || 'Unknown Gemini error';
    
    if (message.includes('API_KEY_INVALID')) {
      throw new AIProviderError('Invalid Gemini API key', 'gemini', 'INVALID_API_KEY', 401);
    }
    
    if (message.includes('QUOTA_EXCEEDED')) {
      throw new AIQuotaExceededError('gemini');
    }
    
    if (message.includes('RATE_LIMIT_EXCEEDED')) {
      throw new AIRateLimitError('gemini');
    }
    
    if (message.includes('SAFETY')) {
      throw new AIProviderError('Content filtered by Gemini safety systems', 'gemini', 'CONTENT_FILTER');
    }
    
    throw new AIProviderError(`Gemini error: ${message}`, 'gemini', 'UNKNOWN_ERROR');
  }
}
