
import { Part } from "@google/genai";
import { InventoryItem, VisitedLocation, Quest, CharacterProfile, DialogueLine, ImportantEvent, PlayerStatus } from '../types';
import { UIText, AVAILABLE_SUMMARY_MODELS, defaultCoreAttributes } from '../constants';
import { ai } from './geminiClient'; // Import the initialized 'ai' instance

const API_KEY = process.env.API_KEY; // Still needed for the initial check

const callSummarizationModel = async <T>(
    modelId: string,
    prompt: string,
    contextDescription: string
  ): Promise<T | null> => {
    if (!API_KEY) {
      console.error(`API Key missing for ${contextDescription}.`);
      throw new Error(UIText.errorApiKeyMissing);
    }
    const summaryModelToUse = AVAILABLE_SUMMARY_MODELS.find(m => m.id === modelId)?.id || AVAILABLE_SUMMARY_MODELS[0].id;

    try {
      const response = await ai.models.generateContent({
        model: summaryModelToUse,
        contents: [{ role: "user", parts: [{ text: prompt } as Part] }],
        config: { responseMimeType: "application/json", temperature: 0.2 }
      });

      if (response.text) {
        let jsonStr = response.text.trim();
        const fenceRegex = /^```(?:json)?\s*\n?(.*?)\n?\s*```$/s;
        const match = jsonStr.match(fenceRegex);
        if (match && match[1]) {
          jsonStr = match[1].trim();
        }
        return JSON.parse(jsonStr) as T;
      }
      console.warn(`Empty response from summarization model for ${contextDescription}.`);
      return null;
    } catch (error: any) {
      console.error(`Error during ${contextDescription} with model ${summaryModelToUse}:`, error.message, "Prompt:", prompt.substring(0, 500));
      throw new Error(UIText.errorSummarizationFailed);
    }
};

const getSummarizationContext = (dialogueLog: DialogueLine[], turnsToConsider: number = 10): string => {
    return dialogueLog
        .slice(-turnsToConsider)
        .map(line => {
            let contextLine = `${line.speakerName}: ${line.text}`;
            if (line.storyUpdateForSummary) {
                contextLine += ` (提示: ${line.storyUpdateForSummary})`;
            } else if(line.storyUpdate) {
                 contextLine += ` (背景: ${line.storyUpdate})`;
            }
            return contextLine;
        })
        .join('\n');
};

export const summarizeInventory = async (
    dialogueLog: DialogueLine[],
    currentInventory: InventoryItem[],
    summaryModelId: string
): Promise<InventoryItem[]> => {
    const context = getSummarizationContext(dialogueLog);
    const prompt = `你是一名专业的游戏日志分析师，负责精确管理玩家的背包物品。
根据以下最近发生的游戏事件和当前的背包状态，返回一个*完整且已更新*的背包物品列表。

当前背包物品 (JSON数组格式):
${JSON.stringify(currentInventory, null, 2)}

最近发生的游戏事件 (对话和故事更新):
(请特别关注明确的物品获取、使用、或丢失的提示，例如 "[物品获得: 神秘的钥匙 x1]", "[物品消耗: 治疗药水 x1]", "[剧情提示: 玩家找到了3个金币]" 等)
${context}

# 你的任务与规则：
1.  **输出格式**: 返回一个JSON对象数组。每个对象代表一个物品，必须包含以下字段：
    *   \`id\`: 字符串。物品的唯一标识符 (例如 "rusty_key", "healing_potion")。对于已存在物品，ID必须保持不变。新物品需分配新的、有意义的ID (通常是物品英文名的小写并用下划线连接)。
    *   \`name\`: 字符串。用户友好的物品名称 (例如 "生锈的钥匙", "治疗药水")。
    *   \`description\`: 字符串。物品的简短描述。
    *   \`quantity\`: 数字。物品的数量。

2.  **物品管理规则**:
    *   **获得新种类物品**: 如果事件表明玩家获得了之前背包中没有的物品，将其作为新条目添加到列表中，并设定其初始数量。
    *   **获得已有物品**: 如果事件表明玩家获得了背包中已有的物品，请**增加**该物品的现有数量。不要创建重复的物品条目。
    *   **消耗/丢失物品**: 如果事件表明玩家消耗、使用或丢失了物品，请**减少**该物品的数量。
    *   **数量为零**: 如果任何物品的数量减少到零或更少，必须将其从列表中**彻底移除**。
    *   **描述更新**: 如果事件提供了物品的新描述或更新了旧描述，请更新对应物品的 \`description\` 字段。
    *   **精确性**: 严格根据对话和故事更新中明确的物品变动事件来操作。不要猜测或假定物品的获取/丢失。

3.  **输出要求**:
    *   **只返回JSON数组。**
    *   数组中的每个对象都必须符合上述物品结构。
    *   不要在JSON数据前后添加任何解释性文字、注释或Markdown标记。
`;
    const updatedInventory = await callSummarizationModel<InventoryItem[]>(summaryModelId, prompt, "inventory update");
    return updatedInventory || currentInventory;
};

export const summarizeLocations = async (
    dialogueLog: DialogueLine[],
    currentLocations: VisitedLocation[],
    summaryModelId: string
): Promise<VisitedLocation[]> => {
    const context = getSummarizationContext(dialogueLog);
    const prompt = `你是一名专业的游戏日志分析师。请根据最近发生的游戏事件，更新玩家已探索的地点列表。
当前已探索地点 (JSON数组格式):
${JSON.stringify(currentLocations, null, 2)}

最近发生的游戏事件 (对话和故事更新，请重点关注明确提到地点发现或进入新区域的内容，例如 "[地点发现: ...]" 或 "进入了..." 等提示):
${context}

你的任务是返回一个*完整且已更新*的已探索地点列表，格式为JSON对象数组。每个对象必须包含 'id' (字符串，唯一标识符，可以是地点名称的小写并用下划线连接，例如 "old_library")，'name' (字符串，用户友好的地点名称)，'description' (字符串，地点的简短描述)，'firstVisited' (数字，首次访问的时间戳 - 对新地点使用当前时间戳 ${Date.now()}，对现有地点保留旧的时间戳)，以及可选的 'notes' (字符串，关于地点的额外笔记)。
- 如果发现了新地点，请将其添加到列表中。
- 如果揭示了关于现有地点的更多细节，请更新其 'description' 或 'notes'。
- 务必保留现有地点的 'id'。
请只返回JSON数组。`;
    const updatedLocations = await callSummarizationModel<VisitedLocation[]>(summaryModelId, prompt, "locations update");
    return updatedLocations || currentLocations;
};

export const summarizeQuests = async (
    dialogueLog: DialogueLine[],
    currentQuests: Quest[],
    summaryModelId: string
): Promise<Quest[]> => {
    const context = getSummarizationContext(dialogueLog);
    const prompt = `你是一名专业的游戏日志分析师。请根据最近发生的游戏事件，更新玩家的任务日志。
当前任务 (JSON数组格式):
${JSON.stringify(currentQuests, null, 2)}

最近发生的游戏事件 (对话和故事更新，请重点关注明确提到任务开始、更新或完成的内容，例如 "[任务开始: ...]", "[任务更新: ...]", "[任务完成: ...]" 等提示)。**注意：更广泛的线索或一般情报应记录在“重要事件”中，此处仅关注明确的任务目标和状态。**
${context}

你的任务是返回一个*完整且已更新*的任务日志，格式为JSON对象数组。每个对象必须包含 'id' (字符串，唯一标识符，可以是任务标题的小写并用下划线连接，例如 "find_lost_cat")，'title' (字符串，任务标题)，'description' (字符串，任务描述)，'objectives' (字符串数组，任务目标列表)，'status' (字符串，必须是 'active', 'completed', 或 'failed' 之一)，以及 'lastUpdated' (数字，最后更新的时间戳 - 使用当前时间戳 ${Date.now()})。
- 如果开始了新任务，请将其添加到列表中，状态为 "active"。
- 如果任务目标达成或描述发生变化，请更新它。
- 如果任务完成或失败，请更新其 'status'。
- 务必保留现有任务的 'id'。
请只返回JSON数组。`;
    const updatedQuests = await callSummarizationModel<Quest[]>(summaryModelId, prompt, "quests update");
    return updatedQuests || currentQuests;
};

export const summarizeCharacters = async (
    dialogueLog: DialogueLine[],
    currentCharacterProfiles: CharacterProfile[],
    summaryModelId: string
): Promise<CharacterProfile[]> => {
    const context = getSummarizationContext(dialogueLog);
    const prompt = `你是一名专业的游戏日志分析师。请根据最近发生的游戏事件，更新角色档案。
当前角色档案 (JSON数组格式):
${JSON.stringify(currentCharacterProfiles, null, 2)}

最近发生的游戏事件 (对话和故事更新，请重点关注明确提到新角色出现、角色关系变化或重要的角色互动内容，例如 "[新角色出现: ...]", "[关系提升: ...]" 等提示):
${context}

你的任务是返回一个*完整且已更新*的角色档案列表，格式为JSON对象数组。每个对象必须包含 'id' (字符串，唯一标识符，通常是角色的名字小写)，'name' (字符串，角色名字)，'description' (字符串，角色的关键特征或简介)，'relationshipLevel' (数字，例如0-10表示好感度)，'notableInteractions' (字符串数组，关键互动时刻的简短记录)，以及 'firstEncountered' (数字，首次遇见的时间戳 - 对新角色使用当前时间戳 ${Date.now()})。
- 如果遇到了新角色，请添加其档案。
- 如果角色关系发生变化或发生重要互动，请更新 'relationshipLevel' 并将互动记录添加到 'notableInteractions'。
- 如果揭示了新的角色特征，请更新 'description'。
- 务必保留现有角色的 'id'。
请只返回JSON数组。`;
    const updatedProfiles = await callSummarizationModel<CharacterProfile[]>(summaryModelId, prompt, "characters update");
    return updatedProfiles || currentCharacterProfiles;
};

export const summarizeImportantEvents = async (
    dialogueLog: DialogueLine[],
    currentImportantEvents: ImportantEvent[],
    summaryModelId: string
): Promise<ImportantEvent[]> => {
    const context = getSummarizationContext(dialogueLog, 15); // Consider more turns for events
    const prompt = `你是一名专业的游戏日志分析师，负责从对话和事件中提取对玩家有用的重要信息、线索、情报和世界观片段。
当前已记录的重要事件 (JSON数组格式，每个对象包含 'id', 'text', 'timestamp', 'category', 'source'):
${JSON.stringify(currentImportantEvents, null, 2)}

最近发生的游戏事件 (对话和故事更新):
${context}

# 你的任务与规则：
1.  **提取内容**: 从“最近发生的游戏事件”中识别并提取以下类型的信息：
    *   **关键线索 (clue)**: 直接指向谜题解决、任务进展或秘密发现的信息。
    *   **重要情报 (intel)**: 关于角色、地点、组织、计划等可能影响玩家决策或理解世界的重要信息。
    *   **世界观片段 (lore)**: 揭示游戏世界背景、历史、规则等的叙述。
    *   **有用笔记 (note)**: 玩家可能希望记录下来的其他杂项有用信息。
2.  **避免重复**: 如果某个信息与“当前已记录的重要事件”中的某条目本质上是重复的，或者只是对已有事件的微小补充，则优先更新现有条目（如果适用）或不新增。目标是记录*新的、有价值的*信息。
3.  **排除任务目标**: 不要记录明确属于“任务日志”中具体任务目标的内容。此列表专注于更广泛的线索和情报。
4.  **简洁明了**: 提取的信息应保持简洁，突出核心内容。
5.  **输出格式**: 返回一个JSON对象数组。每个对象代表一个重要事件，必须包含：
    *   \`id\`: 字符串。唯一标识符 (例如 "clue_ancient_symbol_meaning", "intel_yuki_true_identity")。对于已存在事件，ID必须保持不变。新事件需分配新的、有意义的ID (通常是事件核心内容的小写英文并用下划线连接，确保唯一性)。
    *   \`text\`: 字符串。事件/线索的具体内容。
    *   \`timestamp\`: 数字。事件发生或记录的时间戳 (对新事件使用当前时间戳 ${Date.now()})。
    *   \`category\`: 字符串 (可选)。可以是 'clue', 'intel', 'note', 'lore'。
    *   \`source\`: 字符串 (可选)。简述信息来源，例如 "与Yuki的对话", "在图书馆发现的笔记"。
6.  **完整列表**: 返回包含所有旧事件和新提取事件的*完整列表*。如果旧事件没有更新，也应包含在内。
7.  **只返回JSON数组。**不要添加任何解释性文字。
`;
    const updatedEvents = await callSummarizationModel<ImportantEvent[]>(summaryModelId, prompt, "important events update");
    return updatedEvents || currentImportantEvents;
};


export const summarizeStoryForContinuation = async (
  dialogueLog: DialogueLine[],
  playerStatus: PlayerStatus,
  summaryModelId: string
): Promise<string> => {
  const context = dialogueLog
    .slice(-20) // Consider more context for continuation
    .map(line => `${line.speakerName}: ${line.text}${line.storyUpdateForSummary ? ` (Context: ${line.storyUpdateForSummary})` : ''}`)
    .join('\n');
  
  const coreAttrs = playerStatus.coreAttributes || defaultCoreAttributes;

  const prompt = `
你是一名经验丰富的游戏叙事者（GM）。你的任务是根据以下游戏历史和玩家当前状态，生成一段自然的承上启下的剧情摘要，作为故事在新开局时的旁白。
这段摘要必须流畅地衔接之前的剧情，让玩家感觉故事从未中断。

当前玩家状态：
- 姓名: ${playerStatus.name || '玩家'}
- 心情: ${playerStatus.mood || '平静'}
- 元气值: ${playerStatus.healthEnergy.current}/${playerStatus.healthEnergy.max}
- 等级: ${playerStatus.level || 1}, 经验: ${playerStatus.xp || 0}/${playerStatus.xpToNextLevel || 100}
- 当前任务: ${playerStatus.quests?.find(q => q.status === 'active')?.title || '无'}
- 主要属性: (力量: ${coreAttrs.strength}, 敏捷: ${coreAttrs.agility}, 智力: ${coreAttrs.intelligence}, 魅力: ${coreAttrs.charisma}, 幸运: ${coreAttrs.luck}, 心智韧性: ${coreAttrs.sanity})

最近的游戏对话历史（最后几条，可能包含玩家的行动和NPC的反应）：
${context}

请基于以上信息，特别是对话历史的结尾和玩家的即时状态（例如，如果玩家刚说完一句话，或刚完成一个动作，或场景有明确的未尽事宜），创作一段【旁白】。
这段旁白应该：
1.  作为故事的延续点，自然地承接最近发生的事件或玩家最后的行动意图。
2.  从叙事者的角度书写，语言风格应与之前的游戏保持一致。
3.  简洁地描绘出玩家当前所处的场景和氛围，或者点明接下来的即时情境。
4.  能够引导玩家自然地做出下一个选择或行动。
5.  **不要提及“总结”、“摘要”、“继续”、“重新开始”或任何元信息。** 就像故事从未间断一样。
6.  输出必须是简体中文，并且直接是旁白内容，不包含任何前缀或标签，也不要包含角色名称作为发言者。
7.  玩家的角色状态（物品、任务、属性等）由游戏系统保留，此摘要重点是提供**叙事上的上下文衔接**。

例如，如果最后是玩家说：“我决定先去调查那个发光的符文。”
一个好的承接旁白可能是：“${playerStatus.name || '你'}小心翼翼地靠近了那个在昏暗角落中散发着微光的符文，古老的纹路似乎在轻微地脉动，空气中隐约传来低沉的嗡鸣……”

请生成承接旁白（JSON格式，包含一个名为 "story_summary" 的字段，其值为旁白字符串）：
`;
  const summaryResponse = await callSummarizationModel<{ story_summary: string }>(summaryModelId, prompt, "story continuation summary");
  return summaryResponse?.story_summary || `在${playerStatus.name || '某个地方'}，故事继续着...`; // Fallback
};
