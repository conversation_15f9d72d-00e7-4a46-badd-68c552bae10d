# MemoryAble Troubleshooting Guide

## Common Installation Issues

### 1. Invalid Package Name Error (`EINVALIDPACKAGENAME`)

**Error:**
```
npm error code EINVALIDPACKAGENAME
npm error Invalid package name "@" of package "@@latest"
```

**Solution:**
This has been fixed in the latest package.json. If you still see this error:

```bash
# Clean everything and reinstall
npm run clean
npm install

# Or use the setup script
npm run setup
```

### 2. Node.js Version Issues

**Error:**
```
npm error engine Unsupported engine
```

**Solution:**
Make sure you have Node.js 16 or higher:

```bash
# Check your Node.js version
node --version

# If you need to update Node.js:
# Visit https://nodejs.org/ and download the latest LTS version
```

### 3. Permission Errors

**Error:**
```
npm error EACCES: permission denied
```

**Solution:**
```bash
# On macOS/Linux, try:
sudo npm install -g npm@latest

# Or fix npm permissions:
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.profile
source ~/.profile

# Then try installing again
npm install
```

### 4. Network/Proxy Issues

**Error:**
```
npm error network request failed
```

**Solution:**
```bash
# Clear npm cache
npm cache clean --force

# Try with different registry
npm install --registry https://registry.npmjs.org/

# If behind corporate firewall:
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080
```

## Runtime Issues

### 1. Local Server Won't Start

**Error:**
```
Error: listen EADDRINUSE :::3001
```

**Solution:**
```bash
# Check what's using port 3001
lsof -i :3001

# Kill the process or use a different port
LOCAL_SERVER_PORT=3002 npm run local-server

# Or update .env.local:
echo "LOCAL_SERVER_PORT=3002" >> .env.local
```

### 2. Vite Development Server Issues

**Error:**
```
Error: Could not resolve "@/components/..."
```

**Solution:**
Make sure your vite.config.ts has the correct alias configuration:

```typescript
resolve: {
  alias: {
    '@': path.resolve(__dirname, '.'),
  }
}
```

### 3. API Key Issues

**Error:**
```
Error: API key not configured
```

**Solution:**
1. Create or edit `.env.local`:
```bash
GEMINI_API_KEY=your_actual_api_key_here
```

2. Get an API key from: https://makersuite.google.com/app/apikey

3. Restart the development server after adding the key

### 4. Import Path Issues

**Error:**
```
Module not found: Can't resolve './types'
```

**Solution:**
Check if you have both `types.ts` and `src/types.ts`. Use the correct import path:

```typescript
// If types.ts is in root:
import { GameSettingsData } from './types';

// If types.ts is in src/:
import { GameSettingsData } from './src/types';
```

## Development Issues

### 1. TypeScript Errors

**Error:**
```
Type 'X' is not assignable to type 'Y'
```

**Solution:**
1. Make sure all TypeScript dependencies are installed:
```bash
npm install --save-dev typescript @types/react @types/react-dom
```

2. Check your tsconfig.json configuration

3. Restart your IDE/editor

### 2. React Version Conflicts

**Error:**
```
Warning: Invalid hook call
```

**Solution:**
Make sure you have compatible React versions:

```bash
# Check installed versions
npm list react react-dom

# If versions don't match, reinstall:
npm uninstall react react-dom
npm install react@^18.2.0 react-dom@^18.2.0
```

### 3. Build Issues

**Error:**
```
Build failed with errors
```

**Solution:**
1. Clear build cache:
```bash
rm -rf dist
npm run build
```

2. Check for TypeScript errors:
```bash
npx tsc --noEmit
```

3. Make sure all imports are correct

## Performance Issues

### 1. Slow Development Server

**Solution:**
1. Reduce cache size in `.env.local`:
```
CACHE_SIZE_MB=25
MAX_CONCURRENT_REQUESTS=2
```

2. Disable some features temporarily:
```
ENABLE_ADVANCED_REGEX=false
ENABLE_CHARACTER_RELATIONSHIPS=false
```

### 2. High Memory Usage

**Solution:**
1. Restart the development server periodically
2. Close unused browser tabs
3. Reduce the number of open character cards/world books

### 3. Slow Character Card Import

**Solution:**
1. Import smaller batches of files
2. Use JSON format instead of PNG when possible
3. Check available disk space

## File and Data Issues

### 1. Character Cards Won't Import

**Solution:**
1. Verify file format (JSON/PNG)
2. Check file size (should be < 10MB)
3. Validate JSON format:
```bash
# Test JSON validity
cat your-character.json | python -m json.tool
```

### 2. Local Data Not Saving

**Solution:**
1. Check directory permissions:
```bash
ls -la local-server/data/
```

2. Make sure local server is running:
```bash
curl http://localhost:3001/api/health
```

3. Check browser console for errors

### 3. Lost Configuration

**Solution:**
1. Check if `.env.local` exists
2. Restore from `.env.local.example`:
```bash
cp .env.local.example .env.local
```

3. Reconfigure your settings

## Browser Issues

### 1. Blank Page

**Solution:**
1. Check browser console for errors (F12)
2. Clear browser cache and cookies
3. Try incognito/private mode
4. Make sure JavaScript is enabled

### 2. Features Not Working

**Solution:**
1. Update to a modern browser (Chrome 90+, Firefox 88+, Safari 14+)
2. Disable browser extensions temporarily
3. Check if local storage is enabled

### 3. Import/Export Not Working

**Solution:**
1. Check if file API is supported
2. Allow file downloads in browser settings
3. Try a different browser

## Getting Help

If you're still having issues:

1. **Check the logs:**
   - Browser console (F12)
   - Local server logs in terminal
   - Check `logs/` directory if it exists

2. **Gather information:**
   - Operating system and version
   - Node.js version (`node --version`)
   - npm version (`npm --version`)
   - Browser and version
   - Exact error message

3. **Try the nuclear option:**
   ```bash
   # Complete clean reinstall
   rm -rf node_modules package-lock.json
   npm cache clean --force
   npm install
   ```

4. **Submit an issue:**
   - Include all the information from step 2
   - Describe what you were trying to do
   - Include the exact error message
   - Mention if it worked before

## Quick Fixes Checklist

- [ ] Node.js 16+ installed
- [ ] npm cache cleared (`npm cache clean --force`)
- [ ] No `node_modules` conflicts (delete and reinstall)
- [ ] Correct API key in `.env.local`
- [ ] Local server port not in use
- [ ] Browser supports modern JavaScript
- [ ] File permissions correct for local-server/data/
- [ ] No proxy/firewall blocking requests
- [ ] TypeScript dependencies installed
- [ ] React versions compatible

## Emergency Reset

If nothing else works:

```bash
# 1. Backup your data
cp -r local-server/data/ backup-data/

# 2. Complete reset
rm -rf node_modules package-lock.json .env.local
npm cache clean --force

# 3. Fresh install
npm install
npm run setup

# 4. Restore your data
cp -r backup-data/* local-server/data/

# 5. Reconfigure
cp .env.local.example .env.local
# Edit .env.local with your settings
```
