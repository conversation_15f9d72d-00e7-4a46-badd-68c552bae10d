
import React, { useContext } from 'react';
import NotificationItem from './NotificationItem';
import { NotificationMessage, NotificationContextType } from '../types';
import { NotificationContext } from '../contexts/NotificationContext';

const NotificationsContainer: React.FC = () => {
  const context = useContext(NotificationContext);
  
  if (!context) {
    // This should not happen if the component is used within NotificationProvider
    console.error("NotificationsContainer: NotificationContext is undefined. Ensure it's rendered within NotificationProvider.");
    return null; 
  }

  const { notifications, removeNotification } = context;

  if (!notifications || notifications.length === 0) {
    return null;
  }

  return (
    <div
      className="fixed top-5 right-5 flex flex-col items-end space-y-2 z-[100]" // Changed positioning to top-right
      aria-live="polite"
      aria-atomic="true"
    >
      {notifications.map(notification => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onDismiss={removeNotification} // Pass removeNotification from context
        />
      ))}
    </div>
  );
};

export default NotificationsContainer;