
import React from 'react';
import { GameSaveData, GameSettingsData, UserPreferences, NotificationType, OpeningLineStyle } from '../types';
import { Icons, AppTitleStyled, UIText, APP_VERSION, APP_TITLE_CN } from '../constants';
import ImageDisplay from './ImageDisplay';
import SaveLoadManager from './SaveLoadManager';

interface StartScreenProps {
  playerName: string;
  onPlayerNameChange: (name: string) => void;
  dynamicOpeningLine: string;
  finalBackgroundUrl: string;
  isLoadingBackground: boolean;
  onStartGame: () => void;
  // Save/Load props
  gameSaves: GameSaveData[];
  onSaveGame: (name: string) => Promise<string | null>;
  onLoadGame: (saveId: string) => void;
  onDeleteSave: (saveId: string) => void;
  onRenameSave: (saveId: string, newName: string) => void;
  addNotification: (message: string, type: NotificationType, duration?: number) => void;
  onRequestConfirmation: (title: string, message: string, onConfirm: () => void, confirmText?: string, cancelText?: string) => void;
  gameSettings: GameSettingsData; 
  userPreferences: UserPreferences; 
  onGameSettingsChange: (newSettings: Partial<GameSettingsData>) => void; 
  onUserPreferencesChange: (newPreferences: UserPreferences) => void; 
  onExportData: () => void;
  onImportData: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onResetAllSettingsToDefaults: () => void; 
  // GDrive props are removed
}

const StartScreen: React.FC<StartScreenProps> = ({
  playerName,
  onPlayerNameChange,
  dynamicOpeningLine,
  finalBackgroundUrl,
  isLoadingBackground, 
  onStartGame,
  gameSaves,
  onSaveGame,
  onLoadGame,
  onDeleteSave,
  onRenameSave,
  addNotification,
  onRequestConfirmation,
  gameSettings, 
  userPreferences,
  onGameSettingsChange,
  onUserPreferencesChange,
  onExportData,
  onImportData,
  onResetAllSettingsToDefaults, 
}) => {
  return (
    <div className="w-screen h-screen flex flex-col items-center justify-center relative text-primary-themed transition-colors duration-300">
      <ImageDisplay finalImageUrl={finalBackgroundUrl} />
      {isLoadingBackground && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black/50 text-white p-3 rounded-md shadow-lg z-20">
          {UIText.loadingScene}
        </div>
      )}
      <div className="absolute inset-0 flex flex-col items-center justify-center p-4 z-10"> {/* Overlay content */}
        <div className="bg-secondary-themed/80 backdrop-blur-md p-6 md:p-8 rounded-xl shadow-themed-xl text-center max-w-lg w-full">
          <h1 className="text-5xl md:text-6xl font-bold app-title-enhanced mb-3">
            <AppTitleStyled />
          </h1>
          <p className="text-sm md:text-md text-primary-themed italic mb-6 min-h-[20px] md:min-h-[24px]">
            {dynamicOpeningLine || UIText.dynamicOpeningLineFallback}
          </p>

          <div className="mb-6">
            <input
              type="text"
              value={playerName}
              onChange={(e) => onPlayerNameChange(e.target.value)}
              placeholder={UIText.characterNamePlaceholder}
              className="w-full max-w-xs mx-auto p-3 bg-element-themed text-primary-themed rounded-lg ring-accent-themed focus:outline-none placeholder-themed border border-themed text-lg shadow-sm"
              aria-label={UIText.enterYourNameContext}
            />
          </div>

          <button
            onClick={onStartGame}
            disabled={!playerName.trim()}
            className="w-full max-w-xs mx-auto btn-dreamy text-lg py-3"
          >
            {UIText.startYourStory}
          </button>

          <div className="mt-6 pt-4 border-t border-themed/30">
            <SaveLoadManager
              saves={gameSaves}
              onSave={onSaveGame}
              onLoad={onLoadGame}
              onDelete={onDeleteSave}
              onRename={onRenameSave}
              addNotification={addNotification}
              onRequestConfirmation={onRequestConfirmation}
              isCompact={true} 
              maxHeight="max-h-32" 
              onExportData={onExportData}
              onImportData={onImportData}
              onResetAllSettingsToDefaults={onResetAllSettingsToDefaults}
              gameSettings={gameSettings} 
              onGameSettingsChange={onGameSettingsChange}
              // GDrive props removed
            />
          </div>
        </div>
      </div>
      <footer className="absolute bottom-4 text-center text-xs text-primary-themed/70 z-0">
        <p>{UIText.gameCopyright}</p>
        <p>{APP_VERSION} - {APP_TITLE_CN}</p>
      </footer>
    </div>
  );
};

export default StartScreen;
