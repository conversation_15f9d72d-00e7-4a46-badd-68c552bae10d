
import React, { useEffect, useState, useContext, useCallback } from 'react';
import { NotificationMessage, NotificationType } from '../types';
import { Icons, UIText } from '../constants'; 
import { ThemeContext } from '../contexts/ThemeContext';

interface NotificationItemProps {
  notification: NotificationMessage;
  onDismiss: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onDismiss }) => {
  const [isExiting, setIsExiting] = useState(false);
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  // handleDismiss is now only for manual 'x' button clicks
  const handleDismiss = useCallback(() => {
    setIsExiting(true); // Trigger exit animation
    // Actual removal from state will happen after animation by calling onDismiss
    setTimeout(() => {
      onDismiss(notification.id);
    }, 400); // Match CSS exit animation duration
  }, [notification.id, onDismiss]);

  // Removed useEffect for auto-dismissal. Context handles removal from the list.
  // If animation is desired on auto-dismissal (unmount), NotificationsContainer would need react-transition-group.

  let typeStyles = {
    bgGradientStart: 'var(--notification-info-bg-gradient-start)',
    bgGradientEnd: 'var(--notification-info-bg-gradient-end)',
    text: 'var(--notification-info-text)',
    border: 'var(--notification-info-border)',
    iconColor: 'var(--notification-info-icon)', // Renamed for clarity
  };
  let IconComponent: React.FC<React.SVGProps<SVGSVGElement>> = Icons.InformationCircle;

  switch (notification.type) {
    case 'success':
      typeStyles = { 
        bgGradientStart: 'var(--notification-success-bg-gradient-start)',
        bgGradientEnd: 'var(--notification-success-bg-gradient-end)',
        text: 'var(--notification-success-text)', 
        border: 'var(--notification-success-border)', 
        iconColor: 'var(--notification-success-icon)' 
      };
      IconComponent = Icons.CheckCircle;
      break;
    case 'error':
      typeStyles = { 
        bgGradientStart: 'var(--notification-error-bg-gradient-start)',
        bgGradientEnd: 'var(--notification-error-bg-gradient-end)',
        text: 'var(--notification-error-text)', 
        border: 'var(--notification-error-border)', 
        iconColor: 'var(--notification-error-icon)' 
      };
      IconComponent = Icons.XCircle;
      break;
    case 'warning':
      typeStyles = { 
        bgGradientStart: 'var(--notification-warning-bg-gradient-start)',
        bgGradientEnd: 'var(--notification-warning-bg-gradient-end)',
        text: 'var(--notification-warning-text)', 
        border: 'var(--notification-warning-border)', 
        iconColor: 'var(--notification-warning-icon)' 
      };
      IconComponent = Icons.ExclamationTriangle;
      break;
    case 'achievement':
      typeStyles = { 
        bgGradientStart: 'var(--notification-achievement-bg-gradient-start)',
        bgGradientEnd: 'var(--notification-achievement-bg-gradient-end)',
        text: 'var(--notification-achievement-text)', 
        border: 'var(--notification-achievement-border)', 
        iconColor: 'var(--notification-achievement-icon)' 
      };
      IconComponent = Icons.Trophy; 
      break;
  }

  return (
    <div
      style={{
        background: `linear-gradient(135deg, ${typeStyles.bgGradientStart}, ${typeStyles.bgGradientEnd})`,
        borderColor: typeStyles.border,
        boxShadow: '0 4px 12px -2px var(--notification-shadow)', // Updated shadow
        borderRadius: 'var(--notification-border-radius)',
        fontFamily: "'LXGW WenKai Screen', 'Noto Sans SC', sans-serif", // Apply elegant font
        textShadow: 'var(--notification-text-shadow)',
      }}
      className={`
        w-full max-w-sm px-3 py-2.5  /* Increased padding and max-width */
        border flex items-center space-x-2
        ${isExiting ? 'animate-notifyExit' : 'animate-notifyEnter'}
      `}
      role="alert"
      aria-live="assertive"
    >
      <div style={{ color: typeStyles.iconColor }} className="flex-shrink-0">
        <IconComponent className="w-5 h-5" /> {/* Slightly larger icon */}
      </div>
      <div className="flex-grow min-w-0">
        {notification.title && (
          <p style={{ color: typeStyles.text }} className="text-sm font-semibold truncate"> {/* Increased font size */}
            {notification.title}
          </p>
        )}
        <p style={{ color: typeStyles.text }} className={`text-sm ${notification.title ? 'mt-0.5' : ''} break-words leading-snug`}> {/* Increased font size and adjusted leading */}
          {notification.message}
        </p>
      </div>
      <button
        onClick={handleDismiss}
        style={{ color: typeStyles.text }}
        className="flex-shrink-0 p-1 rounded-md hover:bg-black/10 dark:hover:bg-white/10 transition-colors opacity-70 hover:opacity-100"
        aria-label={UIText.closeModal}
      >
        <Icons.Close className="w-4 h-4" /> {/* Icon size adjusted */}
      </button>
    </div>
  );
};

export default NotificationItem;
