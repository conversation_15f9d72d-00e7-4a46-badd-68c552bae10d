import {
  BaseAIProvider,
  AIProviderConfig,
  AIProviderCapabilities,
  AIModelInfo,
  AIMessage,
  AIResponse,
  AIStreamChunk,
  AIGenerationConfig,
  AIProviderError,
  AIRateLimitError,
  AIQuotaExceededError
} from './types';

export class AnthropicProvider extends BaseAIProvider {
  private baseUrl: string;

  constructor(config: AIProviderConfig) {
    super(config);
    this.baseUrl = config.baseUrl || 'https://api.anthropic.com/v1';
  }

  getCapabilities(): AIProviderCapabilities {
    return {
      supportsStreaming: true,
      supportsSystemMessages: true,
      supportsToolCalls: true,
      supportsImageInput: true,
      maxContextLength: 200000, // Claude 3 context length
      supportedFormats: ['text', 'json']
    };
  }

  async getAvailableModels(): Promise<AIModelInfo[]> {
    return [
      {
        id: 'claude-3-5-sonnet-20241022',
        name: '<PERSON> 3.5 Sonnet (最新)',
        provider: 'anthropic',
        maxTokens: 200000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.003, output: 0.015 }
      },
      {
        id: 'claude-3-5-haiku-20241022',
        name: 'Claude 3.5 Haiku (快速)',
        provider: 'anthropic',
        maxTokens: 200000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.0008, output: 0.004 }
      },
      {
        id: 'claude-3-opus-20240229',
        name: 'Claude 3 Opus (最强)',
        provider: 'anthropic',
        maxTokens: 200000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.015, output: 0.075 }
      },
      {
        id: 'claude-3-sonnet-20240229',
        name: 'Claude 3 Sonnet',
        provider: 'anthropic',
        maxTokens: 200000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.003, output: 0.015 }
      },
      {
        id: 'claude-3-haiku-20240307',
        name: 'Claude 3 Haiku',
        provider: 'anthropic',
        maxTokens: 200000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.00025, output: 0.00125 }
      }
    ];
  }

  async validateConfig(): Promise<boolean> {
    if (!this.config.apiKey) {
      throw new AIProviderError('Anthropic API key is required', 'anthropic', 'MISSING_API_KEY');
    }

    try {
      const response = await fetch(`${this.baseUrl}/messages`, {
        method: 'POST',
        headers: {
          'x-api-key': this.config.apiKey,
          'anthropic-version': '2023-06-01',
          'content-type': 'application/json',
          ...this.config.customHeaders
        },
        body: JSON.stringify({
          model: this.config.defaultModel || 'claude-3-5-haiku-20241022',
          max_tokens: 1,
          messages: [{ role: 'user', content: 'Hello' }]
        })
      });

      if (response.status === 401) {
        throw new AIProviderError('Invalid Anthropic API key', 'anthropic', 'INVALID_API_KEY', 401);
      }

      return response.ok;
    } catch (error) {
      if (error instanceof AIProviderError) throw error;
      throw new AIProviderError(`Anthropic validation failed: ${error.message}`, 'anthropic', 'VALIDATION_ERROR');
    }
  }

  async testConnection(): Promise<{ success: boolean; responseTime: number; error?: string; modelCount?: number }> {
    const startTime = Date.now();

    try {
      const isValid = await this.validateConfig();
      const models = await this.getAvailableModels();
      const responseTime = Date.now() - startTime;

      return {
        success: isValid,
        responseTime,
        modelCount: models.length
      };
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      return {
        success: false,
        responseTime,
        error: error.message || 'Connection test failed'
      };
    }
  }

  async generateResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): Promise<AIResponse> {
    const validatedConfig = this.validateGenerationConfig(config);
    const formattedMessages = this.formatMessages(messages);

    // Separate system message from other messages
    let systemMessage = '';
    const conversationMessages: AIMessage[] = [];

    for (const msg of formattedMessages) {
      if (msg.role === 'system') {
        systemMessage = msg.content;
      } else {
        conversationMessages.push(msg);
      }
    }

    const requestBody: any = {
      model: this.config.defaultModel || 'claude-3-5-haiku-20241022',
      max_tokens: validatedConfig.maxTokens,
      messages: conversationMessages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: validatedConfig.temperature,
      top_p: validatedConfig.topP,
      stop_sequences: validatedConfig.stopSequences,
      stream: false
    };

    if (systemMessage) {
      requestBody.system = systemMessage;
    }

    try {
      const response = await fetch(`${this.baseUrl}/messages`, {
        method: 'POST',
        headers: {
          'x-api-key': this.config.apiKey!,
          'anthropic-version': '2023-06-01',
          'content-type': 'application/json',
          ...this.config.customHeaders
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        await this.handleErrorResponse(response);
      }

      const data = await response.json();

      if (!data.content || !data.content[0]) {
        throw new AIProviderError('No response from Anthropic', 'anthropic', 'NO_RESPONSE');
      }

      return {
        content: data.content[0].text || '',
        finishReason: this.mapAnthropicStopReason(data.stop_reason),
        usage: data.usage ? {
          promptTokens: data.usage.input_tokens,
          completionTokens: data.usage.output_tokens,
          totalTokens: data.usage.input_tokens + data.usage.output_tokens
        } : undefined,
        model: data.model,
        provider: 'anthropic'
      };
    } catch (error) {
      if (error instanceof AIProviderError) throw error;
      throw new AIProviderError(`Anthropic request failed: ${error.message}`, 'anthropic', 'REQUEST_ERROR');
    }
  }

  async* generateStreamResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): AsyncGenerator<AIStreamChunk, void, unknown> {
    const validatedConfig = this.validateGenerationConfig(config);
    const formattedMessages = this.formatMessages(messages);

    // Separate system message from other messages
    let systemMessage = '';
    const conversationMessages: AIMessage[] = [];

    for (const msg of formattedMessages) {
      if (msg.role === 'system') {
        systemMessage = msg.content;
      } else {
        conversationMessages.push(msg);
      }
    }

    const requestBody: any = {
      model: this.config.defaultModel || 'claude-3-5-haiku-20241022',
      max_tokens: validatedConfig.maxTokens,
      messages: conversationMessages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: validatedConfig.temperature,
      top_p: validatedConfig.topP,
      stop_sequences: validatedConfig.stopSequences,
      stream: true
    };

    if (systemMessage) {
      requestBody.system = systemMessage;
    }

    try {
      const response = await fetch(`${this.baseUrl}/messages`, {
        method: 'POST',
        headers: {
          'x-api-key': this.config.apiKey!,
          'anthropic-version': '2023-06-01',
          'content-type': 'application/json',
          ...this.config.customHeaders
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        await this.handleErrorResponse(response);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new AIProviderError('No response stream from Anthropic', 'anthropic', 'NO_STREAM');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                yield { content: '', isComplete: true };
                return;
              }

              try {
                const parsed = JSON.parse(data);
                
                if (parsed.type === 'content_block_delta' && parsed.delta?.text) {
                  yield {
                    content: parsed.delta.text,
                    isComplete: false
                  };
                } else if (parsed.type === 'message_delta' && parsed.usage) {
                  yield {
                    content: '',
                    isComplete: false,
                    usage: {
                      promptTokens: 0,
                      completionTokens: parsed.usage.output_tokens || 0,
                      totalTokens: parsed.usage.output_tokens || 0
                    }
                  };
                } else if (parsed.type === 'message_stop') {
                  yield { content: '', isComplete: true };
                  return;
                }
              } catch (e) {
                // Skip invalid JSON lines
                continue;
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      if (error instanceof AIProviderError) throw error;
      throw new AIProviderError(`Anthropic stream failed: ${error.message}`, 'anthropic', 'STREAM_ERROR');
    }
  }

  private mapAnthropicStopReason(reason?: string): AIResponse['finishReason'] {
    switch (reason) {
      case 'end_turn':
        return 'stop';
      case 'max_tokens':
        return 'length';
      case 'stop_sequence':
        return 'stop';
      default:
        return 'stop';
    }
  }

  private async handleErrorResponse(response: Response): Promise<never> {
    const errorData = await response.json().catch(() => ({}));
    const errorMessage = errorData.error?.message || `HTTP ${response.status}`;

    switch (response.status) {
      case 401:
        throw new AIProviderError('Invalid Anthropic API key', 'anthropic', 'INVALID_API_KEY', 401);
      case 429:
        const retryAfter = response.headers.get('retry-after');
        throw new AIRateLimitError('anthropic', retryAfter ? parseInt(retryAfter) : undefined);
      case 402:
        throw new AIQuotaExceededError('anthropic');
      case 400:
        throw new AIProviderError(`Bad request: ${errorMessage}`, 'anthropic', 'BAD_REQUEST', 400);
      case 500:
      case 502:
      case 503:
        throw new AIProviderError(`Anthropic server error: ${errorMessage}`, 'anthropic', 'SERVER_ERROR', response.status);
      default:
        throw new AIProviderError(`Anthropic error: ${errorMessage}`, 'anthropic', 'UNKNOWN_ERROR', response.status);
    }
  }
}
