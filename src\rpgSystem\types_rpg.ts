// src/rpgSystem/types_rpg.ts

// Removed: import { CoreAttributes_RPG as CoreAttributesRPG } from './types_rpg'; 

// Define these RPG-specific base types here
export interface InventoryItem_RPG {
  id: string;
  name: string;
  description: string;
  quantity: number;
}
export interface VisitedLocation_RPG {
  id: string;
  name: string;
  description: string;
  firstVisited: number;
  notes?: string;
}
export interface Quest_RPG {
  id: string;
  title: string;
  description: string;
  objectives: string[];
  status: 'active' | 'completed' | 'failed';
  lastUpdated: number;
}
export interface CharacterProfile_RPG {
  id: string;
  name: string;
  description: string;
  relationshipLevel: number;
  notableInteractions: string[];
  firstEncountered: number;
}
export interface ImportantEvent_RPG {
  id: string;
  text: string;
  timestamp: number;
  category?: 'clue' | 'intel' | 'note' | 'lore';
  source?: string;
}

export interface CoreAttributes_RPG {
  strength: number;
  agility: number;
  intelligence: number;
  charisma: number;
  luck: number;
  sanity: number;
}

export interface Skill_RPG {
  id: string; 
  name: string;
  level: number;
  description: string;
  xpToNextLevel?: number; 
  currentXp?: number;     
}

export interface StatusEffect_RPG {
  id: string; 
  name: string; 
  description?: string; 
  icon?: string; 
  durationTurns: number; 
  remainingTurns: number; 
  timestampApplied: number; 
  source?: string; 
  isBuff: boolean; 
}

// This represents the part of the player's status managed by the RPG system
export interface PlayerStatus_RPG {
  healthEnergy: { current: number; max: number };
  // specialEffects_legacy?: string[]; // Consider removing if fully migrated to StatusEffect_RPG
  currentDay: number; // Moved from PlayerStatus_Session for RPG context if day advances have RPG implications
  
  inventory: InventoryItem_RPG[];
  visitedLocations: VisitedLocation_RPG[];
  quests: Quest_RPG[];
  characterProfiles: CharacterProfile_RPG[];
  importantEvents: ImportantEvent_RPG[];
  
  level: number;
  xp: number;
  xpToNextLevel: number;
  attributePoints: number; 
  skillPoints: number;     
  coreAttributes: CoreAttributes_RPG; // Use the direct definition
  skills: Skill_RPG[];

  buffs: StatusEffect_RPG[];
  debuffs: StatusEffect_RPG[];
}

// For notifications returned by the RPG system
export interface RPGNotificationDetails {
  message: string;
  type: 'success' | 'error' | 'info' | 'warning' | 'achievement'; 
  duration?: number;
  title?: string;
  displayToUser?: boolean; // Flag to indicate if App.tsx should show this notification
}

// Type for the return value of RPG processing functions
export interface RPGProcessingResult {
  statusChangesDelta: Partial<PlayerStatus_RPG>; // The changes to be applied to PlayerStatus_RPG
  notifications: RPGNotificationDetails[];
  achievementsToUnlock: string[];
}

// Types for RPG Tag Parsing parameters (ensure keys match CoreAttributes_RPG)
export type RPGAttributeChangeParams_RPG = { attrKey: keyof CoreAttributes_RPG; changeValue: number; reason: string }; // Use direct definition
export type RPGSkillUpdateParams_RPG = { skillId: string; newLevel: number; reason: string };
export type RPGSkillXPGainParams_RPG = { skillId: string; xpAmount: number; reason: string };
export type RPGAwardPointsParams_RPG = { amount: number; reason: string };
export type RPGHealthEnergyChangeParams_RPG = { change: number; reason: string };
export type RPGHealthEnergySetParams_RPG = { current: number; max: number; reason: string };
export type RPGEffectAddParams_RPG = { type: 'buff' | 'debuff'; name: string; duration: number; description?: string; icon?: string; source?: string; };
