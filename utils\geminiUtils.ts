
import { DialogueLine, Content } from '../types';

export const convertDialogueLogToGeminiHistory = (log: DialogueLine[]): Content[] => {
  return log.map(line => {
    let role: "user" | "model";
    if (line.speakerType === 'player') {
      role = "user";
    } else { // npc or narrator
      role = "model";
    }
    // Ensure text is always a string, even if it's empty
    const textContent = typeof line.text === 'string' ? line.text : '';
    return {
      role,
      parts: [{ text: textContent }]
    };
  });
};
