import axios from 'axios';
import { GeminiResponseFormat } from '../types';

// Local server configuration
const LOCAL_SERVER_URL = process.env.LOCAL_SERVER_URL || 'http://localhost:3001';

export interface LocalApiConfig {
  serverUrl: string;
  aiEndpoint?: string;
  imageEndpoint?: string;
  timeout?: number;
}

export class LocalApiService {
  private config: LocalApiConfig;
  private isServerAvailable: boolean = false;

  constructor(config?: Partial<LocalApiConfig>) {
    this.config = {
      serverUrl: LOCAL_SERVER_URL,
      aiEndpoint: '/api/ai/generate',
      imageEndpoint: '/api/images/generate',
      timeout: 30000,
      ...config
    };
    
    this.checkServerHealth();
  }

  async checkServerHealth(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.config.serverUrl}/api/health`, {
        timeout: 5000
      });
      this.isServerAvailable = response.data.status === 'ok';
      return this.isServerAvailable;
    } catch (error) {
      console.warn('Local server not available:', error);
      this.isServerAvailable = false;
      return false;
    }
  }

  async generateAIResponse(
    prompt: string,
    model: string,
    settings: any
  ): Promise<GeminiResponseFormat> {
    if (!this.isServerAvailable) {
      await this.checkServerHealth();
    }

    if (!this.isServerAvailable) {
      throw new Error('Local AI server is not available. Please start the local server or configure cloud AI.');
    }

    try {
      const response = await axios.post(
        `${this.config.serverUrl}${this.config.aiEndpoint}`,
        { prompt, model, settings },
        { timeout: this.config.timeout }
      );

      return response.data;
    } catch (error) {
      console.error('Local AI generation failed:', error);
      throw new Error(`Local AI generation failed: ${error.message}`);
    }
  }

  async generateImage(
    prompt: string,
    style: string,
    settings: any
  ): Promise<{ imageUrl: string }> {
    if (!this.isServerAvailable) {
      await this.checkServerHealth();
    }

    if (!this.isServerAvailable) {
      throw new Error('Local image generation server is not available.');
    }

    try {
      const response = await axios.post(
        `${this.config.serverUrl}${this.config.imageEndpoint}`,
        { prompt, style, settings },
        { timeout: this.config.timeout }
      );

      return response.data;
    } catch (error) {
      console.error('Local image generation failed:', error);
      throw new Error(`Local image generation failed: ${error.message}`);
    }
  }

  async saveFile(
    file: File,
    type: 'character' | 'worldbook' | 'regex' | 'save',
    metadata?: any
  ): Promise<{ success: boolean; fileInfo: any }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }

    try {
      const response = await axios.post(
        `${this.config.serverUrl}/api/files/save`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
          timeout: this.config.timeout
        }
      );

      return response.data;
    } catch (error) {
      console.error('File save failed:', error);
      throw new Error(`File save failed: ${error.message}`);
    }
  }

  async listFiles(type: 'character' | 'worldbook' | 'regex' | 'save'): Promise<any[]> {
    try {
      const response = await axios.get(
        `${this.config.serverUrl}/api/files/list/${type}`,
        { timeout: this.config.timeout }
      );

      return response.data;
    } catch (error) {
      console.error('File list failed:', error);
      throw new Error(`File list failed: ${error.message}`);
    }
  }

  async createBackup(data: any, name?: string): Promise<{ success: boolean; backupId: string; name: string }> {
    try {
      const response = await axios.post(
        `${this.config.serverUrl}/api/backup/create`,
        { data, name },
        { timeout: this.config.timeout }
      );

      return response.data;
    } catch (error) {
      console.error('Backup creation failed:', error);
      throw new Error(`Backup creation failed: ${error.message}`);
    }
  }

  async listBackups(): Promise<Array<{ id: string; name: string; createdAt: string }>> {
    try {
      const response = await axios.get(
        `${this.config.serverUrl}/api/backup/list`,
        { timeout: this.config.timeout }
      );

      return response.data;
    } catch (error) {
      console.error('Backup list failed:', error);
      throw new Error(`Backup list failed: ${error.message}`);
    }
  }

  async restoreBackup(backupId: string): Promise<{ success: boolean; data: any }> {
    try {
      const response = await axios.get(
        `${this.config.serverUrl}/api/backup/restore/${backupId}`,
        { timeout: this.config.timeout }
      );

      return response.data;
    } catch (error) {
      console.error('Backup restore failed:', error);
      throw new Error(`Backup restore failed: ${error.message}`);
    }
  }

  isLocalServerAvailable(): boolean {
    return this.isServerAvailable;
  }

  getServerUrl(): string {
    return this.config.serverUrl;
  }
}

// Singleton instance
export const localApiService = new LocalApiService();

// Utility functions for backward compatibility
export async function checkLocalServerAvailability(): Promise<boolean> {
  return await localApiService.checkServerHealth();
}

export async function generateLocalAIResponse(
  prompt: string,
  model: string,
  settings: any
): Promise<GeminiResponseFormat> {
  return await localApiService.generateAIResponse(prompt, model, settings);
}

export async function generateLocalImage(
  prompt: string,
  style: string,
  settings: any
): Promise<{ imageUrl: string }> {
  return await localApiService.generateImage(prompt, style, settings);
}

// Enhanced error handling for local operations
export class LocalApiError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'LocalApiError';
  }
}

// Configuration helper
export function configureLocalApi(config: Partial<LocalApiConfig>): void {
  Object.assign(localApiService['config'], config);
}

// Health monitoring
export function startHealthMonitoring(intervalMs: number = 30000): () => void {
  const interval = setInterval(() => {
    localApiService.checkServerHealth();
  }, intervalMs);

  return () => clearInterval(interval);
}
