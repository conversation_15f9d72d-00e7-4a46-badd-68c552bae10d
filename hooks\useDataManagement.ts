
import { useCallback, useState } from 'react';
import { GameSaveData, GameSettingsData, UserPreferences, Theme, SettingPreset, OpeningLineStyle, BackupData, LocalStorageKeys, NotificationType } from '../types';
import { APP_VERSION, DefaultGameSettings, CACHED_BACKGROUNDS_MAX_SIZE, UIText, GIST_BACKUP_FILENAME } from '../constants';
import { ensureCustomElementsStructure } from '../utils/dataUtils';
import { loadCachedBackgrounds } from '../utils/backgroundCache';
import * as GistService from '../services/gistService'; // Import Gist service

interface DataManagementProps {
    gameSaves: GameSaveData[];
    gameSettings: GameSettingsData;
    userPreferences: UserPreferences;
    theme: Theme;
    characterCardPresets: SettingPreset[];
    userRolePresets: SettingPreset[];
    aiStylePresets: SettingPreset[];
    openingLineHistory: string[];
    lastOpeningStyle: OpeningLineStyle | null;

    addNotification: (message: string, type: NotificationType, duration?: number) => void;
    unlockAchievement: (achievementId: string) => void;
    handleRequestConfirmation: (title: string, message: string, onConfirm: () => void, confirmText?: string, cancelText?: string) => void;

    setGameSaves: React.Dispatch<React.SetStateAction<GameSaveData[]>>;
    setGameSettings: (newSettings: Partial<GameSettingsData> | ((prevState: GameSettingsData) => GameSettingsData)) => void;
    setUserPreferences: React.Dispatch<React.SetStateAction<UserPreferences>>;
    setTheme: React.Dispatch<React.SetStateAction<Theme>>;
    setCharacterCardPresets: React.Dispatch<React.SetStateAction<SettingPreset[]>>;
    setUserRolePresets: React.Dispatch<React.SetStateAction<SettingPreset[]>>;
    setAiStylePresets: React.Dispatch<React.SetStateAction<SettingPreset[]>>;
    setOpeningLineHistory: React.Dispatch<React.SetStateAction<string[]>>;
    setLastOpeningStyle: React.Dispatch<React.SetStateAction<OpeningLineStyle | null>>;
    defaultUserPreferences: UserPreferences;
}

export const useDataManagement = ({
    gameSaves, gameSettings, userPreferences, theme, characterCardPresets, userRolePresets, aiStylePresets, openingLineHistory, lastOpeningStyle,
    addNotification, unlockAchievement, handleRequestConfirmation,
    setGameSaves, setGameSettings, setUserPreferences, setTheme, setCharacterCardPresets, setUserRolePresets, setAiStylePresets, setOpeningLineHistory, setLastOpeningStyle, defaultUserPreferences
}: DataManagementProps) => {
  const [isGistLoading, setIsGistLoading] = useState<boolean>(false);

  const exportAllData = useCallback(() => {
    try {
      const backupData: BackupData = { version: APP_VERSION, exportedAt: Date.now(), gameSaves, gameSettings, userPreferences, theme,
        characterCardPresets,
        userRolePresets, aiStylePresets, openingLineHistory, lastOpeningStyle, cachedBackgrounds: loadCachedBackgrounds() };
      const jsonString = JSON.stringify(backupData, null, 2); const blob = new Blob([jsonString], { type: "application/json" }); const url = URL.createObjectURL(blob);
      const a = document.createElement("a"); a.href = url; a.download = `MemoryAble_Backup_${new Date().toISOString().slice(0,10)}.json`; document.body.appendChild(a); a.click(); document.body.removeChild(a); URL.revokeObjectURL(url);
      addNotification(UIText.exportDataSuccess, "success"); 
      unlockAchievement('data_guardian');
    } catch (error) { console.error("Error exporting data:", error); addNotification(UIText.exportDataError, "error"); }
  }, [gameSaves, gameSettings, userPreferences, theme, characterCardPresets, userRolePresets, aiStylePresets, openingLineHistory, lastOpeningStyle, addNotification, unlockAchievement]);

  const importAllData = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]; if (!file) return;
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target?.result as string) as BackupData;
        if (importedData.version !== APP_VERSION) { addNotification(UIText.importDataErrorVersionMismatch(importedData.version, APP_VERSION), "error", 10000); return; }
        
        handleRequestConfirmation(UIText.importDataConfirmTitle, UIText.importDataConfirmMessage, () => {
            setGameSaves(Array.isArray(importedData.gameSaves) ? importedData.gameSaves : []);

            // Start with DefaultGameSettings, then layer imported settings, then carefully handle Gist settings
            let restoredSettings: GameSettingsData = { ...DefaultGameSettings };
            if (importedData.gameSettings) {
                restoredSettings = { ...restoredSettings, ...importedData.gameSettings };
                restoredSettings.customNarrativeElements = ensureCustomElementsStructure(importedData.gameSettings.customNarrativeElements);

                // Gist specific settings handling
                const importedSavePat = typeof importedData.gameSettings.saveGithubPat === 'boolean'
                    ? importedData.gameSettings.saveGithubPat
                    : DefaultGameSettings.saveGithubPat;
                
                restoredSettings.saveGithubPat = importedSavePat;

                if (importedSavePat) {
                    // If imported data wants to save PAT, use its PAT if valid, otherwise keep current valid PAT if current settings also save PAT
                    if (typeof importedData.gameSettings.githubPat === 'string' && importedData.gameSettings.githubPat) {
                        restoredSettings.githubPat = importedData.gameSettings.githubPat;
                    } else if (gameSettings.saveGithubPat && typeof gameSettings.githubPat === 'string' && gameSettings.githubPat) {
                        restoredSettings.githubPat = gameSettings.githubPat; // Keep current if imported is missing but should be saved
                    } else {
                        restoredSettings.githubPat = ""; // Fallback to empty if no valid PAT found and save is true
                    }
                } else {
                    restoredSettings.githubPat = ""; // If not saving PAT, it must be empty
                }
                
                restoredSettings.gistId = typeof importedData.gameSettings.gistId === 'string' ? importedData.gameSettings.gistId : DefaultGameSettings.gistId;
                restoredSettings.enableGistAutoBackup = typeof importedData.gameSettings.enableGistAutoBackup === 'boolean' ? importedData.gameSettings.enableGistAutoBackup : DefaultGameSettings.enableGistAutoBackup;
                restoredSettings.gistAutoBackupIntervalHours = typeof importedData.gameSettings.gistAutoBackupIntervalHours === 'number' && importedData.gameSettings.gistAutoBackupIntervalHours >= 0.25
                    ? importedData.gameSettings.gistAutoBackupIntervalHours
                    : DefaultGameSettings.gistAutoBackupIntervalHours;
                restoredSettings.gistUseSystemProxy = typeof importedData.gameSettings.gistUseSystemProxy === 'boolean' 
                    ? importedData.gameSettings.gistUseSystemProxy 
                    : DefaultGameSettings.gistUseSystemProxy;
            } else { // If no gameSettings in import, ensure Gist settings are default
                 restoredSettings.githubPat = DefaultGameSettings.githubPat;
                 restoredSettings.saveGithubPat = DefaultGameSettings.saveGithubPat;
                 restoredSettings.gistId = DefaultGameSettings.gistId;
                 restoredSettings.enableGistAutoBackup = DefaultGameSettings.enableGistAutoBackup;
                 restoredSettings.gistAutoBackupIntervalHours = DefaultGameSettings.gistAutoBackupIntervalHours;
                 restoredSettings.gistUseSystemProxy = DefaultGameSettings.gistUseSystemProxy;
            }
            
            setGameSettings(restoredSettings);

            setUserPreferences(importedData.userPreferences || defaultUserPreferences);
            setTheme(importedData.theme || Theme.Light);
            setCharacterCardPresets(Array.isArray(importedData.characterCardPresets) ? importedData.characterCardPresets : []);
            setUserRolePresets(Array.isArray(importedData.userRolePresets) ? importedData.userRolePresets : []);
            setAiStylePresets(Array.isArray(importedData.aiStylePresets) ? importedData.aiStylePresets : []);
            setOpeningLineHistory(Array.isArray(importedData.openingLineHistory) ? importedData.openingLineHistory : []);
            setLastOpeningStyle(importedData.lastOpeningStyle || null);
            if (Array.isArray(importedData.cachedBackgrounds)) localStorage.setItem(LocalStorageKeys.CACHED_BACKGROUNDS, JSON.stringify(importedData.cachedBackgrounds.slice(0, CACHED_BACKGROUNDS_MAX_SIZE)));
            
            addNotification(UIText.importDataSuccess, "success", 6000); 
            unlockAchievement('data_guardian'); 
            setTimeout(() => window.location.reload(), 1500);
        });
      } catch (error) { console.error("Error importing data:", error); addNotification(UIText.importDataError, "error"); }
      finally { if (event.target) event.target.value = ""; }
    }; reader.readAsText(file);
  }, [
      addNotification, setTheme, unlockAchievement, handleRequestConfirmation,
      setGameSaves, setGameSettings, setUserPreferences, gameSettings, 
      setCharacterCardPresets, setUserRolePresets, setAiStylePresets,
      setOpeningLineHistory, setLastOpeningStyle, defaultUserPreferences
  ]);

  const handleResetAllSettingsToDefaults = useCallback(() => {
    handleRequestConfirmation(
        UIText.resetToDefaults,
        UIText.resetToDefaultsConfirmation,
        () => {
            setGameSettings(prev => {
                const preservedSettings = {
                  characterName: prev.characterName,
                  characterDescription: prev.characterDescription,
                  characterOpeningMessage: prev.characterOpeningMessage,
                  characterPersonality: prev.characterPersonality,
                  characterScenario: prev.characterScenario,
                  characterExampleDialogue: prev.characterExampleDialogue,
                  characterPortraitKeywords: prev.characterPortraitKeywords,
                  userRole: prev.userRole,
                  systemRole: prev.systemRole,
                  customNarrativeElements: (prev.customNarrativeElements || []).map(primaryEl => ({
                    ...primaryEl,
                    isActive: false, 
                    subElements: primaryEl.subElements.map(subEl => ({
                      ...subEl,
                      isActive: false, 
                    })),
                  })),
                  // Preserve Gist settings on general reset, user has to manually clear them if desired
                  githubPat: prev.githubPat,
                  gistId: prev.gistId,
                  saveGithubPat: prev.saveGithubPat,
                  enableGistAutoBackup: prev.enableGistAutoBackup,
                  gistAutoBackupIntervalHours: prev.gistAutoBackupIntervalHours,
                  gistUseSystemProxy: prev.gistUseSystemProxy,
                };

                return {
                  ...DefaultGameSettings, 
                  ...preservedSettings,  
                };
            });
            setUserPreferences(defaultUserPreferences);
            addNotification(UIText.settingsReset, "success");
        },
        UIText.confirmReset,
        UIText.cancelAction
    );
  }, [setGameSettings, setUserPreferences, addNotification, handleRequestConfirmation, defaultUserPreferences]);

  const handleInitializeGistBackup = useCallback(async () => {
    if (isGistLoading) { addNotification(UIText.gistActionInProgress, 'info'); return; }
    if (!gameSettings.githubPat) {
      addNotification(UIText.gistErrorConfigMissing, 'error');
      return;
    }
    setIsGistLoading(true);
    addNotification(UIText.gistActionInProgress, 'info');
    try {
      const foundGistId = await GistService.findOrCreateBackupGist(gameSettings.githubPat, GIST_BACKUP_FILENAME, addNotification, gameSettings);
      if (foundGistId) {
        setGameSettings(prev => ({ ...prev, gistId: foundGistId }));
      }
    } catch (error: any) {
      addNotification(error.message || UIText.gistInitFailed, 'error', 7000);
    } finally {
      setIsGistLoading(false);
    }
  }, [isGistLoading, gameSettings, addNotification, setGameSettings]);

  const handleBackupToGist = useCallback(async (isAutoBackup: boolean = false) => {
    if (isGistLoading) {
        if (!isAutoBackup) addNotification(UIText.gistActionInProgress, 'info');
        return false;
    }
    if (!gameSettings.githubPat) {
      if (!isAutoBackup) addNotification(UIText.gistErrorConfigMissing, 'error');
      return false;
    }
    if (!gameSettings.gistId) {
      if (!isAutoBackup) addNotification(UIText.gistErrorGistIdMissing, 'error');
      return false;
    }
    setIsGistLoading(true);
    if (!isAutoBackup) addNotification(UIText.gistActionInProgress, 'info');

    const backupData: BackupData = { version: APP_VERSION, exportedAt: Date.now(), gameSaves, gameSettings, userPreferences, theme,
        characterCardPresets,
        userRolePresets, aiStylePresets, openingLineHistory, lastOpeningStyle, cachedBackgrounds: loadCachedBackgrounds() };

    const success = await GistService.uploadToGist(gameSettings.githubPat, gameSettings.gistId, GIST_BACKUP_FILENAME, backupData, addNotification, gameSettings, isAutoBackup);
    if (success && !isAutoBackup) {
        unlockAchievement('cloud_sync_initiate');
    }
    setIsGistLoading(false);
    return success; 
  }, [isGistLoading, gameSettings, gameSaves, userPreferences, theme, characterCardPresets, userRolePresets, aiStylePresets, openingLineHistory, lastOpeningStyle, addNotification, unlockAchievement]);


  const handleRestoreFromGist = useCallback(async () => {
    if (isGistLoading) { addNotification(UIText.gistActionInProgress, 'info'); return; }
    if (!gameSettings.githubPat) {
      addNotification(UIText.gistErrorConfigMissing, 'error');
      return;
    }
     if (!gameSettings.gistId) {
        addNotification(UIText.gistErrorGistIdMissing, 'error');
        return;
    }
    handleRequestConfirmation(
      UIText.gistRestoreConfirmTitle,
      UIText.gistRestoreConfirmMessage,
      async () => {
        setIsGistLoading(true);
        addNotification(UIText.gistActionInProgress, 'info');

        const importedData = await GistService.downloadFromGist(gameSettings.githubPat!, gameSettings.gistId!, GIST_BACKUP_FILENAME, addNotification, gameSettings);

        if (importedData) {
          if (importedData.version !== APP_VERSION) {
            addNotification(UIText.importDataErrorVersionMismatch(importedData.version, APP_VERSION), "error", 10000);
            setIsGistLoading(false);
            return;
          }
          setGameSaves(Array.isArray(importedData.gameSaves) ? importedData.gameSaves : []);

          let restoredSettings: GameSettingsData = { ...DefaultGameSettings };
            if (importedData.gameSettings) {
                restoredSettings = { ...restoredSettings, ...importedData.gameSettings };
                restoredSettings.customNarrativeElements = ensureCustomElementsStructure(importedData.gameSettings.customNarrativeElements);

                const importedSavePat = typeof importedData.gameSettings.saveGithubPat === 'boolean'
                    ? importedData.gameSettings.saveGithubPat
                    : DefaultGameSettings.saveGithubPat;
                
                restoredSettings.saveGithubPat = importedSavePat;

                if (importedSavePat) {
                    if (typeof importedData.gameSettings.githubPat === 'string' && importedData.gameSettings.githubPat) {
                        restoredSettings.githubPat = importedData.gameSettings.githubPat;
                    } else if (gameSettings.saveGithubPat && typeof gameSettings.githubPat === 'string' && gameSettings.githubPat) {
                        restoredSettings.githubPat = gameSettings.githubPat; 
                    } else {
                        restoredSettings.githubPat = ""; 
                    }
                } else {
                    restoredSettings.githubPat = ""; 
                }
                
                restoredSettings.gistId = typeof importedData.gameSettings.gistId === 'string' ? importedData.gameSettings.gistId : DefaultGameSettings.gistId;
                restoredSettings.enableGistAutoBackup = typeof importedData.gameSettings.enableGistAutoBackup === 'boolean' ? importedData.gameSettings.enableGistAutoBackup : DefaultGameSettings.enableGistAutoBackup;
                restoredSettings.gistAutoBackupIntervalHours = typeof importedData.gameSettings.gistAutoBackupIntervalHours === 'number' && importedData.gameSettings.gistAutoBackupIntervalHours >= 0.25
                    ? importedData.gameSettings.gistAutoBackupIntervalHours
                    : DefaultGameSettings.gistAutoBackupIntervalHours;
                restoredSettings.gistUseSystemProxy = typeof importedData.gameSettings.gistUseSystemProxy === 'boolean' 
                    ? importedData.gameSettings.gistUseSystemProxy 
                    : DefaultGameSettings.gistUseSystemProxy;
            } else {
                 restoredSettings.githubPat = DefaultGameSettings.githubPat;
                 restoredSettings.saveGithubPat = DefaultGameSettings.saveGithubPat;
                 restoredSettings.gistId = DefaultGameSettings.gistId;
                 restoredSettings.enableGistAutoBackup = DefaultGameSettings.enableGistAutoBackup;
                 restoredSettings.gistAutoBackupIntervalHours = DefaultGameSettings.gistAutoBackupIntervalHours;
                 restoredSettings.gistUseSystemProxy = DefaultGameSettings.gistUseSystemProxy;
            }

          setGameSettings(restoredSettings);

          setUserPreferences(importedData.userPreferences || defaultUserPreferences);
          setTheme(importedData.theme || Theme.Light);
          setCharacterCardPresets(Array.isArray(importedData.characterCardPresets) ? importedData.characterCardPresets : []);
          setUserRolePresets(Array.isArray(importedData.userRolePresets) ? importedData.userRolePresets : []);
          setAiStylePresets(Array.isArray(importedData.aiStylePresets) ? importedData.aiStylePresets : []);
          setOpeningLineHistory(Array.isArray(importedData.openingLineHistory) ? importedData.openingLineHistory : []);
          setLastOpeningStyle(importedData.lastOpeningStyle || null);
          if (Array.isArray(importedData.cachedBackgrounds)) localStorage.setItem(LocalStorageKeys.CACHED_BACKGROUNDS, JSON.stringify(importedData.cachedBackgrounds.slice(0, CACHED_BACKGROUNDS_MAX_SIZE)));
          
          addNotification(UIText.gistRestoreSuccess, 'success');
          unlockAchievement('cloud_sync_initiate');
          setTimeout(() => window.location.reload(), 1500);
        }
        setIsGistLoading(false);
      }
    );
  }, [
      isGistLoading, gameSettings, addNotification, handleRequestConfirmation,
      setGameSaves, setGameSettings, setUserPreferences, setTheme,
      setCharacterCardPresets, setUserRolePresets, setAiStylePresets,
      setOpeningLineHistory, setLastOpeningStyle, defaultUserPreferences, unlockAchievement
  ]);

  return {
    exportAllData,
    importAllData,
    handleResetAllSettingsToDefaults,
    handleInitializeGistBackup,
    handleBackupToGist,
    handleRestoreFromGist,
    isGistLoading,
  };
};
