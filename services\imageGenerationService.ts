
import { IMAGE_GENERATION_API_CONFIG, UIText, AVA<PERSON>ABLE_IMAGE_PROMPT_STYLES } from '../constants';

// Helper to add style-specific keywords for Pollinations.ai
const getStyleSpecificKeywords = (styleId: string): string[] => {
    switch(styleId) {
        case "galgame_character_focus":
            return ["beautiful anime girl", "detailed face", "expressive eyes", "visual novel character art cg", "close up shot", "bokeh background", "bishoujo", "galge"];
        case "cinematic_scene_ambience":
            return ["wide angle shot", "dramatic lighting", "environmental storytelling", "anime movie screencap", "detailed background", "atmospheric perspective"];
        case "dynamic_action_shot":
            return ["action pose", "dynamic movement", "speed lines", "impact effect", "battle scene", "shonen anime style", "explosion"];
        case "dreamy_fantasy_world":
            return ["ethereal glow", "magical elements", "floating islands", "mystical creatures", "Studio Ghibli inspired", "fantasy landscape art"];
        case "default_anime_landscape":
        default:
            return ["serene landscape", "beautiful scenery", "anime wallpaper", "peaceful atmosphere"];
    }
};

const transformKeywordToPollinationsPrompt = (keyword: string, selectedStyleId: string): string => {
  const plotDescription = keyword.replace(/_/g, ' ').trim(); 
  
  const specificStyleEnhancers = getStyleSpecificKeywords(selectedStyleId);
  const generalQualityTags = IMAGE_GENERATION_API_CONFIG.defaultStyleTags;
  
  let promptParts: string[] = [];

  if (plotDescription) {
    promptParts.push(plotDescription);
  }

  // Add specific style enhancers next
  if (specificStyleEnhancers.length > 0) {
    promptParts = [...promptParts, ...specificStyleEnhancers];
  }
  
  // Add general quality tags last
  if (generalQualityTags.length > 0) {
    promptParts = [...promptParts, ...generalQualityTags];
  }

  // Filter for unique parts, respecting the order of importance (plot > specific > general)
  // Case-insensitive check for uniqueness.
  const uniquePromptParts: string[] = [];
  const seen = new Set<string>();
  for (const part of promptParts) {
    const trimmedPart = part.trim();
    if (trimmedPart) { // Ensure part is not empty after trimming
        const lowerCasePart = trimmedPart.toLowerCase();
        if (!seen.has(lowerCasePart)) {
            uniquePromptParts.push(trimmedPart); // Add the original (non-lowercased) part
            seen.add(lowerCasePart);
        }
    }
  }
  
  return uniquePromptParts.join(', ');
};

export const generateAnimeImage = async (
  sceneImageKeyword: string, 
  selectedImagePromptStyleId?: string 
): Promise<string> => {
  
  const currentStyleId = selectedImagePromptStyleId || AVAILABLE_IMAGE_PROMPT_STYLES[0].id;

  if (!sceneImageKeyword || sceneImageKeyword.trim() === "" || sceneImageKeyword.toLowerCase() === 'loading') {
    console.warn("Empty or loading sceneImageKeyword. Using generic anime placeholder prompt for Pollinations.");
    const genericAnimePrompt = transformKeywordToPollinationsPrompt("beautiful anime scenery, vibrant", currentStyleId);
    const encodedDefaultText = encodeURIComponent(genericAnimePrompt);
    return `${IMAGE_GENERATION_API_CONFIG.url}${encodedDefaultText}?width=${IMAGE_GENERATION_API_CONFIG.resolution.split('x')[0]}&height=${IMAGE_GENERATION_API_CONFIG.resolution.split('x')[1]}&seed=${Math.floor(Math.random() * 1000000)}&nologo=true`;
  }
  
  const API_BASE_URL = IMAGE_GENERATION_API_CONFIG.url;
  const promptForApiPath = transformKeywordToPollinationsPrompt(sceneImageKeyword, currentStyleId);
  const encodedPromptPath = encodeURIComponent(promptForApiPath);
  
  // Determine resolution based on screen width
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
  const resolutionToUse = isMobile ? IMAGE_GENERATION_API_CONFIG.mobileResolution : IMAGE_GENERATION_API_CONFIG.resolution;
  const [widthStr, heightStr] = resolutionToUse.split('x');
  const width = parseInt(widthStr, 10);
  const height = parseInt(heightStr, 10);

  let fullUrl = `${API_BASE_URL}${encodedPromptPath}?width=${width}&height=${height}`;

  if (IMAGE_GENERATION_API_CONFIG.negativePrompt && IMAGE_GENERATION_API_CONFIG.negativePrompt.trim() !== "") {
    fullUrl += `&negative_prompt=${encodeURIComponent(IMAGE_GENERATION_API_CONFIG.negativePrompt.trim())}`;
  }
  fullUrl += `&seed=${Math.floor(Math.random() * 1000000)}`;
  
  if (IMAGE_GENERATION_API_CONFIG.model) {
    fullUrl += `&model=${encodeURIComponent(IMAGE_GENERATION_API_CONFIG.model)}`;
  }
  fullUrl += `&nologo=true`;

  if (fullUrl.length > 2048) { 
    console.warn("Generated Pollinations URL is very long:", fullUrl.substring(0, 200) + "...");
    const fallbackResolution = isMobile ? IMAGE_GENERATION_API_CONFIG.mobileResolution : IMAGE_GENERATION_API_CONFIG.resolution;
    const encodedErrorText = encodeURIComponent(UIText.failedGenerateImage("Prompt too complex"));
    return `https://via.placeholder.com/${fallbackResolution.replace('x', '/')}/D10000/FFFFFF?text=${encodedErrorText}`;
  }
  return fullUrl;
};
