
// Enhanced AI Service - Now supports multiple providers (OpenAI, Gemini, Claude, etc.)
// This file maintains backward compatibility while adding multi-provider support

// Legacy exports for backward compatibility
export { createChatSession, sendMessageToGemini, parseGeminiResponse } from './geminiChat';
export { fetchUniqueOpeningLine } from './geminiFeatures';
export {
    summarizeInventory,
    summarizeLocations,
    summarizeQuests,
    summarizeCharacters,
    summarizeImportantEvents,
    summarizeStoryForContinuation
} from './geminiSummarization';

// New enhanced AI service exports
export {
    generateAIResponse,
    generateAIStreamResponse,
    switchAIProvider,
    getAvailableProviders,
    validateAIProviders,
    getCurrentAIProvider,
    updateAIProviderConfig,
    checkAIServiceHealth,
    convertToAIMessages,
    convertFromAIMessages
} from './aiService';

// Re-export types for convenience
export type { AIProviderType, AIMessage, GeminiResponseFormat } from './aiProviders/types';
