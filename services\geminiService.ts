
// This file now acts as a barrel, re-exporting from the modularized services.
// This ensures that other parts of the application that import from 'geminiService'
// do not need to change their import paths.

export { createChatSession, sendMessageToGemini, parseGeminiResponse } from './geminiChat'; // Added parseGeminiResponse if it's needed externally, or remove if not
export { fetchUniqueOpeningLine } from './geminiFeatures';
export { 
    summarizeInventory, 
    summarizeLocations, 
    summarizeQuests, 
    summarizeCharacters,
    summarizeImportantEvents, // New export
    summarizeStoryForContinuation // New export
} from './geminiSummarization';

// Note:
// - The 'ai' instance is initialized in 'geminiClient.ts' and imported by other service modules where needed.
// - Prompt construction logic, including hardcoded elements and getBaseSystemInstruction,
//   is now primarily in 'geminiPrompts.ts' and used internally by 'geminiChat.ts'.
// - If any other functions were previously exported and are still needed publicly, they should be added here.
