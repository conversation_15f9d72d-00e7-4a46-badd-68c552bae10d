// utils/regexUtils.ts

import { RegexRule, RegexRuleScope } from '../types';

/**
 * Applies a list of regex rules to a given text.
 * Rules are applied in the order they appear in the `rules` array.
 * Only active rules matching one of the `targetScopes` are applied.
 *
 * @param text The input string to transform.
 * @param rules An array of RegexRule objects.
 * @param targetScopes An array of RegexRuleScope to filter which rules are applied.
 * @returns The transformed string.
 */
export function applyRegexRules(
  text: string,
  rules: RegexRule[],
  targetScopes: RegexRuleScope[] // This parameter is effectively handled by the caller by pre-filtering rules.
                                 // It's kept here for potential direct use or if the function evolves.
): string {
  if (!rules || rules.length === 0 || !text) {
    return text;
  }

  let processedText = text;

  for (const rule of rules) {
    // The filtering by isActive and scope is expected to be done by the caller (useGameSession)
    // before passing the rules array to this function.
    // However, as a safeguard or for direct use, we can keep it.
    if (rule.isActive && targetScopes.includes(rule.scope)) {
      try {
        // Ensure flags are valid and don't conflict (e.g. if user enters 'gg' make it 'g')
        const uniqueFlags = Array.from(new Set(rule.flags.split(''))).join('');
        const regex = new RegExp(rule.pattern, uniqueFlags);
        
        // TODO: Implement trimInput logic here.
        // If rule.trimInput is defined and not empty:
        // 1. Split rule.trimInput by newline to get an array of strings to remove.
        // 2. For each match of `regex` in `processedText`:
        //    a. Get the matched substring.
        //    b. Iteratively remove all occurrences of the trimInput strings from this matched substring.
        //    c. Use this "trimmed match" in the `rule.replacement` (if {{match}} is used) or
        //       perform the replacement on the "trimmed match" if the replacement string is static.
        // This is complex because String.prototype.replace with a function for the replacement
        // would be needed to process each match individually.
        // For now, we proceed without `trimInput` logic.

        processedText = processedText.replace(regex, rule.replacement);
      } catch (error) {
        console.warn(`Invalid regex rule skipped: Name="${rule.name}", Pattern="${rule.pattern}", Flags="${rule.flags}". Error:`, error);
        // Optionally, notify the user about the invalid rule if a notification system is available here.
      }
    }
  }
  return processedText;
}