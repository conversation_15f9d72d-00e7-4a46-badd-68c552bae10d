# MemoryAble - Enhanced Local AI Storytelling Platform

A powerful, local-first interactive storytelling application with advanced SillyTavern compatibility, enhanced character management, and comprehensive regex processing capabilities.

## 🌟 Key Features

### 🏠 Local-First Architecture
- **Hybrid AI Operation**: Seamless switching between local and cloud AI
- **Local File Management**: Complete control over your data
- **Offline Capability**: Full functionality without internet connection
- **Privacy-Focused**: Your stories and characters stay on your device

### 🎭 Enhanced SillyTavern Integration
- **Advanced Character Cards**: Full v2.0+ format support with metadata
- **Character Relationships**: Automatic relationship extraction and management
- **Hierarchical World Books**: Parent-child relationships and dependencies
- **Batch Operations**: Import/export multiple characters and world books
- **PNG Character Cards**: Extract character data from PNG files

### 🔧 Advanced Regex System
- **Visual Pattern Builder**: User-friendly regex creation interface
- **Pattern Library**: Pre-built patterns for common use cases
- **Real-time Testing**: Live regex validation and testing
- **Rule Optimization**: Automatic performance optimization suggestions
- **Import/Export**: Share regex rule sets with the community

### 🎮 RPG-Style Progression
- **Character Development**: Attributes, skills, and progression system
- **Relationship Tracking**: Dynamic character relationship management
- **Quest System**: Goal tracking and achievement system
- **Status Effects**: Buffs, debuffs, and temporary effects

## 🚀 Quick Start

### Prerequisites
- **Node.js 18+**
- **Modern web browser** (Chrome 90+, Firefox 88+, Safari 14+)

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd memoryable
   npm install
   ```

2. **Configure API keys (optional for local-only use):**
   ```bash
   # Copy environment template
   cp .env.local.example .env.local

   # Edit .env.local and add your API keys
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

3. **Start the application:**
   ```bash
   # Start both local server and web app
   npm run dev:local

   # Or start separately:
   npm run local-server  # Terminal 1
   npm run dev          # Terminal 2
   ```

4. **Open your browser:**
   Navigate to `http://localhost:5173`

## 🔧 Configuration Options

### Local vs Cloud Operation

#### Full Local Mode (Recommended for Privacy)
- No API keys required
- All processing done locally
- Complete offline functionality
- Requires local AI setup (optional)

#### Hybrid Mode (Default)
- Local processing with cloud fallback
- Best of both worlds
- Automatic failover

#### Cloud Mode
- Traditional cloud-based operation
- Requires API keys
- Always-online functionality

### Local AI Setup (Optional)

For complete local operation, you can integrate with:
- **Ollama**: Local LLM hosting
- **LM Studio**: User-friendly local AI interface
- **Text Generation WebUI**: Advanced local AI setup

## 📁 Project Structure

```
memoryable/
├── src/                          # Main application source
│   ├── components/              # React components
│   ├── hooks/                   # Custom React hooks
│   ├── services/               # Business logic services
│   │   ├── localApiService.ts  # Local server communication
│   │   ├── sillyTavernService.ts # SillyTavern compatibility
│   │   ├── regexBuilderService.ts # Advanced regex features
│   │   └── localConfigService.ts # Configuration management
│   ├── utils/                  # Utility functions
│   └── types.ts               # TypeScript type definitions
├── local-server/              # Local backend server
│   ├── index.js              # Express server
│   └── data/                 # Local data storage
├── components/               # UI components
└── docs/                    # Documentation
```

## 🎯 Usage Guide

### Character Management

#### Importing SillyTavern Character Cards
1. Click the import button in the header
2. Select "Import SillyTavern Character Card"
3. Choose your .json or .png character files
4. Characters are automatically processed with relationships extracted

#### Creating Character Relationships
- Relationships are automatically detected from character descriptions
- Manually add relationships in the character management panel
- Track relationship changes over time

### World Book Management

#### Hierarchical Organization
- Create parent-child relationships between world book entries
- Set dependencies and conflicts between entries
- Use advanced search and filtering

#### SillyTavern Compatibility
- Import existing SillyTavern world books
- Export in compatible formats
- Maintain full compatibility with SillyTavern ecosystem

### Advanced Regex Features

#### Using the Pattern Library
1. Open Settings → Regex Replacement Editor
2. Browse the pattern library by category
3. Select patterns and customize replacements
4. Test patterns in real-time

#### Creating Custom Patterns
1. Use the visual pattern builder
2. Test with sample text
3. Save to your personal library
4. Export/import pattern collections

## 🔌 API Reference

### Local Server Endpoints

```
GET  /api/health                    # Server health check
POST /api/ai/generate              # Local AI text generation
POST /api/images/generate          # Local image generation
POST /api/files/save               # Save files locally
GET  /api/files/list/:type         # List files by type
POST /api/backup/create            # Create local backup
GET  /api/backup/list              # List available backups
GET  /api/backup/restore/:id       # Restore from backup
```

### Service APIs

#### SillyTavern Service
```typescript
// Process character card
const result = await sillyTavernService.processCharacterCard(cardData, fileName);

// Export character with relationships
const blob = await sillyTavernService.exportCharacterCard(
  characterData,
  worldBookElements,
  relationships
);

// Batch import
const results = await sillyTavernService.importMultipleCharacterCards(files);
```

#### Regex Builder Service
```typescript
// Test regex pattern
const result = regexBuilderService.testRegex(pattern, flags, testText, replacement);

// Get pattern library
const patterns = regexBuilderService.getPatternLibrary();

// Create rule from pattern
const rule = regexBuilderService.createRuleFromPattern(pattern, replacement, scope);
```

## 🛠️ Development

### Building from Source
```bash
# Development build
npm run dev

# Production build
npm run build

# Preview production build
npm run preview
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Testing
```bash
# Run unit tests
npm test

# Run integration tests
npm run test:integration

# Run performance tests
npm run test:performance
```

## 📊 Performance

### Benchmarks
- **Character Card Import**: < 100ms per card
- **World Book Processing**: < 500ms for 1000+ entries
- **Regex Processing**: < 10ms per rule application
- **Local Server Response**: < 50ms average

### Memory Usage
- **Base Application**: < 100MB
- **With Large Dataset**: < 500MB
- **Local Server**: < 50MB

## 🔒 Privacy & Security

### Data Protection
- All data stored locally by default
- Optional encryption for sensitive data
- No telemetry or tracking
- Complete user control over data

### Security Features
- CORS protection for local server
- Trusted domain configuration
- Secure file handling
- Input validation and sanitization

## 🆘 Troubleshooting

### Common Issues

#### Local Server Won't Start
```bash
# Check if port is in use
lsof -i :3001

# Try different port
LOCAL_SERVER_PORT=3002 npm run local-server
```

#### Character Cards Won't Import
- Verify file format (JSON/PNG)
- Check file size limits
- Ensure valid SillyTavern format

#### Performance Issues
- Reduce cache size in settings
- Disable advanced features if needed
- Check available memory

### Getting Help
- Check the [troubleshooting guide](docs/troubleshooting.md)
- Review [common issues](docs/common-issues.md)
- Submit issues on GitHub

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **SillyTavern Community**: For the excellent character card format standards
- **Google Gemini**: For AI capabilities
- **Pollinations AI**: For image generation services
- **Open Source Community**: For the amazing tools and libraries

---

**Made with ❤️ for the AI storytelling community**
