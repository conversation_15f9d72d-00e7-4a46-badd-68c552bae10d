
// src/rpgSystem/components/RPGStatusPanel.tsx

import React, { useState, forwardRef } from 'react';
import { PlayerStatus_RPG, Quest_RPG, CoreAttributes_RPG, Skill_RPG, StatusEffect_RPG } from '../types_rpg';
import { UIText, Icons } from '@/constants'; 
import { DetailModalType } from '@/types'; 

interface RPGStatusPanelProps {
  statusRPG: PlayerStatus_RPG;
  playerName: string; 
  className?: string;
  onSummarizeInventoryAndShowDetails: () => void;
  onSummarizeLocationsAndShowDetails: () => void;
  onSummarizeQuestsAndShowDetails: () => void;
  onSummarizeCharactersAndShowDetails: () => void;
  onSummarizeImportantEventsAndShowDetails: () => void;
  isSummarizing: { 
    inventory: boolean;
    locations: boolean;
    quests: boolean;
    characters: boolean;
    importantEvents: boolean;
  };
  enableBackdropBlur?: boolean;
  onClosePanel?: () => void;
  onAllocateAttribute: (attributeKey: keyof CoreAttributes_RPG) => void;
  onAllocateSkill: (skillId: string) => void;
  currentMood?: string; 
  currentTimeOfDay?: string; 
  currentWeather?: string; 
}

interface GaugeBarPropsRPG {
  current: number;
  max: number;
  label: string;
  gaugeType?: 'xp' | 'health' | 'default';
  colorVar?: string;
  showValue?: boolean;
}

const GaugeBarRPG: React.FC<GaugeBarPropsRPG> = ({
  current, max, label, gaugeType = 'default', colorVar = 'var(--accent-color)', showValue = true
}) => {
  const percentage = max > 0 ? (current / max) * 100 : 0;
  const isRpgGauge = gaugeType === 'xp' || gaugeType === 'health';

  return (
    <div className={`mb-1.5 ${isRpgGauge ? 'rpg-gauge-bar-rpg' : ''}`}>
      <div className="flex justify-between text-xs mb-0.5">
        <span className={`font-medium ${isRpgGauge ? 'text-[var(--rpg-text-secondary)]' : 'text-secondary-themed'}`}>{label}</span>
        {showValue && <span className={`font-mono ${isRpgGauge ? 'text-[var(--rpg-text-accent)]' : 'text-accent-themed'}`}>{current} / {max}</span>}
      </div>
      <div className={`h-2.5 w-full rounded-full overflow-hidden ${isRpgGauge ? 'rpg-gauge-track' : 'bg-element-themed/50 border border-themed'}`}>
        <div
          className={`h-full rounded-full transition-all duration-300 ease-out 
                     ${isRpgGauge ? (gaugeType === 'xp' ? 'rpg-gauge-fill rpg-gauge-fill-xp' : 'rpg-gauge-fill rpg-gauge-fill-health') : ''}`}
          style={{ width: `${percentage}%`, backgroundColor: isRpgGauge ? undefined : colorVar }}
        />
      </div>
    </div>
  );
};

const AttributeRowRPG: React.FC<{
    label: string, value: number, onAllocate: () => void, canAllocate: boolean, attrKey: keyof CoreAttributes_RPG
}> = ({ label, value, onAllocate, canAllocate, attrKey }) => (
    <div className="rpg-attribute-slot flex justify-between items-center">
        <span className="text-primary-themed">{label}: <span className="font-semibold text-accent-themed">{value}</span></span>
        {canAllocate && (
            <button onClick={onAllocate} className="btn-dreamy btn-dreamy-xs" title={`提升${label}`} aria-label={`提升${label}`}>
                <Icons.PixelPlus className="w-3 h-3" />
            </button>
        )}
    </div>
);

const SkillRowRPG: React.FC<{
    skill: Skill_RPG, onAllocate: () => void, canAllocate: boolean
}> = ({ skill, onAllocate, canAllocate }) => (
    <div className="rpg-skill-slot">
        <div className="flex justify-between items-center mb-0.5">
            <span className="text-primary-themed text-xs">{skill.name} <span className="font-semibold text-accent-themed">Lv.{skill.level}</span></span>
            {canAllocate && (
                <button onClick={onAllocate} className="btn-dreamy btn-dreamy-xs" title={`提升${skill.name}`} aria-label={`提升${skill.name}`}>
                    <Icons.PixelPlus className="w-3 h-3" />
                </button>
            )}
        </div>
        <p className="text-secondary-themed text-[0.6rem] leading-tight mb-0.5">{skill.description}</p>
        {skill.xpToNextLevel !== undefined && skill.currentXp !== undefined && skill.level < 10 && ( 
             <GaugeBarRPG current={skill.currentXp} max={skill.xpToNextLevel} label="经验" gaugeType="xp" showValue={false} />
        )}
    </div>
);

const EffectDisplayRPG: React.FC<{ effect: StatusEffect_RPG }> = ({ effect }) => {
  const durationText = effect.durationTurns === -1 
    ? UIText.effectDurationPermanent 
    : UIText.effectDurationTurns(effect.remainingTurns);
  
  const EffectIcon = effect.icon === "💪" ? () => <span role="img" aria-label="strength">💪</span> : 
                     effect.icon === "☠️" ? () => <span role="img" aria-label="poison">☠️</span> : 
                     effect.isBuff ? Icons.ShieldCheck : Icons.ShieldExclamation;

  return (
    <div 
      className={`rpg-attribute-slot flex items-center justify-between text-xs p-1.5 ${effect.isBuff ? 'border-[var(--rpg-gauge-health-fill)]' : 'border-[var(--rpg-text-accent)]'}`}
      title={effect.description || effect.name}
    >
      <div className="flex items-center">
        <EffectIcon className={`w-3.5 h-3.5 mr-1.5 ${effect.isBuff ? 'text-green-500' : 'text-red-500'}`} />
        <span className={`${effect.isBuff ? 'text-[var(--rpg-text-primary)]' : 'text-[var(--rpg-text-secondary)]'}`}>{effect.name}</span>
      </div>
      <span className="text-[var(--rpg-text-secondary)] text-[0.65rem]">{durationText}</span>
    </div>
  );
};


export const RPGStatusPanel = forwardRef<HTMLDivElement, RPGStatusPanelProps>(({
  statusRPG, playerName, className = '',
  onSummarizeInventoryAndShowDetails, onSummarizeLocationsAndShowDetails,
  onSummarizeQuestsAndShowDetails, onSummarizeCharactersAndShowDetails,
  onSummarizeImportantEventsAndShowDetails, isSummarizing,
  enableBackdropBlur = true, onClosePanel,
  onAllocateAttribute, onAllocateSkill,
  currentMood, currentTimeOfDay, currentWeather 
}, ref) => {

  const {
    healthEnergy, currentDay,
    level, xp, xpToNextLevel, attributePoints, skillPoints,
    coreAttributes, skills, quests, buffs, debuffs
  } = statusRPG;

  const [showGrowthSection, setShowGrowthSection] = useState(false);
  const activeQuest = quests?.find(q => q.status === 'active');

  const displayMood = currentMood ? (UIText.moods[currentMood as keyof typeof UIText.moods] || currentMood) : UIText.mockMood;
  const displayWeather = currentWeather ? (UIText.weatherTypes[currentWeather as keyof typeof UIText.weatherTypes] || currentWeather) : UIText.mockWeather;
  const displayTimeOfDay = currentTimeOfDay ? (UIText.timeOfDayNames[currentTimeOfDay as keyof typeof UIText.timeOfDayNames] || currentTimeOfDay) : UIText.mockTimeOfDay;


  const statusItems = [
    { icon: Icons.CalendarDays, label: UIText.currentDayLabel.replace('{day}', String(currentDay)), isFullWidth: false },
    { icon: Icons.Clock, label: displayTimeOfDay, isFullWidth: false },
    { icon: Icons.Sun, label: displayWeather, isFullWidth: false },
    { icon: Icons.FaceNeutral, label: displayMood, isFullWidth: false },
    { icon: Icons.Heart, label: `${UIText.healthEnergyLabel}: ${healthEnergy.current}/${healthEnergy.max}`, isFullWidth: true, isGauge: true, gaugeType: 'health' as 'health', current: healthEnergy.current, max: healthEnergy.max },
    { icon: Icons.Sparkles, label: `${UIText.xpLabel}: ${xp}/${xpToNextLevel}`, isFullWidth: true, isGauge: true, gaugeType: 'xp' as 'xp', current: xp, max: xpToNextLevel },
  ];

  const detailButtons = [
    { label: UIText.inventory, icon: Icons.Bag, onClick: onSummarizeInventoryAndShowDetails, loading: isSummarizing.inventory, modalKey: 'inventory' as DetailModalType },
    { label: UIText.map, icon: Icons.Map, onClick: onSummarizeLocationsAndShowDetails, loading: isSummarizing.locations, modalKey: 'locations' as DetailModalType },
    { label: UIText.quests, icon: Icons.Quest, onClick: onSummarizeQuestsAndShowDetails, loading: isSummarizing.quests, modalKey: 'quests' as DetailModalType },
    { label: UIText.profile, icon: Icons.Profile, onClick: onSummarizeCharactersAndShowDetails, loading: isSummarizing.characters, modalKey: 'characters' as DetailModalType },
    { label: UIText.importantEventsButton, icon: Icons.ClipboardList, onClick: onSummarizeImportantEventsAndShowDetails, loading: isSummarizing.importantEvents, modalKey: 'importantEvents' as DetailModalType },
  ];

  const panelBaseClasses = "rpg-status-panel p-3 overflow-y-auto flex flex-col relative rounded-l-xl";
  const blurClass = enableBackdropBlur ? "backdrop-blur-md" : "";
  
  const hasSpendablePoints = (attributePoints || 0) > 0 || (skillPoints || 0) > 0;

  return (
    <div ref={ref} className={`${panelBaseClasses} ${blurClass} ${className}`} role="complementary" aria-labelledby="rpg-status-panel-title">
       {onClosePanel && (
        <button
          onClick={onClosePanel}
          className="absolute top-2 right-2 p-1.5 text-[var(--rpg-icon-color)] hover:text-[var(--rpg-text-accent)] rounded-full hover:bg-[var(--rpg-header-bg)] z-10"
          aria-label="关闭状态面板"
        >
          <Icons.Close className="w-5 h-5" />
        </button>
      )}
      <div className="rpg-header mb-2 flex items-center">
        <Icons.PixelLevelBadge className="w-5 h-5 mr-2 text-[var(--rpg-header-text)]" />
        <h3 id="rpg-status-panel-title" className="text-sm font-semibold text-[var(--rpg-header-text)] flex-grow">{playerName || UIText.player("冒险者")} - {UIText.levelLabel}{level}</h3>
      </div>

      <div className="space-y-1 mb-2 rpg-info-block">
        {statusItems.map(item => (
          item.isGauge ? (
            <GaugeBarRPG key={item.label} current={item.current!} max={item.max!} label={item.label.split(':')[0]} gaugeType={item.gaugeType} />
          ) : (
            <div key={item.label} className={`rpg-info-item ${item.isFullWidth ? 'w-full' : ''}`}>
              <item.icon className="w-3.5 h-3.5 rpg-info-icon" />
              <span className="rpg-info-label">{item.label}</span>
            </div>
          )
        ))}
      </div>
      
      <hr className="my-2 themed-divider" /> {/* Use themed-divider for consistency */}

      <div className="rpg-quest-section rpg-info-block">
        <div className="rpg-info-item">
          <Icons.Quest className="w-3.5 h-3.5 rpg-info-icon" />
          <span className="rpg-info-label">{UIText.currentQuestLabel}:</span>
        </div>
        <p className="text-xs text-primary-themed ml-5 truncate" title={activeQuest?.title || UIText.noActiveQuests}>
          {activeQuest?.title || UIText.noActiveQuests}
        </p>
        {activeQuest && activeQuest.objectives[0] && (
          <p className="text-xs text-secondary-themed ml-5 truncate" title={activeQuest.objectives[0]}>
           - {activeQuest.objectives[0]}
          </p>
        )}
      </div>

      <hr className="my-2 themed-divider" />
       <div className="grid grid-cols-3 gap-1.5 mb-2">
        {detailButtons.map(btn => (
          <button
            key={btn.modalKey}
            onClick={btn.onClick}
            disabled={btn.loading}
            className="rpg-action-button-rpg text-xs flex flex-col items-center justify-center p-1.5"
            title={btn.label}
            aria-label={btn.label}
          >
            <btn.icon className={`w-4 h-4 mb-0.5 ${btn.loading ? 'animate-spin' : ''}`} />
            <span className="truncate text-[0.6rem] leading-tight">{btn.label}</span>
          </button>
        ))}
      </div>
      <hr className="my-2 themed-divider" />
      
      <div className="rpg-info-block mb-1.5">
        <div className="rpg-header text-xs mb-1 flex items-center !py-1 !px-1.5">
            <Icons.ShieldCheck className="w-3.5 h-3.5 mr-1.5 text-green-400" />
            {UIText.buffsSectionTitle}
        </div>
        {buffs && buffs.length > 0 ? (
            <div className="space-y-1">
                {buffs.map(buff => <EffectDisplayRPG key={buff.id} effect={buff} />)}
            </div>
        ) : <p className="text-xs text-secondary-themed italic px-1.5">{UIText.noActiveBuffs}</p>}
      </div>

      <div className="rpg-info-block mb-2">
        <div className="rpg-header text-xs mb-1 flex items-center !py-1 !px-1.5">
            <Icons.ShieldExclamation className="w-3.5 h-3.5 mr-1.5 text-red-400" />
            {UIText.debuffsSectionTitle}
        </div>
        {debuffs && debuffs.length > 0 ? (
            <div className="space-y-1">
                {debuffs.map(debuff => <EffectDisplayRPG key={debuff.id} effect={debuff} />)}
            </div>
        ) : <p className="text-xs text-secondary-themed italic px-1.5">{UIText.noActiveDebuffs}</p>}
      </div>
      
      <hr className="my-2 themed-divider" />

      <button
        onClick={() => setShowGrowthSection(!showGrowthSection)}
        className={`rpg-growth-toggle flex justify-between items-center w-full text-left text-xs font-semibold mb-1.5 ${hasSpendablePoints ? 'rpg-button-pulsing-glow' : ''}`}
        aria-expanded={showGrowthSection}
      >
        <span className="flex items-center">
          <Icons.PixelGrowth className="w-4 h-4 mr-1.5" /> {UIText.characterGrowthTitle}
        </span>
        <div className="flex items-center">
          {statusRPG.attributePoints > 0 && <span className="flex items-center mr-2"><Icons.PixelAttributePoint className="w-3 h-3 mr-0.5"/>{statusRPG.attributePoints}</span>}
          {statusRPG.skillPoints > 0 && <span className="flex items-center"><Icons.PixelSkillPoint className="w-3 h-3 mr-0.5"/>{statusRPG.skillPoints}</span>}
          <Icons.ChevronDown className={`w-4 h-4 ml-1 transition-transform ${showGrowthSection ? 'rotate-180' : ''}`} />
        </div>
      </button>

      {showGrowthSection && (
        <div className="space-y-2 animate-fadeIn rpg-info-block">
          {attributePoints > 0 && <p className="text-xs text-accent-themed">{UIText.attributePointsSpendPrompt(attributePoints)}</p>}
          <div className="grid grid-cols-2 gap-1">
            {(Object.keys(coreAttributes) as Array<keyof CoreAttributes_RPG>).map(attrKey => {
              const dynamicKey = (attrKey + 'Short') as keyof typeof UIText;
              const uiTextValue = UIText[dynamicKey];
              const finalLabel = typeof uiTextValue === 'string' ? uiTextValue : attrKey.toString();
              return (
                <AttributeRowRPG
                  key={attrKey}
                  label={finalLabel}
                  value={coreAttributes[attrKey]}
                  onAllocate={() => onAllocateAttribute(attrKey)}
                  canAllocate={attributePoints > 0}
                  attrKey={attrKey}
                />
              );
            })}
          </div>
          {skillPoints > 0 && <p className="text-xs text-accent-themed mt-1.5">{UIText.skillPointsSpendPrompt(skillPoints)}</p>}
          {skills && skills.length > 0 ? (
            <div className="space-y-1">
              {skills.sort((a,b) => b.level - a.level || a.name.localeCompare(b.name)).map(skill => (
                <SkillRowRPG
                  key={skill.id}
                  skill={skill}
                  onAllocate={() => onAllocateSkill(skill.id)}
                  canAllocate={skillPoints > 0}
                />
              ))}
            </div>
          ) : <p className="text-xs text-secondary-themed italic">{UIText.noSkillsLearned}</p>}
        </div>
      )}
    </div>
  );
});

// RPGStatusPanel.displayName = 'RPGStatusPanel'; // Add display name
// export default RPGStatusPanel; // Keep default export if this is the primary export
