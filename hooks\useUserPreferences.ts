
import { useState, useEffect, useCallback } from 'react';
import { UserPreferences, LocalStorageKeys, Achievement } from '../types';
import { AVAILABLE_ACHIEVEMENTS, UIText } from '../constants';
import { NotificationContextType } from '../types'; // For addNotification type

const defaultUserPreferences: UserPreferences = {
  fontSizeScale: 1.0,
  unlockedAchievements: {},
};

export const useUserPreferences = (addNotification?: NotificationContextType['addNotification']) => {
  const [userPreferences, setUserPreferencesState] = useState<UserPreferences>(() => {
    const saved = localStorage.getItem(LocalStorageKeys.USER_PREFERENCES);
    if (saved) {
      try {
        const parsed = JSON.parse(saved) as Partial<UserPreferences>;
        return {
          ...defaultUserPreferences,
          ...parsed,
          fontSizeScale: typeof parsed.fontSizeScale === 'number'
            ? Math.max(0.8, Math.min(1.5, parsed.fontSizeScale))
            : defaultUserPreferences.fontSizeScale,
          unlockedAchievements: parsed.unlockedAchievements || {},
        };
      } catch (e) {
        console.error("Failed to parse user preferences, using defaults:", e);
      }
    }
    return { ...defaultUserPreferences };
  });

  const persistUserPreferences = useCallback(() => {
    localStorage.setItem(LocalStorageKeys.USER_PREFERENCES, JSON.stringify(userPreferences));
  }, [userPreferences]);

  useEffect(() => {
    persistUserPreferences();
    document.documentElement.style.fontSize = `${userPreferences.fontSizeScale * 16}px`;
  }, [userPreferences, persistUserPreferences]);

  const updateUserPreferences = useCallback((newPreferences: Partial<UserPreferences> | ((prevState: UserPreferences) => UserPreferences)) => {
    setUserPreferencesState(prev => 
        typeof newPreferences === 'function' ? newPreferences(prev) : {...prev, ...newPreferences}
    );
  }, []);

  const unlockAchievement = useCallback((achievementId: string) => {
    if (!userPreferences.unlockedAchievements[achievementId]) {
      const achievement = AVAILABLE_ACHIEVEMENTS.find(a => a.id === achievementId);
      if (achievement && addNotification) {
        setUserPreferencesState(prev => ({
          ...prev,
          unlockedAchievements: {
            ...prev.unlockedAchievements,
            [achievementId]: Date.now()
          }
        }));
        addNotification(achievement.name, 'achievement', 7000, UIText.achievementUnlockedTitle);
      } else if (achievement && !addNotification) {
         setUserPreferencesState(prev => ({
          ...prev,
          unlockedAchievements: {
            ...prev.unlockedAchievements,
            [achievementId]: Date.now()
          }
        }));
        console.warn("Achievement unlocked but no notification function provided:", achievement.name);
      }
    }
  }, [userPreferences.unlockedAchievements, addNotification]);

  return { userPreferences, setUserPreferences: updateUserPreferences, persistUserPreferences, unlockAchievement, defaultUserPreferences };
};
