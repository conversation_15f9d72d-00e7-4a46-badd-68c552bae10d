import {
  BaseAIProvider,
  AIProviderConfig,
  AIProviderType,
  AIModelInfo,
  AIMessage,
  AIResponse,
  AIStreamChunk,
  AIGenerationConfig,
  AIProviderError,
  AIRateLimitError,
  AIQuotaExceededError,
  GeminiResponseFormat,
  convertToGeminiFormat
} from './types';

import { OpenAIProvider } from './openaiProvider';
import { GeminiProvider } from './geminiProvider';
import { AnthropicProvider } from './anthropicProvider';

export interface AIServiceConfig {
  primaryProvider: AIProviderType;
  fallbackProviders?: AIProviderType[];
  providers: Record<AIProviderType, AIProviderConfig>;
  retryAttempts?: number;
  retryDelay?: number;
  enableAutoFallback?: boolean;
}

export class AIServiceManager {
  private providers: Map<AIProviderType, BaseAIProvider> = new Map();
  private config: AIServiceConfig;
  private currentProvider: AIProviderType;

  constructor(config: AIServiceConfig) {
    this.config = {
      retryAttempts: 3,
      retryDelay: 1000,
      enableAutoFallback: true,
      ...config
    };
    this.currentProvider = config.primaryProvider;
    this.initializeProviders();
  }

  private initializeProviders(): void {
    for (const [providerType, providerConfig] of Object.entries(this.config.providers)) {
      try {
        const provider = this.createProvider(providerType as AIProviderType, providerConfig);
        this.providers.set(providerType as AIProviderType, provider);
      } catch (error) {
        console.warn(`Failed to initialize ${providerType} provider:`, error);
      }
    }
  }

  private createProvider(type: AIProviderType, config: AIProviderConfig): BaseAIProvider {
    // Handle custom providers (they use OpenAI-compatible API)
    if (typeof type === 'string' && type.startsWith('custom_')) {
      return new OpenAIProvider(config);
    }

    switch (type) {
      case 'openai':
      case 'azure-openai':
        return new OpenAIProvider(config);
      case 'gemini':
        return new GeminiProvider(config);
      case 'anthropic':
        return new AnthropicProvider(config);
      default:
        throw new AIProviderError(`Unsupported provider type: ${type}`, type, 'UNSUPPORTED_PROVIDER');
    }
  }

  // Get current active provider
  getCurrentProvider(): BaseAIProvider | null {
    return this.providers.get(this.currentProvider) || null;
  }

  // Switch to a different provider
  switchProvider(providerType: AIProviderType): boolean {
    if (this.providers.has(providerType)) {
      this.currentProvider = providerType;
      return true;
    }
    return false;
  }

  // Get all available models from all providers
  async getAllAvailableModels(): Promise<Record<AIProviderType, AIModelInfo[]>> {
    const models: Record<string, AIModelInfo[]> = {};
    
    for (const [providerType, provider] of this.providers.entries()) {
      try {
        models[providerType] = await provider.getAvailableModels();
      } catch (error) {
        console.warn(`Failed to get models for ${providerType}:`, error);
        models[providerType] = [];
      }
    }
    
    return models as Record<AIProviderType, AIModelInfo[]>;
  }

  // Get models for current provider
  async getCurrentProviderModels(): Promise<AIModelInfo[]> {
    const provider = this.getCurrentProvider();
    if (!provider) {
      throw new AIProviderError('No active provider', this.currentProvider, 'NO_PROVIDER');
    }
    return await provider.getAvailableModels();
  }

  // Validate all configured providers
  async validateAllProviders(): Promise<Record<AIProviderType, boolean>> {
    const results: Record<string, boolean> = {};

    for (const [providerType, provider] of this.providers.entries()) {
      try {
        results[providerType] = await provider.validateConfig();
      } catch (error) {
        console.warn(`Validation failed for ${providerType}:`, error);
        results[providerType] = false;
      }
    }

    return results as Record<AIProviderType, boolean>;
  }

  // Get provider status information
  getProviderStatus(): Record<AIProviderType, { available: boolean; current: boolean }> {
    const status: Record<string, { available: boolean; current: boolean }> = {};

    for (const [providerType] of this.providers.entries()) {
      status[providerType] = {
        available: this.providers.has(providerType),
        current: providerType === this.currentProvider
      };
    }

    return status as Record<AIProviderType, { available: boolean; current: boolean }>;
  }

  // Generate response with automatic fallback
  async generateResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): Promise<AIResponse> {
    const providers = this.getProviderFallbackOrder();
    let lastError: Error | null = null;

    for (const providerType of providers) {
      const provider = this.providers.get(providerType);
      if (!provider) continue;

      try {
        return await this.executeWithRetry(
          () => provider.generateResponse(messages, config),
          this.config.retryAttempts!
        );
      } catch (error) {
        lastError = error as Error;
        console.warn(`Provider ${providerType} failed:`, error);
        
        // Don't fallback for certain errors
        if (error instanceof AIProviderError && 
            ['INVALID_API_KEY', 'MISSING_API_KEY', 'BAD_REQUEST'].includes(error.code || '')) {
          continue;
        }
        
        if (!this.config.enableAutoFallback) {
          throw error;
        }
      }
    }

    throw lastError || new AIProviderError('All providers failed', this.currentProvider, 'ALL_PROVIDERS_FAILED');
  }

  // Generate stream response with automatic fallback
  async* generateStreamResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): AsyncGenerator<AIStreamChunk, void, unknown> {
    const providers = this.getProviderFallbackOrder();
    let lastError: Error | null = null;

    for (const providerType of providers) {
      const provider = this.providers.get(providerType);
      if (!provider) continue;

      try {
        yield* provider.generateStreamResponse(messages, config);
        return; // Success, exit
      } catch (error) {
        lastError = error as Error;
        console.warn(`Stream provider ${providerType} failed:`, error);
        
        // Don't fallback for certain errors
        if (error instanceof AIProviderError && 
            ['INVALID_API_KEY', 'MISSING_API_KEY', 'BAD_REQUEST'].includes(error.code || '')) {
          continue;
        }
        
        if (!this.config.enableAutoFallback) {
          throw error;
        }
      }
    }

    throw lastError || new AIProviderError('All stream providers failed', this.currentProvider, 'ALL_PROVIDERS_FAILED');
  }

  // Legacy compatibility method for Gemini format
  async generateGeminiResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): Promise<GeminiResponseFormat> {
    const response = await this.generateResponse(messages, config);
    return convertToGeminiFormat(response);
  }

  // Get provider fallback order
  private getProviderFallbackOrder(): AIProviderType[] {
    const order = [this.currentProvider];
    
    if (this.config.fallbackProviders) {
      for (const fallback of this.config.fallbackProviders) {
        if (fallback !== this.currentProvider) {
          order.push(fallback);
        }
      }
    }
    
    return order;
  }

  // Execute with retry logic
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxAttempts: number
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry for certain errors
        if (error instanceof AIProviderError && 
            ['INVALID_API_KEY', 'MISSING_API_KEY', 'BAD_REQUEST', 'QUOTA_EXCEEDED'].includes(error.code || '')) {
          throw error;
        }
        
        // Don't retry on last attempt
        if (attempt === maxAttempts) {
          throw error;
        }
        
        // Wait before retry (exponential backoff for rate limits)
        const delay = error instanceof AIRateLimitError 
          ? (error.retryAfter || 1) * 1000
          : this.config.retryDelay! * Math.pow(2, attempt - 1);
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }

  // Get provider status
  getProviderStatus(): Record<AIProviderType, { available: boolean; current: boolean }> {
    const status: Record<string, { available: boolean; current: boolean }> = {};
    
    for (const providerType of Object.keys(this.config.providers) as AIProviderType[]) {
      status[providerType] = {
        available: this.providers.has(providerType),
        current: providerType === this.currentProvider
      };
    }
    
    return status as Record<AIProviderType, { available: boolean; current: boolean }>;
  }

  // Update provider configuration
  updateProviderConfig(providerType: AIProviderType, config: AIProviderConfig): void {
    this.config.providers[providerType] = config;
    
    try {
      const provider = this.createProvider(providerType, config);
      this.providers.set(providerType, provider);
    } catch (error) {
      console.error(`Failed to update ${providerType} provider:`, error);
      this.providers.delete(providerType);
    }
  }

  // Remove provider
  removeProvider(providerType: AIProviderType): void {
    this.providers.delete(providerType);
    delete this.config.providers[providerType];
    
    if (this.currentProvider === providerType) {
      // Switch to first available provider
      const availableProviders = Array.from(this.providers.keys());
      if (availableProviders.length > 0) {
        this.currentProvider = availableProviders[0];
      }
    }
  }
}
