
import { GenerateContentResponse, GenerateContentParameters, Part } from "@google/genai";
import { UIText, GEMINI_MODEL_TEXT_FALLBACK, APP_TITLE_CN } from '../constants';
import { ai } from './geminiClient'; // Import the initialized 'ai' instance

const API_KEY = process.env.API_KEY; // Still needed for the initial check

export const fetchUniqueOpeningLine = async (
  style: 'comedy' | 'horror',
  usedLines: string[]
): Promise<string> => {
  if (!API_KEY) {
    console.warn("API Key missing, cannot fetch opening line from Gemini.");
    return UIText.dynamicOpeningLineFallback;
  }

  const styleDescription = style === 'comedy'
    ? "轻松幽默的日式轻喜剧风格"
    : "带有悬念和神秘感的日式恐怖风格";

  let prompt = `为一款名为《${APP_TITLE_CN}》的视觉小说游戏生成一句独特且引人入胜的开场白。
风格要求：${styleDescription}。
这句话将是玩家第一次启动游戏时看到的，用于快速设定基调。
请确保只返回一句话，并且是简体中文。
请务必避免以下已经使用过的句子：\n`;

  if (usedLines && usedLines.length > 0) {
    usedLines.forEach(line => {
      prompt += `- "${line}"\n`;
    });
  } else {
    prompt += "- (无)\n";
  }
  prompt += "\n请直接给出新的开场白，不要添加任何额外的前缀或标签。";

  try {
    const request: GenerateContentParameters = {
      model: GEMINI_MODEL_TEXT_FALLBACK,
      contents: [{ role: "user", parts: [{text: prompt} as Part] }],
      config: {
        temperature: style === 'comedy' ? 0.8 : 0.6,
        maxOutputTokens: 100,
      }
    };

    const response: GenerateContentResponse = await ai.models.generateContent(request);

    if (response.text && response.text.trim() !== "") {
      return response.text.trim();
    } else {
      console.warn("Gemini returned empty opening line, using fallback.");
      return UIText.dynamicOpeningLineFallback;
    }
  } catch (error: any) {
    console.error("Error fetching unique opening line from Gemini:", error.message);
    return UIText.dynamicOpeningLineFallback;
  }
};
