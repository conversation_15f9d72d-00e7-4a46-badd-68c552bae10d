// src/rpgSystem/hooks/useRPGSystem.ts

import { useCallback, useRef, useState } from 'react'; // Added useState
import {
    PlayerStatus_RPG, CoreAttributes_RPG, Skill_RPG, RPGNotificationDetails, RPGProcessingResult,
    InventoryItem_RPG, VisitedLocation_RPG, Quest_RPG, CharacterProfile_RPG, ImportantEvent_RPG // Added these
} from '../types_rpg';
import {
    defaultCoreAttributes_RPG, defaultSkills_RPG
} from '../constants_rpg';
import { processStoryUpdateTagsRPG, getAttributeDisplayNameRPG } from '../utils/rpgUtils';
import { UIText, AVAILABLE_ACHIEVEMENTS, GEMINI_MODEL_TEXT_FALLBACK } from '@/constants';
import { DetailModalType, DialogueLine } from '@/types';
import { summarizeInventory, summarizeLocations, summarizeQuests, summarizeCharacters, summarizeImportantEvents } from '@/services/geminiService';


export const useRPGSystem = (
    onNotifications: (notifications: RPGNotificationDetails[]) => void,
    onAchievementsUnlock: (achievementIds: string[]) => void,
    setActiveDetailModalFn?: (modal: DetailModalType | null) => void,
) => {
    const notificationTimestampsRefRPG = useRef<Record<string, number>>({});
    const [isSummarizingRPG, setIsSummarizingRPG] = useState({ inventory: false, locations: false, quests: false, characters: false, importantEvents: false });


    const allocateAttributePointRPG = useCallback((
        currentStatusRPG: PlayerStatus_RPG, 
        attributeKeyToAllocate: keyof CoreAttributes_RPG
    ): RPGProcessingResult => {
        let notifications: RPGNotificationDetails[] = [];
        let achievementsToUnlock: string[] = [];
        let statusChangesDelta: Partial<PlayerStatus_RPG> = {};

        if ((currentStatusRPG.attributePoints || 0) <= 0) {
            notifications.push({ message: UIText.cannotAllocateAttribute, type: 'warning', displayToUser: true });
        } else {
            const currentAttributeValue = currentStatusRPG.coreAttributes?.[attributeKeyToAllocate] ?? defaultCoreAttributes_RPG[attributeKeyToAllocate];
            const newAttributes = {
                ...(currentStatusRPG.coreAttributes || defaultCoreAttributes_RPG),
                [attributeKeyToAllocate]: currentAttributeValue + 1
            };
            notifications.push({ message: UIText.attributeIncreasedNotification(getAttributeDisplayNameRPG(attributeKeyToAllocate), newAttributes[attributeKeyToAllocate]), type: 'success', duration: 4000, title: "属性提升！", displayToUser: true });
            achievementsToUnlock.push('attribute_enhancer');
            if (attributeKeyToAllocate === 'strength' && newAttributes.strength >= 10 && (currentStatusRPG.coreAttributes?.strength || 0) < 10) achievementsToUnlock.push('strength_manifested');
            if (attributeKeyToAllocate === 'agility' && newAttributes.agility >= 10 && (currentStatusRPG.coreAttributes?.agility || 0) < 10) achievementsToUnlock.push('agile_as_wind');
            if (attributeKeyToAllocate === 'intelligence' && newAttributes.intelligence >= 10 && (currentStatusRPG.coreAttributes?.intelligence || 0) < 10) achievementsToUnlock.push('light_of_intellect');
            if (attributeKeyToAllocate === 'charisma' && newAttributes.charisma >= 10 && (currentStatusRPG.coreAttributes?.charisma || 0) < 10) achievementsToUnlock.push('charm_of_words');
            
            statusChangesDelta = { 
                coreAttributes: newAttributes, 
                attributePoints: (currentStatusRPG.attributePoints || 0) - 1 
            };
        }
        // Callbacks for notifications and achievements
        if (notifications.length > 0) onNotifications(notifications);
        if (achievementsToUnlock.length > 0) onAchievementsUnlock(achievementsToUnlock);
        return { statusChangesDelta, notifications, achievementsToUnlock };
    }, [onNotifications, onAchievementsUnlock]);

    const allocateSkillPointRPG = useCallback((
        currentStatusRPG: PlayerStatus_RPG,
        skillIdToAllocate: string
    ): RPGProcessingResult => {
        let notifications: RPGNotificationDetails[] = [];
        let achievementsToUnlock: string[] = [];
        let statusChangesDelta: Partial<PlayerStatus_RPG> = {};

        if ((currentStatusRPG.skillPoints || 0) <= 0) {
            notifications.push({ message: UIText.cannotAllocateSkill, type: 'warning', displayToUser: true });
        } else {
            const existingSkillIndex = (currentStatusRPG.skills || []).findIndex(s => s.id === skillIdToAllocate);
            let newSkillsArray = [...(currentStatusRPG.skills || []).map(s => ({ ...s }))];
            let skillNameForNotification = "";
            let newLevelForNotification = 0;

            if (existingSkillIndex > -1) {
                const oldSkill = newSkillsArray[existingSkillIndex];
                newLevelForNotification = oldSkill.level + 1;
                newSkillsArray[existingSkillIndex] = { ...oldSkill, level: newLevelForNotification, currentXp: 0, xpToNextLevel: Math.floor((oldSkill.xpToNextLevel || 50) * 1.3) };
                skillNameForNotification = newSkillsArray[existingSkillIndex].name;
                notifications.push({ message: UIText.skillLeveledUp(skillNameForNotification, newLevelForNotification), type: 'success', duration: 4000, title: "技能提升！", displayToUser: true });
            } else {
                const baseSkillDefinition = defaultSkills_RPG.find(s => s.id === skillIdToAllocate);
                if (baseSkillDefinition) {
                    newLevelForNotification = 1;
                    newSkillsArray.push({ ...baseSkillDefinition, level: newLevelForNotification, currentXp: 0, xpToNextLevel: baseSkillDefinition.xpToNextLevel || 50 });
                    skillNameForNotification = baseSkillDefinition.name;
                    notifications.push({ message: UIText.skillLearned(skillNameForNotification), type: 'success', duration: 4000, title: "习得技能！", displayToUser: true });
                } else {
                    notifications.push({ message: `错误：尝试分配点数给未知技能 ID "${skillIdToAllocate}"。`, type: 'error', displayToUser: true });
                    if (notifications.length > 0) onNotifications(notifications);
                    return { statusChangesDelta: {}, notifications, achievementsToUnlock }; 
                }
            }
            achievementsToUnlock.push('skill_master_initiate');
            if (newSkillsArray.filter(s => s.level >= 5).length >= 3) achievementsToUnlock.push('three_arts_mastery');
            
            statusChangesDelta = { 
                skills: newSkillsArray, 
                skillPoints: (currentStatusRPG.skillPoints || 0) - 1 
            };
        }
        if (notifications.length > 0) onNotifications(notifications);
        if (achievementsToUnlock.length > 0) onAchievementsUnlock(achievementsToUnlock);
        return { statusChangesDelta, notifications, achievementsToUnlock };
    }, [onNotifications, onAchievementsUnlock]);

    const processStoryUpdateRPG = useCallback((
        currentStatusRPG: PlayerStatus_RPG, 
        storyUpdateText: string | undefined,
        newSessionTimeOfDay?: string, 
        oldSessionTimeOfDay?: string  
    ): RPGProcessingResult => {
        const rpgProcessingResult = processStoryUpdateTagsRPG(
            currentStatusRPG,
            storyUpdateText || "",
            newSessionTimeOfDay,
            oldSessionTimeOfDay
        );
        
        if (rpgProcessingResult.notifications.length > 0) onNotifications(rpgProcessingResult.notifications);
        if (rpgProcessingResult.achievementsToUnlock.length > 0) onAchievementsUnlock(rpgProcessingResult.achievementsToUnlock);
        
        return rpgProcessingResult;
    }, [onNotifications, onAchievementsUnlock]);
    
    const showRateLimitedNotificationRPG = useCallback((
        messageKey: string,
        notification: RPGNotificationDetails
    ): boolean => {
        const now = Date.now();
        const lastShown = notificationTimestampsRefRPG.current[messageKey] || 0;
        const cooldown = 60 * 1000; 

        if (now - lastShown > cooldown) {
            if (notification.displayToUser) {
                onNotifications([notification]);
            }
            notificationTimestampsRefRPG.current[messageKey] = now;
            return true;
        }
        return false;
    }, [onNotifications]);

    const handleSummarizeAndShowDetailsRPG = useCallback(async <DataType, >(
        dialogueLog: DialogueLine[], summaryModelId: string, summaryType: DetailModalType,
        currentData: DataType[], serviceFn: (log: DialogueLine[], current: DataType[], modelId: string) => Promise<DataType[]>,
        updateAppStatusFn: (delta: Partial<PlayerStatus_RPG>) => void 
      ) => {
        if(isSummarizingRPG[summaryType as keyof typeof isSummarizingRPG]) return;
        setIsSummarizingRPG(prev => ({ ...prev, [summaryType]: true }));
        let notifications: RPGNotificationDetails[] = [];
        let achievements: string[] = [];
        let deltaUpdate: Partial<PlayerStatus_RPG> = {};
        try {
          const updatedData = await serviceFn(dialogueLog, currentData, summaryModelId);
          
          if (summaryType === 'inventory') deltaUpdate.inventory = updatedData as unknown as InventoryItem_RPG[];
          else if (summaryType === 'locations') deltaUpdate.visitedLocations = updatedData as unknown as VisitedLocation_RPG[];
          else if (summaryType === 'quests') deltaUpdate.quests = updatedData as unknown as Quest_RPG[];
          else if (summaryType === 'characters') deltaUpdate.characterProfiles = updatedData as unknown as CharacterProfile_RPG[];
          else if (summaryType === 'importantEvents') deltaUpdate.importantEvents = updatedData as unknown as ImportantEvent_RPG[];
          
          updateAppStatusFn(deltaUpdate); 
    
          showRateLimitedNotificationRPG(`${summaryType}_summary_success`, {
            message: `${UIText[summaryType as keyof typeof UIText] || summaryType} ${UIText.statusAutoUpdated.split(' ')[1]}`,
            type: 'info',
            displayToUser: true
          });
    
          if (setActiveDetailModalFn) setActiveDetailModalFn(summaryType);
          if (summaryType === 'importantEvents' && (updatedData as ImportantEvent_RPG[]).length >= 5) achievements.push('event_tracker');
          if (summaryType === 'inventory' && (updatedData as InventoryItem_RPG[]).length >= 10) achievements.push('backpacker');
    
        } catch (error: any) {
          onNotifications([{ message: `Failed to summarize ${summaryType}: ${error.message}`, type: 'error', displayToUser: true }]);
          if (setActiveDetailModalFn) setActiveDetailModalFn(summaryType);
        }
        finally {
          setIsSummarizingRPG(prev => ({ ...prev, [summaryType]: false }));
          if(achievements.length > 0) onAchievementsUnlock(achievements);
        }
    }, [isSummarizingRPG, setActiveDetailModalFn, onNotifications, onAchievementsUnlock, showRateLimitedNotificationRPG]);


    const handleSummarizeInventoryAndShowDetailsRPG_local = useCallback((dialogueLog: DialogueLine[], summaryModelId: string, currentStatusRPG: PlayerStatus_RPG, updateAppStatusFn: (updates: Partial<PlayerStatus_RPG>) => void) => {
        handleSummarizeAndShowDetailsRPG<InventoryItem_RPG>(dialogueLog, summaryModelId, 'inventory', currentStatusRPG.inventory, summarizeInventory, updateAppStatusFn);
    }, [handleSummarizeAndShowDetailsRPG]);

    const handleSummarizeLocationsAndShowDetailsRPG_local = useCallback((dialogueLog: DialogueLine[], summaryModelId: string, currentStatusRPG: PlayerStatus_RPG, updateAppStatusFn: (updates: Partial<PlayerStatus_RPG>) => void) => {
        handleSummarizeAndShowDetailsRPG<VisitedLocation_RPG>(dialogueLog, summaryModelId, 'locations', currentStatusRPG.visitedLocations, summarizeLocations, updateAppStatusFn);
    }, [handleSummarizeAndShowDetailsRPG]);

    const handleSummarizeQuestsAndShowDetailsRPG_local = useCallback((dialogueLog: DialogueLine[], summaryModelId: string, currentStatusRPG: PlayerStatus_RPG, updateAppStatusFn: (updates: Partial<PlayerStatus_RPG>) => void) => {
        handleSummarizeAndShowDetailsRPG<Quest_RPG>(dialogueLog, summaryModelId, 'quests', currentStatusRPG.quests, summarizeQuests, updateAppStatusFn);
    }, [handleSummarizeAndShowDetailsRPG]);

    const handleSummarizeCharactersAndShowDetailsRPG_local = useCallback((dialogueLog: DialogueLine[], summaryModelId: string, currentStatusRPG: PlayerStatus_RPG, updateAppStatusFn: (updates: Partial<PlayerStatus_RPG>) => void) => {
        handleSummarizeAndShowDetailsRPG<CharacterProfile_RPG>(dialogueLog, summaryModelId, 'characters', currentStatusRPG.characterProfiles, summarizeCharacters, updateAppStatusFn);
    }, [handleSummarizeAndShowDetailsRPG]);

    const handleSummarizeImportantEventsAndShowDetailsRPG_local = useCallback((dialogueLog: DialogueLine[], summaryModelId: string, currentStatusRPG: PlayerStatus_RPG, updateAppStatusFn: (updates: Partial<PlayerStatus_RPG>) => void) => {
        handleSummarizeAndShowDetailsRPG<ImportantEvent_RPG>(dialogueLog, summaryModelId, 'importantEvents', currentStatusRPG.importantEvents, summarizeImportantEvents, updateAppStatusFn);
    }, [handleSummarizeAndShowDetailsRPG]);


    return {
        isSummarizingRPG, 
        allocateAttributePointRPG,
        allocateSkillPointRPG,
        processStoryUpdateRPG,
        handleSummarizeInventoryAndShowDetailsRPG: handleSummarizeInventoryAndShowDetailsRPG_local,
        handleSummarizeLocationsAndShowDetailsRPG: handleSummarizeLocationsAndShowDetailsRPG_local,
        handleSummarizeQuestsAndShowDetailsRPG: handleSummarizeQuestsAndShowDetailsRPG_local,
        handleSummarizeCharactersAndShowDetailsRPG: handleSummarizeCharactersAndShowDetailsRPG_local,
        handleSummarizeImportantEventsAndShowDetailsRPG: handleSummarizeImportantEventsAndShowDetailsRPG_local,
    };
};
