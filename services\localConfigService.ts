import { GameSettingsData, UserPreferences, Theme } from '../types';
import { localApiService } from './localApiService';

export interface LocalConfig {
  // AI Configuration
  ai: {
    provider: 'gemini' | 'openai' | 'anthropic' | 'local';
    fallbackToCloud: boolean;
    timeout: number;
    maxRetries: number;
    // Provider-specific configurations
    geminiApiKey?: string;
    openaiApiKey?: string;
    openaiBaseUrl?: string;
    anthropicApiKey?: string;
    localEndpoint?: string;
    // Legacy support
    cloudApiKey?: string;
  };
  
  // Image Generation Configuration
  imageGeneration: {
    provider: 'local' | 'cloud' | 'disabled';
    localEndpoint?: string;
    cloudService?: 'pollinations' | 'stability' | 'dalle';
    quality: 'low' | 'medium' | 'high';
    cacheEnabled: boolean;
    maxCacheSize: number; // in MB
  };
  
  // Storage Configuration
  storage: {
    provider: 'local' | 'cloud' | 'hybrid';
    localPath?: string;
    cloudProvider?: 'gist' | 's3' | 'dropbox';
    autoBackup: boolean;
    backupInterval: number; // in minutes
    maxBackups: number;
  };
  
  // Performance Configuration
  performance: {
    enableCaching: boolean;
    cacheSize: number; // in MB
    enableCompression: boolean;
    lazyLoading: boolean;
    maxConcurrentRequests: number;
  };
  
  // Security Configuration
  security: {
    enableEncryption: boolean;
    encryptionKey?: string;
    allowExternalConnections: boolean;
    trustedDomains: string[];
  };
  
  // Feature Flags
  features: {
    enableLocalAI: boolean;
    enableLocalImageGen: boolean;
    enableAdvancedRegex: boolean;
    enableCharacterRelationships: boolean;
    enableWorldBookHierarchy: boolean;
    enableBatchOperations: boolean;
    enableExperimentalFeatures: boolean;
  };
}

export interface SystemInfo {
  platform: string;
  userAgent: string;
  memory: {
    used: number;
    total: number;
    available: number;
  };
  storage: {
    used: number;
    available: number;
  };
  network: {
    online: boolean;
    connectionType: string;
  };
  capabilities: {
    webGL: boolean;
    webWorkers: boolean;
    serviceWorkers: boolean;
    indexedDB: boolean;
    localStorage: boolean;
  };
}

export class LocalConfigService {
  private config: LocalConfig;
  private readonly CONFIG_KEY = 'memoryable_local_config_v1';
  private readonly DEFAULT_CONFIG: LocalConfig = {
    ai: {
      provider: 'gemini',
      fallbackToCloud: true,
      timeout: 30000,
      maxRetries: 3,
      openaiBaseUrl: 'https://api.openai.com/v1'
    },
    imageGeneration: {
      provider: 'cloud',
      cloudService: 'pollinations',
      quality: 'medium',
      cacheEnabled: true,
      maxCacheSize: 100
    },
    storage: {
      provider: 'local',
      autoBackup: true,
      backupInterval: 30,
      maxBackups: 10
    },
    performance: {
      enableCaching: true,
      cacheSize: 50,
      enableCompression: true,
      lazyLoading: true,
      maxConcurrentRequests: 3
    },
    security: {
      enableEncryption: false,
      allowExternalConnections: true,
      trustedDomains: ['localhost', '127.0.0.1', 'pollinations.ai']
    },
    features: {
      enableLocalAI: false,
      enableLocalImageGen: false,
      enableAdvancedRegex: true,
      enableCharacterRelationships: true,
      enableWorldBookHierarchy: true,
      enableBatchOperations: true,
      enableExperimentalFeatures: false
    }
  };

  constructor() {
    this.config = this.loadConfig();
    this.detectCapabilities();
  }

  private loadConfig(): LocalConfig {
    try {
      const stored = localStorage.getItem(this.CONFIG_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...this.DEFAULT_CONFIG, ...parsed };
      }
    } catch (error) {
      console.warn('Failed to load local config:', error);
    }
    return { ...this.DEFAULT_CONFIG };
  }

  private saveConfig(): void {
    try {
      localStorage.setItem(this.CONFIG_KEY, JSON.stringify(this.config));
    } catch (error) {
      console.error('Failed to save local config:', error);
    }
  }

  private async detectCapabilities(): Promise<void> {
    // Detect local server availability
    const isLocalServerAvailable = await localApiService.checkServerHealth();
    
    if (isLocalServerAvailable) {
      this.config.features.enableLocalAI = true;
      this.config.features.enableLocalImageGen = true;
      this.config.ai.provider = 'hybrid';
      this.config.imageGeneration.provider = 'local';
    }

    this.saveConfig();
  }

  // Configuration Management
  getConfig(): LocalConfig {
    return { ...this.config };
  }

  updateConfig(updates: Partial<LocalConfig>): void {
    this.config = { ...this.config, ...updates };
    this.saveConfig();
  }

  resetConfig(): void {
    this.config = { ...this.DEFAULT_CONFIG };
    this.saveConfig();
  }

  // AI Configuration
  getAIConfig() {
    return this.config.ai;
  }

  updateAIConfig(updates: Partial<LocalConfig['ai']>): void {
    this.config.ai = { ...this.config.ai, ...updates };
    this.saveConfig();
  }

  // Image Generation Configuration
  getImageConfig() {
    return this.config.imageGeneration;
  }

  updateImageConfig(updates: Partial<LocalConfig['imageGeneration']>): void {
    this.config.imageGeneration = { ...this.config.imageGeneration, ...updates };
    this.saveConfig();
  }

  // Storage Configuration
  getStorageConfig() {
    return this.config.storage;
  }

  updateStorageConfig(updates: Partial<LocalConfig['storage']>): void {
    this.config.storage = { ...this.config.storage, ...updates };
    this.saveConfig();
  }

  // Feature Flags
  isFeatureEnabled(feature: keyof LocalConfig['features']): boolean {
    return this.config.features[feature];
  }

  enableFeature(feature: keyof LocalConfig['features']): void {
    this.config.features[feature] = true;
    this.saveConfig();
  }

  disableFeature(feature: keyof LocalConfig['features']): void {
    this.config.features[feature] = false;
    this.saveConfig();
  }

  // System Information
  async getSystemInfo(): Promise<SystemInfo> {
    const info: SystemInfo = {
      platform: navigator.platform,
      userAgent: navigator.userAgent,
      memory: {
        used: 0,
        total: 0,
        available: 0
      },
      storage: {
        used: 0,
        available: 0
      },
      network: {
        online: navigator.onLine,
        connectionType: (navigator as any).connection?.effectiveType || 'unknown'
      },
      capabilities: {
        webGL: this.checkWebGLSupport(),
        webWorkers: typeof Worker !== 'undefined',
        serviceWorkers: 'serviceWorker' in navigator,
        indexedDB: 'indexedDB' in window,
        localStorage: this.checkLocalStorageSupport()
      }
    };

    // Get memory info if available
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      info.memory = {
        used: memInfo.usedJSHeapSize / 1024 / 1024,
        total: memInfo.totalJSHeapSize / 1024 / 1024,
        available: memInfo.jsHeapSizeLimit / 1024 / 1024
      };
    }

    // Get storage info if available
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate();
        info.storage = {
          used: (estimate.usage || 0) / 1024 / 1024,
          available: (estimate.quota || 0) / 1024 / 1024
        };
      } catch (error) {
        console.warn('Failed to get storage estimate:', error);
      }
    }

    return info;
  }

  private checkWebGLSupport(): boolean {
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch (error) {
      return false;
    }
  }

  private checkLocalStorageSupport(): boolean {
    try {
      const test = 'test';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (error) {
      return false;
    }
  }

  // Performance Optimization
  getOptimalSettings(): Partial<LocalConfig> {
    const systemInfo = this.getSystemInfo();
    const recommendations: Partial<LocalConfig> = {};

    // Adjust based on available memory
    systemInfo.then(info => {
      if (info.memory.available < 512) { // Less than 512MB
        recommendations.performance = {
          ...this.config.performance,
          enableCaching: false,
          cacheSize: 10,
          maxConcurrentRequests: 1
        };
        recommendations.imageGeneration = {
          ...this.config.imageGeneration,
          quality: 'low',
          maxCacheSize: 20
        };
      } else if (info.memory.available < 1024) { // Less than 1GB
        recommendations.performance = {
          ...this.config.performance,
          cacheSize: 25,
          maxConcurrentRequests: 2
        };
        recommendations.imageGeneration = {
          ...this.config.imageGeneration,
          quality: 'medium',
          maxCacheSize: 50
        };
      }

      // Adjust based on network
      if (!info.network.online) {
        recommendations.ai = {
          ...this.config.ai,
          provider: 'local',
          fallbackToCloud: false
        };
        recommendations.imageGeneration = {
          ...this.config.imageGeneration,
          provider: 'local'
        };
      }
    });

    return recommendations;
  }

  // Migration and Compatibility
  migrateFromOldConfig(oldConfig: any): void {
    // Migrate from old configuration format
    const migrated: Partial<LocalConfig> = {};

    if (oldConfig.enableGistAutoBackup) {
      migrated.storage = {
        ...this.config.storage,
        provider: 'cloud',
        cloudProvider: 'gist',
        autoBackup: oldConfig.enableGistAutoBackup,
        backupInterval: (oldConfig.gistAutoBackupIntervalHours || 3) * 60
      };
    }

    if (oldConfig.enableRegexReplacement) {
      migrated.features = {
        ...this.config.features,
        enableAdvancedRegex: oldConfig.enableRegexReplacement
      };
    }

    this.updateConfig(migrated);
  }

  // Export/Import Configuration
  exportConfig(): string {
    return JSON.stringify({
      version: '1.0',
      exportedAt: new Date().toISOString(),
      config: this.config
    }, null, 2);
  }

  importConfig(configJson: string): { success: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      const data = JSON.parse(configJson);
      
      if (!data.config) {
        errors.push('Invalid configuration format');
        return { success: false, errors };
      }

      // Validate configuration
      const validatedConfig = this.validateConfig(data.config);
      if (validatedConfig.errors.length > 0) {
        errors.push(...validatedConfig.errors);
      }

      if (errors.length === 0) {
        this.config = { ...this.DEFAULT_CONFIG, ...data.config };
        this.saveConfig();
        return { success: true, errors: [] };
      }
    } catch (error) {
      errors.push(`JSON parsing failed: ${error.message}`);
    }

    return { success: false, errors };
  }

  private validateConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate AI configuration
    if (config.ai) {
      if (config.ai.provider && !['gemini', 'openai', 'anthropic', 'local'].includes(config.ai.provider)) {
        errors.push('Invalid AI provider');
      }
      if (config.ai.timeout && (config.ai.timeout < 1000 || config.ai.timeout > 120000)) {
        errors.push('AI timeout must be between 1000 and 120000 ms');
      }
      if (config.ai.openaiBaseUrl && !config.ai.openaiBaseUrl.startsWith('http')) {
        errors.push('OpenAI base URL must be a valid HTTP/HTTPS URL');
      }
    }

    // Validate image generation configuration
    if (config.imageGeneration) {
      if (config.imageGeneration.provider && !['local', 'cloud', 'disabled'].includes(config.imageGeneration.provider)) {
        errors.push('Invalid image generation provider');
      }
      if (config.imageGeneration.quality && !['low', 'medium', 'high'].includes(config.imageGeneration.quality)) {
        errors.push('Invalid image quality setting');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Singleton instance
export const localConfigService = new LocalConfigService();
