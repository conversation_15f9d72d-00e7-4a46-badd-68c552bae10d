import React, { useState, useEffect } from 'react';
import { Icons } from '../constants';
import { 
  getAIServiceManager,
  switchAIProvider, 
  validateAIProviders,
  updateAIProviderConfig,
  getCurrentAIProvider,
  checkAIServiceHealth
} from '../services/aiService';
import { AIProviderType } from '../services/aiProviders/types';

interface AIProviderSettingsSectionProps {
  onSettingsChange: (newSettings: any) => void;
  enableBackdropBlur?: boolean;
}

interface ProviderInfo {
  available: boolean;
  current: boolean;
  models: any[];
  status: 'ok' | 'error';
  error?: string;
}

interface TestResult {
  success: boolean;
  responseTime: number;
  error?: string;
  modelCount?: number;
}

const providerNames: Record<AIProviderType, string> = {
  gemini: 'Google Gemini',
  openai: 'OpenAI GPT',
  anthropic: 'Anthropic Claude',
  local: '本地AI',
  'azure-openai': 'Azure OpenAI',
  cohere: 'Cohere',
  huggingface: 'Hugging Face'
};

const providerDescriptions: Record<AIProviderType, string> = {
  gemini: '谷歌最新的多模态AI模型，支持长上下文',
  openai: 'OpenAI的GPT系列模型，功能强大且稳定',
  anthropic: 'Anthropic的Claude模型，注重安全性和有用性',
  local: '本地部署的AI模型，完全私密',
  'azure-openai': '微软Azure平台上的OpenAI服务',
  cohere: 'Cohere的语言模型',
  huggingface: 'Hugging Face平台的开源模型'
};

export const AIProviderSettingsSection: React.FC<AIProviderSettingsSectionProps> = ({
  onSettingsChange,
  enableBackdropBlur = true
}) => {
  const [providers, setProviders] = useState<Record<AIProviderType, ProviderInfo>>({} as any);
  const [currentProvider, setCurrentProvider] = useState<AIProviderType | null>(null);
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<Record<AIProviderType, TestResult>>({} as any);
  const [testingProvider, setTestingProvider] = useState<AIProviderType | null>(null);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({
    gemini: '',
    openai: '',
    anthropic: '',
    azure: ''
  });
  const [customEndpoints, setCustomEndpoints] = useState<Record<string, string>>({
    openai: 'https://api.openai.com/v1',
    local: 'http://localhost:11434/v1'
  });

  const inputClass = "w-full p-2 bg-element-themed text-primary-themed rounded-md border border-themed/70 focus:ring-1 focus:ring-accent-themed focus:border-transparent text-sm placeholder-themed";
  const buttonClass = "px-3 py-1.5 text-xs btn-dreamy btn-dreamy-xs";
  const labelClass = "block text-sm font-medium text-primary-themed mb-1";

  useEffect(() => {
    loadProviderData();
  }, []);

  const loadProviderData = async () => {
    setLoading(true);
    try {
      const aiService = getAIServiceManager();
      const [providerStatus, validation] = await Promise.all([
        Promise.resolve(aiService.getProviderStatus()),
        validateAIProviders()
      ]);

      const combinedProviders: Record<string, ProviderInfo> = {};
      
      for (const [provider, info] of Object.entries(providerStatus)) {
        const models = await aiService.getAllAvailableModels();
        combinedProviders[provider] = {
          ...info,
          models: models[provider as AIProviderType] || [],
          status: validation[provider as AIProviderType] ? 'ok' : 'error',
          error: validation[provider as AIProviderType] ? undefined : '配置无效或API密钥错误'
        };
      }

      setProviders(combinedProviders as Record<AIProviderType, ProviderInfo>);
      setCurrentProvider(getCurrentAIProvider());
    } catch (error) {
      console.error('Failed to load provider data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProviderSwitch = async (provider: AIProviderType) => {
    setLoading(true);
    try {
      const success = await switchAIProvider(provider);
      if (success) {
        setCurrentProvider(provider);
        await loadProviderData();
        onSettingsChange({ aiProvider: provider });
      }
    } catch (error) {
      console.error('Provider switch failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApiKeyUpdate = async (provider: AIProviderType, apiKey: string) => {
    try {
      const config: any = { apiKey };
      
      // Add custom endpoint for OpenAI-compatible providers
      if (provider === 'openai' && customEndpoints.openai) {
        config.baseUrl = customEndpoints.openai;
      }
      
      updateAIProviderConfig(provider, config);
      setApiKeys(prev => ({ ...prev, [provider]: apiKey }));
      await loadProviderData();
    } catch (error) {
      console.error('API key update failed:', error);
    }
  };

  const handleEndpointUpdate = async (provider: AIProviderType, endpoint: string) => {
    try {
      setCustomEndpoints(prev => ({ ...prev, [provider]: endpoint }));
      
      // Update provider config with new endpoint
      updateAIProviderConfig(provider, { 
        baseUrl: endpoint,
        apiKey: apiKeys[provider] || ''
      });
      
      await loadProviderData();
    } catch (error) {
      console.error('Endpoint update failed:', error);
    }
  };

  const handleTestConnection = async (provider: AIProviderType) => {
    setTestingProvider(provider);
    try {
      const aiService = getAIServiceManager();
      const providerInstance = aiService.getCurrentProvider();
      
      if (providerInstance && providerInstance.getProviderType() === provider) {
        const result = await providerInstance.testConnection();
        setTestResults(prev => ({ ...prev, [provider]: result }));
      }
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        [provider]: { 
          success: false, 
          responseTime: 0, 
          error: error.message || '测试失败' 
        } 
      }));
    } finally {
      setTestingProvider(null);
    }
  };

  const getStatusColor = (status: 'ok' | 'error') => {
    return status === 'ok' ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (status: 'ok' | 'error') => {
    return status === 'ok' ? '✅' : '❌';
  };

  const renderProviderCard = (provider: AIProviderType, info: ProviderInfo) => {
    const testResult = testResults[provider];
    const isTesting = testingProvider === provider;

    return (
      <div
        key={provider}
        className={`border rounded-lg p-4 mb-4 ${
          info.current ? 'border-blue-500 bg-blue-50/10' : 'border-gray-200'
        }`}
      >
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <h4 className="text-lg font-semibold text-primary-themed">
              {providerNames[provider]}
            </h4>
            <span className={`text-sm ${getStatusColor(info.status)}`}>
              {getStatusIcon(info.status)} {info.status === 'ok' ? '可用' : '不可用'}
            </span>
            {info.current && (
              <span className="bg-blue-500 text-white px-2 py-1 rounded text-xs">
                当前使用
              </span>
            )}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => handleTestConnection(provider)}
              disabled={isTesting || !apiKeys[provider]}
              className={`${buttonClass} ${isTesting ? 'opacity-50' : ''}`}
            >
              {isTesting ? (
                <Icons.ArrowPath className="w-3 h-3 animate-spin mr-1" />
              ) : (
                <Icons.WifiIcon className="w-3 h-3 mr-1" />
              )}
              测试连接
            </button>
            <button
              onClick={() => handleProviderSwitch(provider)}
              disabled={loading || info.status === 'error' || info.current}
              className={`${buttonClass} ${
                info.current
                  ? 'opacity-50 cursor-not-allowed'
                  : info.status === 'ok'
                  ? 'bg-blue-500 text-white hover:bg-blue-600'
                  : 'opacity-50 cursor-not-allowed'
              }`}
            >
              {info.current ? '当前使用' : '切换'}
            </button>
          </div>
        </div>

        <p className="text-sm text-secondary-themed mb-3">
          {providerDescriptions[provider]}
        </p>

        {/* Test Results */}
        {testResult && (
          <div className={`text-sm p-2 rounded mb-3 ${
            testResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {testResult.success ? (
              <span>✅ 连接成功 - 响应时间: {testResult.responseTime}ms{testResult.modelCount ? ` - 可用模型: ${testResult.modelCount}个` : ''}</span>
            ) : (
              <span>❌ 连接失败: {testResult.error}</span>
            )}
          </div>
        )}

        {/* API Key Input */}
        {provider !== 'local' && (
          <div className="mb-3">
            <label className={labelClass}>
              API密钥
            </label>
            <div className="flex space-x-2">
              <input
                type="password"
                value={apiKeys[provider] || ''}
                onChange={(e) => setApiKeys(prev => ({ ...prev, [provider]: e.target.value }))}
                placeholder={`输入${providerNames[provider]}的API密钥`}
                className={`${inputClass} flex-1`}
              />
              <button
                onClick={() => handleApiKeyUpdate(provider, apiKeys[provider] || '')}
                className={`${buttonClass} bg-green-500 text-white hover:bg-green-600`}
              >
                保存
              </button>
            </div>
          </div>
        )}

        {/* Custom Endpoint for OpenAI-compatible providers */}
        {(provider === 'openai' || provider === 'local') && (
          <div className="mb-3">
            <label className={labelClass}>
              API端点URL {provider === 'openai' && <span className="text-xs text-secondary-themed">(支持OpenAI兼容API)</span>}
            </label>
            <div className="flex space-x-2">
              <input
                type="url"
                value={customEndpoints[provider] || ''}
                onChange={(e) => setCustomEndpoints(prev => ({ ...prev, [provider]: e.target.value }))}
                placeholder={provider === 'openai' ? 'https://api.openai.com/v1' : 'http://localhost:11434/v1'}
                className={`${inputClass} flex-1`}
              />
              <button
                onClick={() => handleEndpointUpdate(provider, customEndpoints[provider] || '')}
                className={`${buttonClass} bg-blue-500 text-white hover:bg-blue-600`}
              >
                更新
              </button>
            </div>
            <p className="text-xs text-secondary-themed mt-1">
              支持本地部署的vLLM、Ollama、LM Studio等OpenAI兼容服务
            </p>
          </div>
        )}

        {/* Available Models */}
        {info.models.length > 0 && (
          <div>
            <h5 className="text-sm font-medium text-primary-themed mb-2">可用模型 ({info.models.length}个)</h5>
            <div className="flex flex-wrap gap-2">
              {info.models.slice(0, 6).map((model) => (
                <span
                  key={model.id}
                  className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
                  title={model.name || model.id}
                >
                  {(model.name || model.id).length > 20 
                    ? `${(model.name || model.id).substring(0, 20)}...` 
                    : (model.name || model.id)
                  }
                </span>
              ))}
              {info.models.length > 6 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                  +{info.models.length - 6} 更多
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-primary-themed">AI提供商设置</h3>
        <button
          onClick={loadProviderData}
          disabled={loading}
          className={`${buttonClass} ${loading ? 'opacity-50' : ''}`}
        >
          {loading ? (
            <Icons.ArrowPath className="w-3 h-3 animate-spin mr-1" />
          ) : (
            <Icons.ArrowPath className="w-3 h-3 mr-1" />
          )}
          刷新状态
        </button>
      </div>

      <div className="text-sm text-secondary-themed">
        当前使用: <span className="font-medium text-primary-themed">
          {currentProvider ? providerNames[currentProvider] : '未知'}
        </span>
      </div>

      {Object.entries(providers).map(([provider, info]) => 
        renderProviderCard(provider as AIProviderType, info)
      )}
    </div>
  );
};
