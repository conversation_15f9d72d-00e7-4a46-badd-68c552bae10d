import React, { useState, useEffect } from 'react';
import { Icons } from '../constants';
import {
  switchAIProvider,
  validateAIProviders,
  updateAIProviderConfig,
  getCurrentAIProvider,
  checkAIServiceHealth,
  testAIProviderConnection
} from '../services/aiService';
import { getAIServiceManager } from '../services/geminiClient';
import { AIProviderType } from '../services/aiProviders/types';
import { localConfigService } from '../services/localConfigService';

interface AIProviderSettingsSectionProps {
  onSettingsChange: (newSettings: any) => void;
  enableBackdropBlur?: boolean;
}

interface ProviderInfo {
  available: boolean;
  current: boolean;
  models: any[];
  status: 'ok' | 'error';
  error?: string;
}

interface TestResult {
  success: boolean;
  responseTime: number;
  error?: string;
  modelCount?: number;
}

const providerNames: Record<AIProviderType, string> = {
  gemini: 'Google Gemini',
  openai: 'OpenAI GPT',
  anthropic: 'Anthropic Claude',
  local: '本地AI',
  'azure-openai': 'Azure OpenAI',
  cohere: 'Cohere',
  huggingface: 'Hugging Face'
};

const providerDescriptions: Record<AIProviderType, string> = {
  gemini: '谷歌最新的多模态AI模型，支持长上下文和图像理解',
  openai: 'OpenAI的GPT系列模型，功能强大且稳定，支持自定义端点',
  anthropic: 'Anthropic的Claude模型，注重安全性和有用性',
  local: '本地部署的AI模型，完全私密，支持Ollama、LM Studio等',
  'azure-openai': '微软Azure平台上的OpenAI服务，企业级安全',
  cohere: 'Cohere的语言模型，专注于企业应用',
  huggingface: 'Hugging Face平台的开源模型'
};

const providerIcons: Record<AIProviderType, string> = {
  gemini: '🤖',
  openai: '🧠',
  anthropic: '🎭',
  local: '🏠',
  'azure-openai': '☁️',
  cohere: '🔗',
  huggingface: '🤗'
};

const providerCapabilities: Record<AIProviderType, string[]> = {
  gemini: ['长上下文', '多模态', '快速响应', '免费额度'],
  openai: ['高质量输出', '函数调用', '代码生成', '自定义端点'],
  anthropic: ['安全可靠', '长对话', '分析能力', '道德推理'],
  local: ['完全私密', '无限使用', '离线运行', '自定义模型'],
  'azure-openai': ['企业级', '数据安全', '合规性', '高可用'],
  cohere: ['企业应用', '多语言', '搜索增强', 'RAG支持'],
  huggingface: ['开源模型', '社区驱动', '多样选择', '免费使用']
};

export const AIProviderSettingsSection: React.FC<AIProviderSettingsSectionProps> = ({
  onSettingsChange,
  enableBackdropBlur = true
}) => {
  const [providers, setProviders] = useState<Record<AIProviderType, ProviderInfo>>({} as any);
  const [currentProvider, setCurrentProvider] = useState<AIProviderType | null>(null);
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<Record<AIProviderType, TestResult>>({} as any);
  const [testingProvider, setTestingProvider] = useState<AIProviderType | null>(null);
  const [enabledProviders, setEnabledProviders] = useState<Record<AIProviderType, boolean>>({} as any);
  const [expandedProviders, setExpandedProviders] = useState<Record<AIProviderType, boolean>>({} as any);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({
    gemini: '',
    openai: '',
    anthropic: '',
    'azure-openai': '',
    local: '',
    cohere: '',
    huggingface: ''
  });
  const [customEndpoints, setCustomEndpoints] = useState<Record<string, string>>({
    openai: 'https://api.openai.com/v1',
    'azure-openai': 'https://your-resource.openai.azure.com',
    local: 'http://localhost:11434/v1',
    cohere: 'https://api.cohere.ai/v1',
    huggingface: 'https://api-inference.huggingface.co/models'
  });
  const [customProviders, setCustomProviders] = useState<Array<{
    id: string;
    name: string;
    endpoint: string;
    apiKey: string;
  }>>([]);

  const inputClass = "w-full p-2 bg-element-themed text-primary-themed rounded-md border border-themed/70 focus:ring-1 focus:ring-accent-themed focus:border-transparent text-sm placeholder-themed";
  const buttonClass = "px-3 py-1.5 text-xs btn-dreamy btn-dreamy-xs";
  const labelClass = "block text-sm font-medium text-primary-themed mb-1";

  useEffect(() => {
    loadProviderData();
  }, []);

  const loadProviderData = async () => {
    setLoading(true);
    try {
      // Load configuration from local config service
      const config = localConfigService.getConfig();

      // Load API keys from config
      setApiKeys({
        gemini: config.ai?.geminiApiKey || '',
        openai: config.ai?.openaiApiKey || '',
        anthropic: config.ai?.anthropicApiKey || '',
        'azure-openai': config.ai?.openaiApiKey || '',
        local: '',
        cohere: '',
        huggingface: ''
      });

      // Load custom endpoints
      setCustomEndpoints(prev => ({
        ...prev,
        openai: config.ai?.openaiBaseUrl || 'https://api.openai.com/v1',
        local: config.ai?.localEndpoint || 'http://localhost:11434/v1'
      }));

      const aiService = getAIServiceManager();
      const [providerStatus, validation, allModels] = await Promise.all([
        Promise.resolve(aiService.getProviderStatus()),
        validateAIProviders().catch(() => ({} as Record<AIProviderType, boolean>)),
        aiService.getAllAvailableModels().catch(() => ({} as Record<AIProviderType, any[]>))
      ]);

      const combinedProviders: Record<string, ProviderInfo> = {};

      // Initialize all supported providers
      const supportedProviders: AIProviderType[] = ['gemini', 'openai', 'anthropic', 'azure-openai', 'local'];

      for (const provider of supportedProviders) {
        const hasApiKey = apiKeys[provider] || (provider === 'local');
        combinedProviders[provider] = {
          available: hasApiKey,
          current: provider === getCurrentAIProvider(),
          models: allModels[provider] || [],
          status: validation[provider] ? 'ok' : 'error',
          error: validation[provider] ? undefined : hasApiKey ? '连接测试失败' : '需要配置API密钥'
        };
      }

      setProviders(combinedProviders as Record<AIProviderType, ProviderInfo>);
      setCurrentProvider(getCurrentAIProvider());

      // Set enabled providers based on availability
      const enabled: Record<string, boolean> = {};
      for (const provider of supportedProviders) {
        enabled[provider] = combinedProviders[provider].available;
      }
      setEnabledProviders(enabled as Record<AIProviderType, boolean>);

    } catch (error) {
      console.error('Failed to load provider data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleProviderSwitch = async (provider: AIProviderType) => {
    setLoading(true);
    try {
      const success = await switchAIProvider(provider);
      if (success) {
        setCurrentProvider(provider);
        await loadProviderData();
        onSettingsChange({ aiProvider: provider });
      }
    } catch (error) {
      console.error('Provider switch failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApiKeyUpdate = async (provider: AIProviderType, apiKey: string) => {
    try {
      // Update local config service
      const config = localConfigService.getConfig();
      const updatedConfig = { ...config };

      switch (provider) {
        case 'gemini':
          updatedConfig.ai = { ...updatedConfig.ai, geminiApiKey: apiKey };
          break;
        case 'openai':
          updatedConfig.ai = { ...updatedConfig.ai, openaiApiKey: apiKey };
          break;
        case 'anthropic':
          updatedConfig.ai = { ...updatedConfig.ai, anthropicApiKey: apiKey };
          break;
        case 'azure-openai':
          updatedConfig.ai = { ...updatedConfig.ai, openaiApiKey: apiKey };
          break;
      }

      localConfigService.updateConfig(updatedConfig);

      // Update AI provider config
      const providerConfig: any = { apiKey };

      // Add custom endpoint for OpenAI-compatible providers
      if ((provider === 'openai' || provider === 'azure-openai') && customEndpoints[provider]) {
        providerConfig.baseUrl = customEndpoints[provider];
      }

      updateAIProviderConfig(provider, providerConfig);
      setApiKeys(prev => ({ ...prev, [provider]: apiKey }));

      // Re-validate after update
      setTimeout(() => loadProviderData(), 500);
    } catch (error) {
      console.error('API key update failed:', error);
    }
  };

  const handleEndpointUpdate = async (provider: AIProviderType, endpoint: string) => {
    try {
      // Update local config
      const config = localConfigService.getConfig();
      const updatedConfig = { ...config };

      if (provider === 'openai') {
        updatedConfig.ai = { ...updatedConfig.ai, openaiBaseUrl: endpoint };
      } else if (provider === 'local') {
        updatedConfig.ai = { ...updatedConfig.ai, localEndpoint: endpoint };
      }

      localConfigService.updateConfig(updatedConfig);

      setCustomEndpoints(prev => ({ ...prev, [provider]: endpoint }));

      // Update provider config with new endpoint
      updateAIProviderConfig(provider, {
        baseUrl: endpoint,
        apiKey: apiKeys[provider] || ''
      });

      // Re-validate after update
      setTimeout(() => loadProviderData(), 500);
    } catch (error) {
      console.error('Endpoint update failed:', error);
    }
  };

  const handleTestConnection = async (provider: AIProviderType) => {
    setTestingProvider(provider);
    try {
      const result = await testAIProviderConnection(provider);
      setTestResults(prev => ({ ...prev, [provider]: result }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [provider]: {
          success: false,
          responseTime: 0,
          error: error.message || '测试失败'
        }
      }));
    } finally {
      setTestingProvider(null);
    }
  };

  const getStatusColor = (status: 'ok' | 'error') => {
    return status === 'ok' ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (status: 'ok' | 'error') => {
    return status === 'ok' ? '✅' : '❌';
  };

  const toggleProviderExpansion = (provider: AIProviderType) => {
    setExpandedProviders(prev => ({
      ...prev,
      [provider]: !prev[provider]
    }));
  };

  const toggleProviderEnabled = (provider: AIProviderType) => {
    setEnabledProviders(prev => ({
      ...prev,
      [provider]: !prev[provider]
    }));
  };

  const renderProviderCard = (provider: AIProviderType, info: ProviderInfo) => {
    const testResult = testResults[provider];
    const isTesting = testingProvider === provider;
    const isExpanded = expandedProviders[provider];
    const isEnabled = enabledProviders[provider];

    return (
      <div
        key={provider}
        className={`border rounded-lg mb-4 transition-all duration-200 ${
          info.current
            ? 'border-accent-themed bg-accent-themed/5 shadow-md'
            : isEnabled
              ? 'border-themed/50 bg-element-themed/30'
              : 'border-themed/30 bg-element-themed/10 opacity-75'
        }`}
      >
        {/* Provider Header */}
        <div className="p-4 border-b border-themed/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <span className="text-2xl">{providerIcons[provider]}</span>
                <div>
                  <h4 className="text-lg font-semibold text-primary-themed">
                    {providerNames[provider]}
                  </h4>
                  <p className="text-sm text-secondary-themed">
                    {providerDescriptions[provider]}
                  </p>
                </div>
              </div>

              {/* Status indicators */}
              <div className="flex items-center space-x-2">
                <span className={`text-sm px-2 py-1 rounded-full ${getStatusColor(info.status)}`}>
                  {getStatusIcon(info.status)} {info.status === 'ok' ? '已连接' : '未连接'}
                </span>
                {info.current && (
                  <span className="bg-accent-themed text-white px-2 py-1 rounded-full text-xs font-medium">
                    当前使用
                  </span>
                )}
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-2">
              {/* Enable/Disable Toggle */}
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={isEnabled}
                  onChange={() => toggleProviderEnabled(provider)}
                  className="w-4 h-4 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed"
                />
                <span className="text-sm text-secondary-themed">启用</span>
              </label>

              {/* Expand/Collapse */}
              <button
                onClick={() => toggleProviderExpansion(provider)}
                className={`${buttonClass} p-2`}
                title={isExpanded ? '收起配置' : '展开配置'}
              >
                <Icons.ChevronDown className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
              </button>
            </div>
          </div>

          {/* Capabilities */}
          <div className="mt-3 flex flex-wrap gap-2">
            {providerCapabilities[provider]?.map((capability, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-accent-themed/10 text-accent-themed rounded text-xs"
              >
                {capability}
              </span>
            ))}
          </div>
        </div>

        {/* Collapsible Configuration Section */}
        {isExpanded && isEnabled && (
          <div className="p-4 space-y-4">
            {/* Test Results */}
            {testResult && (
              <div className={`text-sm p-3 rounded-lg border ${
                testResult.success
                  ? 'bg-green-50 text-green-800 border-green-200'
                  : 'bg-red-50 text-red-800 border-red-200'
              }`}>
                {testResult.success ? (
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600">✅</span>
                    <span>连接成功 - 响应时间: {testResult.responseTime}ms</span>
                    {testResult.modelCount && (
                      <span className="text-green-600">• 可用模型: {testResult.modelCount}个</span>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center space-x-2">
                    <span className="text-red-600">❌</span>
                    <span>连接失败: {testResult.error}</span>
                  </div>
                )}
              </div>
            )}

            {/* API Key Configuration */}
            {provider !== 'local' && (
              <div className="space-y-2">
                <label className={`${labelClass} flex items-center space-x-2`}>
                  <Icons.Key className="w-4 h-4" />
                  <span>API密钥</span>
                  {!apiKeys[provider] && <span className="text-red-500 text-xs">*必填</span>}
                </label>
                <div className="flex space-x-2">
                  <input
                    type="password"
                    value={apiKeys[provider] || ''}
                    onChange={(e) => setApiKeys(prev => ({ ...prev, [provider]: e.target.value }))}
                    placeholder={`输入${providerNames[provider]}的API密钥`}
                    className={`${inputClass} flex-1`}
                  />
                  <button
                    onClick={() => handleApiKeyUpdate(provider, apiKeys[provider] || '')}
                    disabled={!apiKeys[provider]}
                    className={`${buttonClass} ${
                      apiKeys[provider]
                        ? 'bg-green-500 text-white hover:bg-green-600'
                        : 'opacity-50 cursor-not-allowed'
                    }`}
                  >
                    <Icons.Check className="w-4 h-4 mr-1" />
                    保存
                  </button>
                </div>
                {provider === 'gemini' && (
                  <p className="text-xs text-secondary-themed">
                    获取API密钥: <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-accent-themed hover:underline">Google AI Studio</a>
                  </p>
                )}
                {provider === 'openai' && (
                  <p className="text-xs text-secondary-themed">
                    获取API密钥: <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-accent-themed hover:underline">OpenAI Platform</a>
                  </p>
                )}
                {provider === 'anthropic' && (
                  <p className="text-xs text-secondary-themed">
                    获取API密钥: <a href="https://console.anthropic.com/" target="_blank" rel="noopener noreferrer" className="text-accent-themed hover:underline">Anthropic Console</a>
                  </p>
                )}
              </div>
            )}

            {/* Custom Endpoint Configuration */}
            {(provider === 'openai' || provider === 'local' || provider === 'azure-openai') && (
              <div className="space-y-2">
                <label className={`${labelClass} flex items-center space-x-2`}>
                  <Icons.Globe className="w-4 h-4" />
                  <span>API端点URL</span>
                  {provider === 'openai' && <span className="text-xs text-secondary-themed">(支持OpenAI兼容API)</span>}
                  {provider === 'azure-openai' && <span className="text-xs text-secondary-themed">(Azure OpenAI端点)</span>}
                </label>
                <div className="flex space-x-2">
                  <input
                    type="url"
                    value={customEndpoints[provider] || ''}
                    onChange={(e) => setCustomEndpoints(prev => ({ ...prev, [provider]: e.target.value }))}
                    placeholder={
                      provider === 'openai' ? 'https://api.openai.com/v1' :
                      provider === 'azure-openai' ? 'https://your-resource.openai.azure.com' :
                      'http://localhost:11434/v1'
                    }
                    className={`${inputClass} flex-1`}
                  />
                  <button
                    onClick={() => handleEndpointUpdate(provider, customEndpoints[provider] || '')}
                    className={`${buttonClass} bg-blue-500 text-white hover:bg-blue-600`}
                  >
                    <Icons.ArrowPath className="w-4 h-4 mr-1" />
                    更新
                  </button>
                </div>
                <p className="text-xs text-secondary-themed">
                  {provider === 'local' && '支持本地部署的vLLM、Ollama、LM Studio等OpenAI兼容服务'}
                  {provider === 'openai' && '支持自定义OpenAI兼容API端点，如代理服务器'}
                  {provider === 'azure-openai' && '输入您的Azure OpenAI资源端点URL'}
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4 border-t border-themed/20">
              <div className="flex space-x-2">
                <button
                  onClick={() => handleTestConnection(provider)}
                  disabled={isTesting || (!apiKeys[provider] && provider !== 'local')}
                  className={`${buttonClass} ${
                    isTesting
                      ? 'opacity-50 cursor-not-allowed'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
                >
                  {isTesting ? (
                    <Icons.ArrowPath className="w-4 h-4 animate-spin mr-1" />
                  ) : (
                    <Icons.WifiIcon className="w-4 h-4 mr-1" />
                  )}
                  {isTesting ? '测试中...' : '测试连接'}
                </button>
              </div>

              <button
                onClick={() => handleProviderSwitch(provider)}
                disabled={loading || info.status === 'error' || info.current || !isEnabled}
                className={`${buttonClass} ${
                  info.current
                    ? 'bg-accent-themed text-white cursor-default'
                    : info.status === 'ok' && isEnabled
                    ? 'bg-green-500 text-white hover:bg-green-600'
                    : 'opacity-50 cursor-not-allowed'
                }`}
              >
                {info.current ? (
                  <>
                    <Icons.Check className="w-4 h-4 mr-1" />
                    当前使用
                  </>
                ) : (
                  <>
                    <Icons.ArrowRight className="w-4 h-4 mr-1" />
                    切换使用
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Available Models - Outside collapsible section */}
        {info.models.length > 0 && (
          <div className="p-4 border-t border-themed/20">
            <h5 className="text-sm font-medium text-primary-themed mb-3 flex items-center">
              <Icons.Cpu className="w-4 h-4 mr-2" />
              可用模型 ({info.models.length}个)
            </h5>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {info.models.slice(0, 8).map((model) => (
                <div
                  key={model.id}
                  className="px-3 py-2 bg-element-themed rounded border border-themed/30 text-xs"
                  title={`${model.name || model.id} - 最大tokens: ${model.maxTokens || 'N/A'}`}
                >
                  <div className="font-medium text-primary-themed truncate">
                    {model.name || model.id}
                  </div>
                  {model.maxTokens && (
                    <div className="text-secondary-themed">
                      {model.maxTokens.toLocaleString()} tokens
                    </div>
                  )}
                </div>
              ))}
              {info.models.length > 8 && (
                <div className="px-3 py-2 bg-element-themed/50 rounded border border-themed/20 text-xs text-secondary-themed flex items-center justify-center">
                  +{info.models.length - 8} 更多模型
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-xl font-bold text-primary-themed flex items-center">
            <Icons.Cog6Tooth className="w-6 h-6 mr-2" />
            AI提供商设置
          </h3>
          <p className="text-sm text-secondary-themed mt-1">
            配置和管理多个AI提供商，享受最佳的AI体验
          </p>
        </div>
        <button
          onClick={loadProviderData}
          disabled={loading}
          className={`${buttonClass} ${loading ? 'opacity-50' : ''}`}
        >
          {loading ? (
            <Icons.ArrowPath className="w-4 h-4 animate-spin mr-2" />
          ) : (
            <Icons.ArrowPath className="w-4 h-4 mr-2" />
          )}
          刷新状态
        </button>
      </div>

      {/* Current Provider Status */}
      <div className="p-4 bg-accent-themed/10 border border-accent-themed/30 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-secondary-themed">当前使用:</span>
            <span className="font-semibold text-primary-themed">
              {currentProvider ? (
                <span className="flex items-center space-x-2">
                  <span>{providerIcons[currentProvider]}</span>
                  <span>{providerNames[currentProvider]}</span>
                </span>
              ) : (
                '未配置'
              )}
            </span>
          </div>
          {currentProvider && (
            <span className="text-xs text-accent-themed bg-accent-themed/20 px-2 py-1 rounded">
              活跃中
            </span>
          )}
        </div>
      </div>

      {/* Provider Cards */}
      <div className="space-y-4">
        {Object.entries(providers).map(([provider, info]) =>
          renderProviderCard(provider as AIProviderType, info)
        )}
      </div>

      {/* Quick Actions */}
      <div className="p-4 bg-element-themed/30 rounded-lg border border-themed/30">
        <h4 className="text-sm font-medium text-primary-themed mb-3">快速操作</h4>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => {
              const allProviders = Object.keys(providers) as AIProviderType[];
              allProviders.forEach(provider => {
                setExpandedProviders(prev => ({ ...prev, [provider]: true }));
              });
            }}
            className={`${buttonClass} text-xs`}
          >
            展开所有
          </button>
          <button
            onClick={() => {
              const allProviders = Object.keys(providers) as AIProviderType[];
              allProviders.forEach(provider => {
                setExpandedProviders(prev => ({ ...prev, [provider]: false }));
              });
            }}
            className={`${buttonClass} text-xs`}
          >
            收起所有
          </button>
          <button
            onClick={() => {
              const allProviders = Object.keys(providers) as AIProviderType[];
              allProviders.forEach(provider => {
                if (providers[provider].available) {
                  handleTestConnection(provider);
                }
              });
            }}
            disabled={loading}
            className={`${buttonClass} text-xs ${loading ? 'opacity-50' : ''}`}
          >
            测试所有连接
          </button>
        </div>
      </div>
    </div>
  );
};
