<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI提供商测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .provider-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: #fafafa;
        }
        .provider-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .provider-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .provider-status.available {
            background-color: #28a745;
            color: white;
        }
        .provider-status.unavailable {
            background-color: #dc3545;
            color: white;
        }
        .provider-status.current {
            background-color: #007bff;
            color: white;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .test-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: inherit;
        }
        .response-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            min-height: 100px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 MemoryAble 多AI提供商测试</h1>
        
        <div id="status" class="status info">
            正在初始化AI服务...
        </div>

        <div id="providers-list">
            <!-- 提供商列表将在这里动态生成 -->
        </div>

        <div class="test-section">
            <h3>🧪 AI响应测试</h3>
            <textarea id="test-prompt" placeholder="输入测试提示词...">你好，请简单介绍一下你自己。</textarea>
            <br>
            <button onclick="testAIResponse()" id="test-button">测试当前AI提供商</button>
            <button onclick="testAllProviders()" id="test-all-button">测试所有可用提供商</button>
            
            <div id="response-area" class="response-area">
                测试结果将显示在这里...
            </div>
        </div>
    </div>

    <script type="module">
        // 模拟AI服务功能（实际应用中这些会从真实的服务导入）
        let currentStatus = {
            providers: {
                gemini: { available: true, current: true },
                openai: { available: false, current: false },
                anthropic: { available: false, current: false }
            },
            currentProvider: 'gemini'
        };

        const providerNames = {
            gemini: 'Google Gemini',
            openai: 'OpenAI GPT',
            anthropic: 'Anthropic Claude',
            local: '本地AI'
        };

        const providerDescriptions = {
            gemini: '谷歌最新的多模态AI模型，支持长上下文',
            openai: 'OpenAI的GPT系列模型，功能强大且稳定',
            anthropic: 'Anthropic的Claude模型，注重安全性和有用性',
            local: '本地部署的AI模型，完全私密'
        };

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function renderProviders() {
            const container = document.getElementById('providers-list');
            container.innerHTML = '';

            Object.entries(currentStatus.providers).forEach(([provider, info]) => {
                const card = document.createElement('div');
                card.className = 'provider-card';
                
                let statusBadges = '';
                if (info.available) {
                    statusBadges += '<span class="provider-status available">可用</span>';
                }
                if (info.current) {
                    statusBadges += '<span class="provider-status current">当前使用</span>';
                }
                if (!info.available) {
                    statusBadges += '<span class="provider-status unavailable">不可用</span>';
                }

                card.innerHTML = `
                    <div class="provider-name">${providerNames[provider] || provider}</div>
                    <div style="margin-bottom: 10px;">${statusBadges}</div>
                    <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
                        ${providerDescriptions[provider] || ''}
                    </div>
                    <button onclick="switchProvider('${provider}')" 
                            ${!info.available || info.current ? 'disabled' : ''}>
                        ${info.current ? '当前使用' : info.available ? '切换到此提供商' : '不可用'}
                    </button>
                `;
                
                container.appendChild(card);
            });
        }

        window.switchProvider = function(provider) {
            updateStatus(`正在切换到 ${providerNames[provider]}...`, 'info');
            
            // 模拟切换过程
            setTimeout(() => {
                // 更新状态
                Object.keys(currentStatus.providers).forEach(p => {
                    currentStatus.providers[p].current = p === provider;
                });
                currentStatus.currentProvider = provider;
                
                updateStatus(`已成功切换到 ${providerNames[provider]}`, 'success');
                renderProviders();
            }, 1000);
        };

        window.testAIResponse = async function() {
            const prompt = document.getElementById('test-prompt').value;
            const responseArea = document.getElementById('response-area');
            const testButton = document.getElementById('test-button');
            
            if (!prompt.trim()) {
                updateStatus('请输入测试提示词', 'error');
                return;
            }

            testButton.disabled = true;
            responseArea.textContent = '正在生成响应...';
            
            try {
                // 模拟AI响应
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const mockResponse = `[${providerNames[currentStatus.currentProvider]}] 你好！我是${providerNames[currentStatus.currentProvider]}，很高兴为你服务。

我是一个AI助手，可以帮助你：
- 回答各种问题
- 协助创作和写作
- 进行对话交流
- 提供信息和建议

当前配置：
- 提供商: ${providerNames[currentStatus.currentProvider]}
- 状态: 正常运行
- 响应时间: ${Math.random() * 2000 + 500}ms

有什么我可以帮助你的吗？`;

                responseArea.textContent = mockResponse;
                updateStatus('AI响应测试完成', 'success');
                
            } catch (error) {
                responseArea.textContent = `错误: ${error.message}`;
                updateStatus('AI响应测试失败', 'error');
            } finally {
                testButton.disabled = false;
            }
        };

        window.testAllProviders = async function() {
            const prompt = document.getElementById('test-prompt').value;
            const responseArea = document.getElementById('response-area');
            const testAllButton = document.getElementById('test-all-button');
            
            if (!prompt.trim()) {
                updateStatus('请输入测试提示词', 'error');
                return;
            }

            testAllButton.disabled = true;
            responseArea.textContent = '正在测试所有可用提供商...\n\n';
            
            const availableProviders = Object.entries(currentStatus.providers)
                .filter(([_, info]) => info.available)
                .map(([provider, _]) => provider);

            for (const provider of availableProviders) {
                responseArea.textContent += `\n=== 测试 ${providerNames[provider]} ===\n`;
                responseArea.textContent += '正在生成响应...\n';
                
                try {
                    // 模拟切换和响应
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    
                    const mockResponse = `[${providerNames[provider]}] 测试响应成功！
响应时间: ${Math.random() * 2000 + 500}ms
状态: 正常`;
                    
                    responseArea.textContent += mockResponse + '\n';
                    
                } catch (error) {
                    responseArea.textContent += `错误: ${error.message}\n`;
                }
            }
            
            responseArea.textContent += '\n=== 所有提供商测试完成 ===';
            updateStatus('所有提供商测试完成', 'success');
            testAllButton.disabled = false;
        };

        // 初始化
        function initialize() {
            updateStatus('AI服务初始化完成', 'success');
            renderProviders();
        }

        // 页面加载完成后初始化
        setTimeout(initialize, 500);
    </script>
</body>
</html>
