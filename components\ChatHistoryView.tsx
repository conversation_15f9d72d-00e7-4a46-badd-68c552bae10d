
import React, { useRef, useEffect, useContext } from 'react';
import { DialogueLine } from '../types';
import { UIText } from '../constants';
import { ThemeContext } from '../contexts/ThemeContext';

interface ChatHistoryViewProps {
  history: DialogueLine[];
}

const ChatHistoryView: React.FC<ChatHistoryViewProps> = ({ history }) => {
  const historyEndRef = useRef<HTMLDivElement>(null);
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  useEffect(() => {
    historyEndRef.current?.scrollIntoView({ behavior: "auto" }); 
  }, [history]);
  
  if (!history || history.length === 0) {
    return <div className="p-4 text-secondary-themed italic text-sm">{UIText.noHistory}</div>;
  }

  const getBubbleStyle = (speakerType: DialogueLine['speakerType']) => {
    switch (speakerType) {
      case 'player':
        return 'bg-element-themed text-player-themed border-themed';
      case 'npc':
        return 'bg-element-themed text-accent-themed border-themed';
      case 'narrator':
        return 'bg-element-themed text-narrator-themed italic border-themed';
      default:
        return 'bg-element-themed text-primary-themed border-themed';
    }
  };
  
  const getSpeakerName = (line: DialogueLine) => {
    if (line.speakerType === 'player') return UIText.player(line.speakerName);
    if (line.speakerType === 'narrator') return UIText.narrator;
    return line.speakerName;
  }

  return (
    <div className="p-4 bg-secondary-themed backdrop-blur-sm rounded-xl shadow-themed-md mb-4 transition-colors duration-300">
      <h3 
        className="text-lg font-semibold text-accent-themed mb-3 sticky top-0 py-2 z-10 bg-secondary-themed backdrop-blur-sm"
      >
        {UIText.chatHistory}
      </h3>
      <div className="space-y-2 max-h-60 overflow-y-auto pr-2">
        {history.map((line) => (
          <div 
            key={line.id} 
            className={`p-2.5 rounded-lg text-sm shadow-themed-sm border ${getBubbleStyle(line.speakerType)} transition-colors duration-300`}
          >
            <span className="font-semibold">{getSpeakerName(line)}: </span>
            {line.text}
          </div>
        ))}
        <div ref={historyEndRef} />
      </div>
    </div>
  );
};

export default ChatHistoryView;
