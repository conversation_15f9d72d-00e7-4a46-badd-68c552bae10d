import React, { createContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { Theme, ThemeContextType, LocalStorageKeys } from '../types'; // Updated import
import { UIText } from '../constants';

export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

const themeOrder: Theme[] = [
  Theme.Light,
  Theme.Dark,
  Theme.Sakura,
  Theme.Starry,
  Theme.Candy,
  Theme.Forest,
];

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>(() => {
    const storedTheme = localStorage.getItem(LocalStorageKeys.THEME) as Theme | null;
    if (storedTheme && themeOrder.includes(storedTheme)) {
      return storedTheme;
    }
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return Theme.Dark;
    }
    return Theme.Light;
  });

  useEffect(() => {
    const root = window.document.documentElement;
    // Remove all theme classes
    themeOrder.forEach(t => root.classList.remove(`theme-${t}`));
    // Add current theme class
    root.classList.add(`theme-${theme}`);
    
    localStorage.setItem(LocalStorageKeys.THEME, theme);
  }, [theme]);

  const toggleTheme = useCallback(() => {
    setThemeState(prevTheme => {
      const currentIndex = themeOrder.indexOf(prevTheme);
      const nextIndex = (currentIndex + 1) % themeOrder.length;
      return themeOrder[nextIndex];
    });
  }, []);

  const setTheme = useCallback((newTheme: Theme) => {
    if (themeOrder.includes(newTheme)) {
      setThemeState(newTheme);
    }
  }, []);

  const getThemeName = useCallback((themeToName: Theme): string => {
    switch (themeToName) {
      case Theme.Light: return UIText.themeNameLight;
      case Theme.Dark: return UIText.themeNameDark;
      case Theme.Sakura: return UIText.themeNameSakura;
      case Theme.Starry: return UIText.themeNameStarry;
      case Theme.Candy: return UIText.themeNameCandy;
      case Theme.Forest: return UIText.themeNameForest;
      default: return UIText.themeToggle;
    }
  }, []);


  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme, getThemeName }}>
      {children}
    </ThemeContext.Provider>
  );
};