
// services/geminiChat.ts

import { GoogleGenAI, Chat, GenerateContentResponse, Content, Part, GenerateContentParameters } from "@google/genai";
import { GeminiResponseFormat, GameSettingsData, PlayerStatus, NotificationType } from '../types';
import { GEMINI_MODEL_TEXT_FALLBACK, UIText, InitialPlayerStatus as defaultPlayerStatus, AVAILABLE_GEMINI_MODELS } from '../constants';
import { ai } from './geminiClient';
import { getBaseSystemInstruction } from './geminiPrompts';

const API_KEY = process.env.API_KEY;

export function createChatSession(
  playerName: string,
  gameSettings: GameSettingsData,
  playerStatus: PlayerStatus,
  history: Content[] = []
): Chat {
  const systemInstruction = getBaseSystemInstruction(playerName, gameSettings, playerStatus);
  const modelId = AVAILABLE_GEMINI_MODELS.find(m => m.id === gameSettings.selectedModelId)?.id || GEMINI_MODEL_TEXT_FALLBACK;
  
  return ai.chats.create({
    model: modelId,
    history: history,
    config: { systemInstruction: systemInstruction }
  });
}

export function parseGeminiResponse(
  responseText: string,
  playerNameForSubstitution: string,
  addNotification?: (message: string, type: NotificationType, duration?: number, title?: string) => void
): GeminiResponseFormat {
    let jsonString = responseText.trim();
    let parsedJson: any = {};
    let successfullyParsedJson = false;

    // Attempt 1: Extract from ```json ... ``` code fence
    const fenceRegex = /```(?:json)?\s*\n?({[\s\S]*?}|\[[\s\S]*?\])\n?\s*```/s; 
    const fenceMatch = jsonString.match(fenceRegex);

    if (fenceMatch && fenceMatch[1]) {
        try {
            parsedJson = JSON.parse(fenceMatch[1].trim());
            successfullyParsedJson = true;
        } catch (e) {
            console.warn("Failed to parse content within JSON fence:", e, "Content:", fenceMatch[1].trim().substring(0,200)+"...");
        }
    }

    // Attempt 2: If no valid fence, find the first '{' or '[' and try to parse from there
    if (!successfullyParsedJson) {
        let startIndex = -1;
        const firstBrace = jsonString.indexOf('{');
        const firstBracket = jsonString.indexOf('[');

        if (firstBrace !== -1 && (firstBracket === -1 || firstBrace < firstBracket)) {
            startIndex = firstBrace;
        } else if (firstBracket !== -1) {
            startIndex = firstBracket;
        }

        if (startIndex !== -1) {
            const potentialJsonString = jsonString.substring(startIndex);
            try {
                parsedJson = JSON.parse(potentialJsonString);
                successfullyParsedJson = true;
            } catch (e) {
                const charArray = potentialJsonString.split('');
                let balance = 0;
                let endIndex = -1;
                let inString = false;
                let escapeNextChar = false;
                const startChar = potentialJsonString[0]; 
                const endChar = startChar === '{' ? '}' : ']';

                for (let i = 0; i < charArray.length; i++) {
                    const char = charArray[i];
                    if (escapeNextChar) { escapeNextChar = false; continue; }
                    if (char === '\\') { escapeNextChar = true; continue; }
                    if (char === '"') { inString = !inString; }
                    if (!inString) {
                        if (char === startChar) balance++;
                        else if (char === endChar) balance--;
                    }
                    if (balance === 0 && i > 0) { endIndex = i; break; }
                }

                if (endIndex !== -1) {
                    const balancedJsonString = potentialJsonString.substring(0, endIndex + 1);
                    try {
                        parsedJson = JSON.parse(balancedJsonString);
                        successfullyParsedJson = true;
                    } catch (e2) {
                        console.warn("Failed to parse balanced JSON substring:", e2, "Substring:", balancedJsonString.substring(0,200)+"...");
                    }
                }
            }
        }
    }

    if (!successfullyParsedJson) {
        const errorMsg = `AI响应格式错误。原始输出 (部分): "${responseText.substring(0, 100)}${responseText.length > 100 ? '...' : ''}"`;
        console.error("Failed to parse Gemini JSON response. Details:", errorMsg, "Full Response:", responseText);
        if (addNotification) {
            addNotification(UIText.errorStatic("AI响应格式无法解析。"), 'warning', 7000);
        }
        return {
            dialogue: responseText, 
            speakerName: UIText.narrator,
            speakerType: 'narrator',
            sceneImageKeyword: "keep_current",
            choices: [], // Return empty choices as they are now embedded in dialogue
            mood: defaultPlayerStatus.mood,
            timeOfDay: defaultPlayerStatus.timeOfDay,
            storyUpdate: `[SYSTEM_ERROR: Failed to parse AI response. Displaying raw output.]`
        };
    }

    let speakerName = parsedJson.speakerName || UIText.narrator;
    if (speakerName === "{{user}}") {
        speakerName = playerNameForSubstitution;
    }
    
    const speakerType = ['npc', 'player', 'narrator'].includes(parsedJson.speakerType) 
        ? parsedJson.speakerType 
        : (speakerName === playerNameForSubstitution ? 'player' : (speakerName === UIText.narrator ? 'narrator' : 'npc'));
    
    return {
        dialogue: parsedJson.dialogue || UIText.errorNarratorConfused,
        speakerName: speakerName,
        speakerType: speakerType,
        sceneImageKeyword: parsedJson.sceneImageKeyword || "keep_current",
        choices: [], // Choices are now embedded in dialogue; UI will extract them.
        storyUpdate: parsedJson.storyUpdate || "",
        mood: parsedJson.mood || defaultPlayerStatus.mood,
        timeOfDay: parsedJson.timeOfDay || defaultPlayerStatus.timeOfDay,
    };
}


export async function sendMessageToGemini(
  chatSession: Chat,
  message: string,
  gameSettings: GameSettingsData, 
  addNotification: (message: string, type: NotificationType, duration?: number, title?: string) => void,
  onStreamChunk?: (chunkText: string) => void
): Promise<string> { 
  if (!API_KEY) {
    addNotification(UIText.errorApiKeyMissing, 'error');
    throw new Error(UIText.errorApiKeyMissing);
  }

  const useStreaming = gameSettings.enableStreamMode && onStreamChunk;

  try {
    let aggregatedResponseText = "";

    if (useStreaming && onStreamChunk) {
      const streamResponse = await chatSession.sendMessageStream({ message });
      for await (const chunk of streamResponse) {
        const chunkText = chunk.text; 
        if (chunkText) {
          aggregatedResponseText += chunkText;
          onStreamChunk(chunkText);
        }
      }
      if (!aggregatedResponseText.trim()) {
          aggregatedResponseText = `{"dialogue": "${UIText.errorEmptyResponse}", "speakerName": "${UIText.narrator}", "speakerType": "narrator", "sceneImageKeyword": "keep_current", "mood": "${defaultPlayerStatus.mood}", "timeOfDay": "${defaultPlayerStatus.timeOfDay}"}`;
          if(addNotification) addNotification(UIText.errorEmptyResponse, "warning");
      }
    } else {
      const response: GenerateContentResponse = await chatSession.sendMessage({ message });
      aggregatedResponseText = response.text; 
      if (!aggregatedResponseText.trim()) {
           aggregatedResponseText = `{"dialogue": "${UIText.errorEmptyResponse}", "speakerName": "${UIText.narrator}", "speakerType": "narrator", "sceneImageKeyword": "keep_current", "mood": "${defaultPlayerStatus.mood}", "timeOfDay": "${defaultPlayerStatus.timeOfDay}"}`;
           if(addNotification) addNotification(UIText.errorEmptyResponse, "warning");
      }
    }
    
    return aggregatedResponseText;

  } catch (error: any) {
    if (addNotification) {
      addNotification(UIText.errorStatic(error.message || UIText.errorGeneric), 'error', 7000);
    }
    return `{"dialogue": "${UIText.errorSpiritsFuzzy}", "speakerName": "${UIText.narrator}", "speakerType": "narrator", "sceneImageKeyword": "keep_current", "mood": "${defaultPlayerStatus.mood}", "timeOfDay": "${defaultPlayerStatus.timeOfDay}"}`;
  }
}
