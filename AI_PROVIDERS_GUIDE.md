# 🤖 MemoryAble 多AI提供商使用指南

## 概述

MemoryAble现在支持多个AI提供商，让你可以在不同的AI服务之间自由切换，享受最佳的AI体验。

## 🌟 支持的AI提供商

### 1. **Google Gemini** (默认)
- **模型**: Gemini 2.5 Pro, Gemini 2.5 Flash, Gemini 1.5 Pro/Flash
- **特点**: 长上下文支持(100万+ tokens)，多模态能力
- **适用场景**: 复杂对话，长篇故事创作
- **配置**: 需要Gemini API密钥

### 2. **OpenAI GPT**
- **模型**: GPT-4o, GPT-4o Mini, GPT-4 Turbo, GPT-3.5 Turbo
- **特点**: 稳定可靠，响应质量高
- **适用场景**: 通用对话，创意写作
- **配置**: 需要OpenAI API密钥

### 3. **Anthropic Claude**
- **模型**: Claude 3.5 Sonnet, Claude 3.5 Haiku, Claude 3 Opus
- **特点**: 注重安全性，长上下文支持
- **适用场景**: 安全对话，分析任务
- **配置**: 需要Anthropic API密钥

### 4. **本地AI** (实验性)
- **模型**: 支持Ollama, LM Studio等本地部署
- **特点**: 完全私密，无网络依赖
- **适用场景**: 隐私敏感场景
- **配置**: 需要本地AI服务

## 🚀 快速开始

### 1. 配置API密钥

在`.env.local`文件中添加你的API密钥：

```bash
# Google Gemini (默认已配置)
GEMINI_API_KEY=你的Gemini密钥

# OpenAI (可选)
OPENAI_API_KEY=你的OpenAI密钥

# Anthropic Claude (可选)
ANTHROPIC_API_KEY=你的Claude密钥

# 主要提供商设置
AI_PRIMARY_PROVIDER=gemini
AI_ENABLE_FALLBACK=true
```

### 2. 在界面中切换提供商

1. 点击顶部工具栏的 **⚙️ 齿轮图标**
2. 在弹出的"AI提供商设置"窗口中：
   - 查看所有可用提供商的状态
   - 配置API密钥
   - 切换当前使用的提供商
   - 查看可用模型列表

### 3. 自动故障转移

当启用自动故障转移时，如果当前提供商出现问题，系统会自动切换到备用提供商，确保服务不中断。

## 🔧 高级配置

### 环境变量配置

```bash
# 主要AI提供商
AI_PRIMARY_PROVIDER=gemini|openai|anthropic|local

# 启用自动故障转移
AI_ENABLE_FALLBACK=true

# 本地AI配置
LOCAL_AI_ENDPOINT=http://localhost:11434
ENABLE_LOCAL_AI=false

# Azure OpenAI (企业用户)
AZURE_OPENAI_API_KEY=你的Azure密钥
AZURE_OPENAI_ENDPOINT=你的Azure端点
```

### 程序化API使用

```typescript
import { 
  switchAIProvider, 
  generateAIResponse, 
  getAvailableProviders 
} from './services/aiService';

// 切换提供商
await switchAIProvider('openai');

// 生成响应
const response = await generateAIResponse([
  { role: 'user', content: '你好' }
], {
  provider: 'anthropic',
  temperature: 0.7,
  maxTokens: 2048
});

// 获取可用提供商
const providers = await getAvailableProviders();
```

## 📊 提供商对比

| 特性 | Gemini | OpenAI | Claude | 本地AI |
|------|--------|--------|--------|--------|
| 上下文长度 | 100万+ | 12.8万 | 20万 | 取决于模型 |
| 响应速度 | 快 | 中等 | 中等 | 很快 |
| 成本 | 低 | 中等 | 中等 | 免费 |
| 隐私性 | 中等 | 中等 | 高 | 最高 |
| 稳定性 | 高 | 最高 | 高 | 取决于配置 |

## 🛠️ 故障排除

### 常见问题

**Q: 提供商显示"不可用"**
- 检查API密钥是否正确配置
- 确认网络连接正常
- 验证API配额是否充足

**Q: 切换提供商失败**
- 确保目标提供商已正确配置
- 检查API密钥的有效性
- 查看浏览器控制台的错误信息

**Q: 响应质量不佳**
- 尝试调整温度参数(0.1-1.0)
- 切换到不同的模型
- 优化提示词内容

### 调试模式

在浏览器控制台中启用调试：

```javascript
// 检查AI服务健康状态
import { checkAIServiceHealth } from './services/aiService';
const health = await checkAIServiceHealth();
console.log(health);

// 验证所有提供商
import { validateAIProviders } from './services/aiService';
const validation = await validateAIProviders();
console.log(validation);
```

## 🔮 未来计划

- **更多提供商**: Cohere, Hugging Face, 百度文心一言
- **模型微调**: 支持自定义模型参数
- **成本监控**: 实时API使用成本跟踪
- **性能优化**: 智能缓存和负载均衡
- **本地模型**: 更好的本地AI集成

## 📞 技术支持

如果遇到问题：

1. 查看 `TROUBLESHOOTING.md` 文件
2. 检查浏览器控制台错误
3. 验证API密钥配置
4. 测试网络连接

## 🎯 最佳实践

### 1. 提供商选择策略
- **日常对话**: Gemini (成本低，速度快)
- **创意写作**: OpenAI GPT-4 (质量高)
- **安全场景**: Claude (安全性好)
- **隐私场景**: 本地AI (完全私密)

### 2. 故障转移配置
```bash
AI_PRIMARY_PROVIDER=gemini
AI_FALLBACK_PROVIDERS=openai,anthropic
AI_ENABLE_FALLBACK=true
```

### 3. 成本优化
- 使用较小的模型处理简单任务
- 启用缓存减少重复请求
- 设置合理的token限制

---

**享受多AI提供商带来的强大功能！** 🚀
