const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = process.env.LOCAL_SERVER_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Storage configuration
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, 'uploads');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.json', '.jsonc', '.png', '.jpg', '.jpeg', '.txt', '.md'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${ext} not allowed`));
    }
  }
});

// Data directories
const DATA_DIR = path.join(__dirname, 'data');
const SAVES_DIR = path.join(DATA_DIR, 'saves');
const CHARACTERS_DIR = path.join(DATA_DIR, 'characters');
const WORLDBOOKS_DIR = path.join(DATA_DIR, 'worldbooks');
const REGEX_DIR = path.join(DATA_DIR, 'regex');
const IMAGES_DIR = path.join(DATA_DIR, 'images');

// Initialize directories
async function initializeDirectories() {
  const dirs = [DATA_DIR, SAVES_DIR, CHARACTERS_DIR, WORLDBOOKS_DIR, REGEX_DIR, IMAGES_DIR];
  for (const dir of dirs) {
    try {
      await fs.mkdir(dir, { recursive: true });
    } catch (error) {
      console.error(`Failed to create directory ${dir}:`, error);
    }
  }
}

// Local AI proxy endpoint (for future local AI integration)
app.post('/api/ai/generate', async (req, res) => {
  try {
    const { prompt, model, settings } = req.body;
    
    // For now, return a mock response
    // This will be replaced with actual local AI integration
    const mockResponse = {
      dialogue: "This is a mock response from the local AI server. Please configure your local AI endpoint.",
      speakerName: "System",
      speakerType: "narrator",
      sceneImageKeyword: "placeholder scene",
      choices: ["Continue", "Ask for help", "Check settings"],
      storyUpdate: "Local AI server is running but not yet configured.",
      mood: "neutral",
      timeOfDay: "unknown"
    };
    
    res.json(mockResponse);
  } catch (error) {
    console.error('AI generation error:', error);
    res.status(500).json({ error: 'AI generation failed', details: error.message });
  }
});

// Local image generation endpoint
app.post('/api/images/generate', async (req, res) => {
  try {
    const { prompt, style, settings } = req.body;
    
    // Mock image generation - return placeholder
    const placeholderImage = `data:image/svg+xml;base64,${Buffer.from(`
      <svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#2a2a2a"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="16">
          Local Image Generation
        </text>
        <text x="50%" y="60%" text-anchor="middle" dy=".3em" fill="#ccc" font-family="Arial" font-size="12">
          Prompt: ${prompt.substring(0, 30)}...
        </text>
      </svg>
    `).toString('base64')}`;
    
    res.json({ imageUrl: placeholderImage });
  } catch (error) {
    console.error('Image generation error:', error);
    res.status(500).json({ error: 'Image generation failed', details: error.message });
  }
});

// File management endpoints
app.post('/api/files/save', upload.single('file'), async (req, res) => {
  try {
    const { type, metadata } = req.body;
    const file = req.file;
    
    if (!file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    
    const fileInfo = {
      id: uuidv4(),
      originalName: file.originalname,
      filename: file.filename,
      path: file.path,
      type: type || 'unknown',
      size: file.size,
      uploadedAt: new Date().toISOString(),
      metadata: metadata ? JSON.parse(metadata) : {}
    };
    
    // Save file info to appropriate directory based on type
    let targetDir = DATA_DIR;
    switch (type) {
      case 'character': targetDir = CHARACTERS_DIR; break;
      case 'worldbook': targetDir = WORLDBOOKS_DIR; break;
      case 'regex': targetDir = REGEX_DIR; break;
      case 'save': targetDir = SAVES_DIR; break;
      default: targetDir = DATA_DIR;
    }
    
    const infoPath = path.join(targetDir, `${fileInfo.id}.json`);
    await fs.writeFile(infoPath, JSON.stringify(fileInfo, null, 2));
    
    res.json({ success: true, fileInfo });
  } catch (error) {
    console.error('File save error:', error);
    res.status(500).json({ error: 'File save failed', details: error.message });
  }
});

app.get('/api/files/list/:type', async (req, res) => {
  try {
    const { type } = req.params;
    let targetDir = DATA_DIR;
    
    switch (type) {
      case 'character': targetDir = CHARACTERS_DIR; break;
      case 'worldbook': targetDir = WORLDBOOKS_DIR; break;
      case 'regex': targetDir = REGEX_DIR; break;
      case 'save': targetDir = SAVES_DIR; break;
      default: targetDir = DATA_DIR;
    }
    
    const files = await fs.readdir(targetDir);
    const fileInfos = [];
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        try {
          const content = await fs.readFile(path.join(targetDir, file), 'utf8');
          const fileInfo = JSON.parse(content);
          fileInfos.push(fileInfo);
        } catch (error) {
          console.error(`Error reading file info ${file}:`, error);
        }
      }
    }
    
    res.json(fileInfos);
  } catch (error) {
    console.error('File list error:', error);
    res.status(500).json({ error: 'Failed to list files', details: error.message });
  }
});

// Local backup/restore endpoints
app.post('/api/backup/create', async (req, res) => {
  try {
    const { data, name } = req.body;
    const backupId = uuidv4();
    const timestamp = new Date().toISOString();
    
    const backup = {
      id: backupId,
      name: name || `Backup_${timestamp}`,
      createdAt: timestamp,
      data
    };
    
    const backupPath = path.join(DATA_DIR, 'backups');
    await fs.mkdir(backupPath, { recursive: true });
    
    const backupFile = path.join(backupPath, `${backupId}.json`);
    await fs.writeFile(backupFile, JSON.stringify(backup, null, 2));
    
    res.json({ success: true, backupId, name: backup.name });
  } catch (error) {
    console.error('Backup creation error:', error);
    res.status(500).json({ error: 'Backup creation failed', details: error.message });
  }
});

app.get('/api/backup/list', async (req, res) => {
  try {
    const backupPath = path.join(DATA_DIR, 'backups');
    
    try {
      const files = await fs.readdir(backupPath);
      const backups = [];
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          try {
            const content = await fs.readFile(path.join(backupPath, file), 'utf8');
            const backup = JSON.parse(content);
            backups.push({
              id: backup.id,
              name: backup.name,
              createdAt: backup.createdAt
            });
          } catch (error) {
            console.error(`Error reading backup ${file}:`, error);
          }
        }
      }
      
      res.json(backups);
    } catch (error) {
      // Backups directory doesn't exist yet
      res.json([]);
    }
  } catch (error) {
    console.error('Backup list error:', error);
    res.status(500).json({ error: 'Failed to list backups', details: error.message });
  }
});

app.get('/api/backup/restore/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const backupPath = path.join(DATA_DIR, 'backups', `${id}.json`);
    
    const content = await fs.readFile(backupPath, 'utf8');
    const backup = JSON.parse(content);
    
    res.json({ success: true, data: backup.data });
  } catch (error) {
    console.error('Backup restore error:', error);
    res.status(500).json({ error: 'Backup restore failed', details: error.message });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      fileStorage: 'active',
      backup: 'active',
      ai: 'mock', // Will be 'active' when local AI is configured
      imageGeneration: 'mock' // Will be 'active' when local image gen is configured
    }
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({ 
    error: 'Internal server error', 
    details: error.message 
  });
});

// Start server
async function startServer() {
  await initializeDirectories();
  
  app.listen(PORT, () => {
    console.log(`Local MemoryAble server running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/api/health`);
    console.log('Data directory:', DATA_DIR);
  });
}

startServer().catch(console.error);
