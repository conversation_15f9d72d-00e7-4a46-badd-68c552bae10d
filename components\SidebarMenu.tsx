


import React, { useContext, useEffect, useRef, useCallback } from 'react';
import { GameSaveData, GameSettingsData, SettingPreset, PresetType, CustomNarrativePrimaryElement, NotificationType, UserPreferences, PlayerStatus, CoreAttributes, Skill, CharacterCardData } from '../types';
import { Icons, AppTitleStyled, UIText } from '../constants';
import SaveLoadManager from './SaveLoadManager';
import SettingsMenu from './SettingsMenu'; // Corrected: No curly braces for default import
import AchievementsPanel from './AchievementsPanel';
import { ThemeContext } from '../contexts/ThemeContext';

export type ActiveTab = 'settings' | 'saveload' | 'achievements'; 

interface SidebarMenuProps {
  isOpen: boolean;
  toggleSidebar: () => void;
  gameSaves: GameSaveData[];
  onSaveGame: (name: string) => Promise<string | null>;
  onLoadGame: (saveId: string) => void;
  onDeleteSave: (saveId: string) => void;
  onRenameSave: (saveId: string, newName: string) => void;
  onUpdateSave: (saveId: string) => void;
  gameSettings: GameSettingsData;
  userPreferences: UserPreferences;
  playerStatus: PlayerStatus; 
  onGameSettingsChange: (newSettings: Partial<GameSettingsData> | ((prevState: GameSettingsData) => GameSettingsData)) => void; 
  onUserPreferencesChange: (newPreferences: UserPreferences) => void;
  onPersistGameSettings: () => void;
  onPersistUserPreferences: () => void;
  onRestartFromSave?: (saveId: string) => void;
  onRestartWithSummary?: () => void;
  onDefaultRestartGameRequest?: () => void; 
  characterCardPresets: SettingPreset[]; 
  userRolePresets: SettingPreset[];
  aiStylePresets: SettingPreset[];
  onSavePreset: (type: PresetType, value: string | CustomNarrativePrimaryElement[] | CharacterCardData, name: string) => void; 
  onLoadPreset: (type: PresetType, presetId: string) => void;
  onDeletePreset: (type: PresetType, presetId: string) => void;
  addNotification: (message: string, type: NotificationType, duration?: number, title?: string) => void;
  onRequestConfirmation: (title: string, message: string, onConfirm: () => void, confirmText?: string, cancelText?: string) => void;
  onExportData: () => void;
  onImportData: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onResetAllSettingsToDefaults: () => void; 
  onAllocateAttributePoint: (attributeKey: keyof CoreAttributes) => void; 
  onAllocateSkillPoint: (skillId: string) => void; 
  activeTab: ActiveTab; 
  onTabChange: (tab: ActiveTab) => void; 
  // Gist Props
  onInitializeGistBackup?: () => void;
  onBackupToGist?: () => void;
  onRestoreFromGist?: () => void;
  isGistLoading?: boolean;
}

const SidebarMenu: React.FC<SidebarMenuProps> = React.memo(({
  isOpen, toggleSidebar,
  gameSaves, onSaveGame, onLoadGame, onDeleteSave, onRenameSave, onUpdateSave,
  gameSettings, userPreferences, playerStatus,
  onGameSettingsChange, onUserPreferencesChange,
  onPersistGameSettings, onPersistUserPreferences,
  onRestartFromSave, onRestartWithSummary, onDefaultRestartGameRequest,
  characterCardPresets, 
  userRolePresets, aiStylePresets,
  onSavePreset, onLoadPreset, onDeletePreset,
  addNotification, onRequestConfirmation,
  onExportData, onImportData, onResetAllSettingsToDefaults, 
  onAllocateAttributePoint, onAllocateSkillPoint,
  activeTab, onTabChange, 
  onInitializeGistBackup, onBackupToGist, onRestoreFromGist, isGistLoading,
}) => {
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  const sidebarRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && sidebarRef.current) sidebarRef.current.focus();
  }, [isOpen]);

  const persistAllSettings = useCallback(() => {
    onPersistGameSettings();
    onPersistUserPreferences();
  }, [onPersistGameSettings, onPersistUserPreferences]);

  const handleSaveChangesAndCloseFromButton = () => {
    persistAllSettings();
    toggleSidebar(); // Close sidebar
  };

  const handleBackdropClick = () => {
    persistAllSettings();
    toggleSidebar();
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpen && sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        const targetElement = event.target as HTMLElement;
        const menuButton = document.querySelector('header button[title="菜单"]'); 
        if (menuButton && menuButton.contains(targetElement)) {
          return; 
        }
        persistAllSettings();
        toggleSidebar();
      }
    };

    if (isOpen) document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, toggleSidebar, persistAllSettings, sidebarRef]);

  const getUnspentPoints = () => (playerStatus.attributePoints || 0) + (playerStatus.skillPoints || 0);

  const menuItems: Array<{ id: ActiveTab; label: string; icon: React.FC<React.SVGProps<SVGSVGElement>>; notificationCount?: number, iconClassName?: string }> = [
    { id: 'settings', label: UIText.settings, icon: Icons.AdjustmentsHorizontal },
    { id: 'saveload', label: UIText.saveLoad, icon: Icons.Save },
    { id: 'achievements', label: UIText.achievementsTitle, icon: Icons.Trophy, notificationCount: getUnspentPoints(), iconClassName: getUnspentPoints() > 0 ? "text-yellow-400 animate-pulse" : "" },
  ];

  const sidebarBaseClasses = "fixed top-0 left-0 h-full bg-secondary-themed shadow-themed-xl z-40 transition-transform transform w-72 md:w-80 lg:w-96 flex flex-col border-r border-themed transition-colors duration-300";
  const sidebarBlurClass = gameSettings.enableBackdropBlur ? "backdrop-blur-md" : "";
  const sidebarTransformClass = isOpen ? "translate-x-0" : "-translate-x-full";

  return (
    <>
      {isOpen && (<div className="fixed inset-0 bg-black bg-opacity-30 z-30 lg:hidden backdrop-blur-sm" onClick={handleBackdropClick} aria-hidden="true"></div>)}
      <div ref={sidebarRef} tabIndex={-1} className={`${sidebarBaseClasses} ${sidebarBlurClass} ${sidebarTransformClass}`} role="dialog" aria-modal="true" aria-labelledby="sidebar-title" style={{overscrollBehaviorY: 'contain'}}>
        <div className="flex justify-between items-center mb-3 p-3 md:p-4 border-b border-themed flex-shrink-0">
          <h2 id="sidebar-title" className="text-2xl font-bold text-accent-themed"><AppTitleStyled /></h2>
        </div>

        <div className="flex justify-around items-center mb-2 px-2 py-1 border-b border-themed flex-shrink-0">
          {menuItems.map(item => (
            <button key={item.id} onClick={() => onTabChange(item.id)} className={`flex-1 p-2 rounded-md flex flex-col items-center justify-center text-xs transition-colors duration-200 ${activeTab === item.id ? 'bg-accent-themed/20 text-accent-themed font-semibold border-l-4 border-[var(--accent-color)]' : 'text-secondary-themed hover:bg-element-themed/50 hover:text-primary-themed border-l-4 border-transparent'}`} aria-current={activeTab === item.id ? "page" : undefined} title={item.label}>
              <div className="relative">
                <item.icon className={`w-5 h-5 mb-0.5 ${item.iconClassName || ''}`} />
                {typeof item.notificationCount === 'number' && item.notificationCount > 0 && (<span className="absolute -top-1 -right-1.5 bg-red-500 text-white text-[0.6rem] font-bold rounded-full h-3.5 w-3.5 flex items-center justify-center">{item.notificationCount > 9 ? '9+' : item.notificationCount}</span>)}
              </div>
              <span className="truncate max-w-[60px]">{item.label}</span>
            </button>
          ))}
        </div>

        <div className="flex-grow overflow-y-auto p-3 md:p-4" style={{overscrollBehaviorY: 'contain'}}>
          {activeTab === 'settings' && ( 
            <SettingsMenu
              gameSettings={gameSettings} userPreferences={userPreferences}
              onSettingsChange={onGameSettingsChange} onUserPreferencesChange={onUserPreferencesChange}
              characterCardPresets={characterCardPresets} 
              userRolePresets={userRolePresets} aiStylePresets={aiStylePresets} 
              onSavePreset={onSavePreset} onLoadPreset={onLoadPreset} onDeletePreset={onDeletePreset}
              onRequestConfirmation={onRequestConfirmation} enableBackdropBlur={gameSettings.enableBackdropBlur}
              // Gist props no longer passed to SettingsMenu
            />
          )}
          {activeTab === 'saveload' && ( 
            <SaveLoadManager
              saves={gameSaves}
              onSave={onSaveGame} onLoad={onLoadGame} onDelete={onDeleteSave} onRename={onRenameSave}
              onUpdateSave={onUpdateSave} 
              onRestartFromSave={onRestartFromSave}
              onRestartWithSummary={onRestartWithSummary} 
              onDefaultRestartGameRequest={onDefaultRestartGameRequest}
              addNotification={addNotification} onRequestConfirmation={onRequestConfirmation}
              enableBackdropBlur={gameSettings.enableBackdropBlur}
              onExportData={onExportData} 
              onImportData={onImportData} 
              onResetAllSettingsToDefaults={onResetAllSettingsToDefaults}
              gameSettings={gameSettings}
              onGameSettingsChange={onGameSettingsChange} // Pass this for Gist settings
              // GDrive removed
              onInitializeGistBackup={onInitializeGistBackup}
              onBackupToGist={onBackupToGist}
              onRestoreFromGist={onRestoreFromGist}
              isGistLoading={isGistLoading}
            />
          )}
          {activeTab === 'achievements' && ( 
            <AchievementsPanel userPreferences={userPreferences} enableBackdropBlur={gameSettings.enableBackdropBlur} />
          )}
        </div>

        <div className="p-3 md:p-4 border-t border-themed flex-shrink-0">
           <button onClick={handleSaveChangesAndCloseFromButton} className="w-full btn-dreamy flex items-center justify-center">
              <Icons.Save className="w-5 h-5 mr-2" /> {UIText.saveSettingsButton}
            </button>
            <footer className="mt-3 text-center text-xs text-secondary-themed"><p>{UIText.gameCopyright}</p><p>{UIText.poweredByGemini}</p></footer>
        </div>
      </div>
    </>
  );
});

export default SidebarMenu;
