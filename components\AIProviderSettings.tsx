import React, { useState, useEffect } from 'react';
import { 
  getAvailableProviders, 
  switchAIProvider, 
  validateAIProviders,
  updateAIProviderConfig,
  getCurrentAIProvider,
  checkAIServiceHealth
} from '../services/aiService';
import { AIProviderType } from '../services/aiProviders/types';

interface AIProviderSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ProviderInfo {
  available: boolean;
  current: boolean;
  models: any[];
  status: 'ok' | 'error';
  error?: string;
}

const providerNames: Record<AIProviderType, string> = {
  gemini: 'Google Gemini',
  openai: 'OpenAI GPT',
  anthropic: 'Anthropic Claude',
  local: '本地AI',
  'azure-openai': 'Azure OpenAI',
  cohere: 'Cohere',
  huggingface: 'Hugging Face'
};

const providerDescriptions: Record<AIProviderType, string> = {
  gemini: '谷歌最新的多模态AI模型，支持长上下文',
  openai: 'OpenAI的GPT系列模型，功能强大且稳定',
  anthropic: 'Anthropic的Claude模型，注重安全性和有用性',
  local: '本地部署的AI模型，完全私密',
  'azure-openai': '微软Azure平台上的OpenAI服务',
  cohere: 'Cohere的语言模型',
  huggingface: 'Hugging Face平台的开源模型'
};

export function AIProviderSettings({ isOpen, onClose }: AIProviderSettingsProps) {
  const [providers, setProviders] = useState<Record<AIProviderType, ProviderInfo>>({} as any);
  const [currentProvider, setCurrentProvider] = useState<AIProviderType | null>(null);
  const [loading, setLoading] = useState(false);
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({
    gemini: '',
    openai: '',
    anthropic: '',
    azure: ''
  });

  useEffect(() => {
    if (isOpen) {
      loadProviderData();
      checkHealth();
    }
  }, [isOpen]);

  const loadProviderData = async () => {
    setLoading(true);
    try {
      const [providerData, validation] = await Promise.all([
        getAvailableProviders(),
        validateAIProviders()
      ]);

      const combinedProviders: Record<string, ProviderInfo> = {};
      
      for (const [provider, info] of Object.entries(providerData.providers)) {
        combinedProviders[provider] = {
          ...info,
          models: providerData.models[provider as AIProviderType] || [],
          status: validation[provider as AIProviderType] ? 'ok' : 'error',
          error: validation[provider as AIProviderType] ? undefined : '配置无效或API密钥错误'
        };
      }

      setProviders(combinedProviders as Record<AIProviderType, ProviderInfo>);
      setCurrentProvider(getCurrentAIProvider());
    } catch (error) {
      console.error('Failed to load provider data:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkHealth = async () => {
    try {
      const health = await checkAIServiceHealth();
      setHealthStatus(health);
    } catch (error) {
      console.error('Health check failed:', error);
    }
  };

  const handleProviderSwitch = async (provider: AIProviderType) => {
    setLoading(true);
    try {
      const success = await switchAIProvider(provider);
      if (success) {
        setCurrentProvider(provider);
        await loadProviderData();
      } else {
        alert(`切换到 ${providerNames[provider]} 失败`);
      }
    } catch (error) {
      console.error('Provider switch failed:', error);
      alert(`切换失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleApiKeyUpdate = async (provider: AIProviderType, apiKey: string) => {
    try {
      updateAIProviderConfig(provider, { apiKey });
      setApiKeys(prev => ({ ...prev, [provider]: apiKey }));
      await loadProviderData();
    } catch (error) {
      console.error('API key update failed:', error);
      alert(`API密钥更新失败: ${error.message}`);
    }
  };

  const getStatusColor = (status: 'ok' | 'error') => {
    return status === 'ok' ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (status: 'ok' | 'error') => {
    return status === 'ok' ? '✅' : '❌';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">AI提供商设置</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-2xl"
            >
              ×
            </button>
          </div>

          {/* Health Status */}
          {healthStatus && (
            <div className="mb-6 p-4 rounded-lg bg-gray-50">
              <h3 className="text-lg font-semibold mb-2">系统状态</h3>
              <div className="flex items-center space-x-4">
                <span className={`font-medium ${
                  healthStatus.status === 'healthy' ? 'text-green-600' :
                  healthStatus.status === 'degraded' ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {healthStatus.status === 'healthy' ? '🟢 健康' :
                   healthStatus.status === 'degraded' ? '🟡 部分可用' : '🔴 不可用'}
                </span>
                <span className="text-sm text-gray-600">
                  当前提供商: {currentProvider ? providerNames[currentProvider] : '未知'}
                </span>
              </div>
            </div>
          )}

          {/* Provider List */}
          <div className="space-y-4">
            {Object.entries(providers).map(([provider, info]) => (
              <div
                key={provider}
                className={`border rounded-lg p-4 ${
                  info.current ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-semibold">
                      {providerNames[provider as AIProviderType]}
                    </h3>
                    <span className={`text-sm ${getStatusColor(info.status)}`}>
                      {getStatusIcon(info.status)} {info.status === 'ok' ? '可用' : '不可用'}
                    </span>
                    {info.current && (
                      <span className="bg-blue-500 text-white px-2 py-1 rounded text-xs">
                        当前使用
                      </span>
                    )}
                  </div>
                  <button
                    onClick={() => handleProviderSwitch(provider as AIProviderType)}
                    disabled={loading || info.status === 'error'}
                    className={`px-4 py-2 rounded ${
                      info.current
                        ? 'bg-gray-300 text-gray-600 cursor-not-allowed'
                        : info.status === 'ok'
                        ? 'bg-blue-500 text-white hover:bg-blue-600'
                        : 'bg-gray-300 text-gray-600 cursor-not-allowed'
                    }`}
                  >
                    {info.current ? '当前使用' : '切换'}
                  </button>
                </div>

                <p className="text-sm text-gray-600 mb-3">
                  {providerDescriptions[provider as AIProviderType]}
                </p>

                {info.error && (
                  <div className="text-sm text-red-600 mb-3">
                    错误: {info.error}
                  </div>
                )}

                {/* API Key Input */}
                {provider !== 'local' && (
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      API密钥
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="password"
                        value={apiKeys[provider] || ''}
                        onChange={(e) => setApiKeys(prev => ({ ...prev, [provider]: e.target.value }))}
                        placeholder={`输入${providerNames[provider as AIProviderType]}的API密钥`}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <button
                        onClick={() => handleApiKeyUpdate(provider as AIProviderType, apiKeys[provider] || '')}
                        className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                      >
                        保存
                      </button>
                    </div>
                  </div>
                )}

                {/* Available Models */}
                {info.models.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 mb-2">可用模型</h4>
                    <div className="flex flex-wrap gap-2">
                      {info.models.slice(0, 5).map((model) => (
                        <span
                          key={model.id}
                          className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
                        >
                          {model.name || model.id}
                        </span>
                      ))}
                      {info.models.length > 5 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                          +{info.models.length - 5} 更多
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Actions */}
          <div className="mt-6 flex justify-between">
            <button
              onClick={checkHealth}
              disabled={loading}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50"
            >
              {loading ? '检查中...' : '重新检查'}
            </button>
            <button
              onClick={onClose}
              className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              完成
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
