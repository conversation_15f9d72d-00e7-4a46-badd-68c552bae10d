
import React, { useContext } from 'react';
import { InventoryItem } from '../types';
import { Icons, UIText } from '../constants';
import { ThemeContext } from '../contexts/ThemeContext';

interface InventoryModalProps {
  isOpen: boolean;
  items: InventoryItem[];
  onClose: () => void;
  enableBackdropBlur?: boolean;
}

const InventoryModal: React.FC<InventoryModalProps> = ({ isOpen, items, onClose, enableBackdropBlur = true }) => {
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  if (!isOpen) return null;

  const mainBgClasses = "fixed inset-0 bg-black/60 flex items-center justify-center z-[70] p-4";
  const mainBgBlurClass = enableBackdropBlur ? "backdrop-blur-sm" : "";
  const contentAreaId = "inventory-modal-content";

  return (
    <div
      className={`${mainBgClasses} ${mainBgBlurClass}`}
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="inventory-modal-title"
      aria-describedby={contentAreaId}
    >
      <div
        className="bg-secondary-themed p-5 md:p-6 rounded-xl shadow-themed-xl w-full max-w-lg border border-themed flex flex-col max-h-[80vh]"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4 flex-shrink-0">
          <h3 id="inventory-modal-title" className="text-lg font-semibold text-accent-themed flex items-center">
            <Icons.Bag className="w-5 h-5 mr-2" />
            {UIText.inventory}
          </h3>
          <button
            onClick={onClose}
            className="p-1 text-primary-themed hover:text-accent-themed rounded-md hover:bg-element-themed/50 transition-colors"
            aria-label={UIText.closeModal}
          >
            <Icons.Close className="w-5 h-5" />
          </button>
        </div>
        
        <div id={contentAreaId} className="text-primary-themed text-sm overflow-y-auto flex-grow pr-1">
          {items.length === 0 ? (
            <p className="italic text-secondary-themed text-center py-4">{UIText.noItems}</p>
          ) : (
            <ul className="space-y-3">
              {items.map(item => (
                <li key={item.id} className="p-2.5 bg-element-themed/50 rounded-md border border-themed/30 shadow-sm">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-primary-themed">{item.name}</span>
                    <span className="text-xs text-accent-themed bg-accent-themed/10 px-1.5 py-0.5 rounded-full">x{item.quantity}</span>
                  </div>
                  <p className="text-xs text-secondary-themed mt-1">{item.description}</p>
                </li>
              ))}
            </ul>
          )}
        </div>
         <div className="mt-6 flex justify-end flex-shrink-0">
            <button
              onClick={onClose}
              className="btn-dreamy btn-dreamy-sm"
            >
              {UIText.closeModal}
            </button>
        </div>
      </div>
    </div>
  );
};

export default InventoryModal;
