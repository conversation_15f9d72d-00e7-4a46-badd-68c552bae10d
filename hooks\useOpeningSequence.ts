
import { useState, useEffect, useCallback } from 'react';
import { OpeningLineStyle, LocalStorageKeys } from '../types';
import { UIText } from '../constants';
import { fetchUniqueOpeningLine } from '../services/geminiService';

export const useOpeningSequence = () => {
  const [dynamicOpeningLine, setDynamicOpeningLine] = useState<string>(UIText.dynamicOpeningLineLoading);
  const [openingLineHistory, setOpeningLineHistory] = useState<string[]>(() => {
    const saved = localStorage.getItem(LocalStorageKeys.OPENING_LINE_HISTORY);
    return saved ? JSON.parse(saved) : [];
  });
  const [lastOpeningStyle, setLastOpeningStyle] = useState<OpeningLineStyle | null>(() => {
    return localStorage.getItem(LocalStorageKeys.LAST_OPENING_STYLE) as OpeningLineStyle | null;
  });

  useEffect(() => {
    localStorage.setItem(LocalStorageKeys.OPENING_LINE_HISTORY, JSON.stringify(openingLineHistory));
  }, [openingLineHistory]);

  useEffect(() => {
    if (lastOpeningStyle) localStorage.setItem(LocalStorageKeys.LAST_OPENING_STYLE, lastOpeningStyle);
    else localStorage.removeItem(LocalStorageKeys.LAST_OPENING_STYLE);
  }, [lastOpeningStyle]);

  const fetchAndSetOpeningLine = useCallback(async () => {
    setDynamicOpeningLine(UIText.dynamicOpeningLineLoading);
    const newStyle = lastOpeningStyle === 'comedy' ? 'horror' : 'comedy';
    const line = await fetchUniqueOpeningLine(newStyle, openingLineHistory);
    setDynamicOpeningLine(line);
    if (line !== UIText.dynamicOpeningLineFallback && !openingLineHistory.includes(line)) {
      setOpeningLineHistory(prev => [line, ...prev].slice(0, 20));
    }
    setLastOpeningStyle(newStyle);
  }, [openingLineHistory, lastOpeningStyle]);

  return {
    dynamicOpeningLine,
    openingLineHistory,
    setOpeningLineHistory, // Expose for data import
    lastOpeningStyle,
    setLastOpeningStyle,   // Expose for data import
    fetchAndSetOpeningLine,
  };
};
