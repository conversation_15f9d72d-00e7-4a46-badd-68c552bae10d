
// src/rpgSystem/utils/rpgUtils.ts

import { CoreAttributes_RPG, PlayerStatus_RPG, RPGNotificationDetails, Skill_RPG, StatusEffect_RPG, RPGEffectAddParams_RPG, Quest_RPG, RPGProcessingResult, InventoryItem_RPG, VisitedLocation_RPG, CharacterProfile_RPG, ImportantEvent_RPG } from '../types_rpg'; // Added RPGEffectAddParams_RPG
import { defaultCoreAttributes_RPG, LEVEL_UP_BASE_XP_RPG, LEVEL_UP_XP_FACTOR_RPG, LEVEL_UP_ATTRIBUTE_POINTS_AWARDED_RPG, LEVEL_UP_SKILL_POINTS_AWARDED_RPG, defaultSkills_RPG } from '../constants_rpg';
import { UIText, AVAILABLE_ACHIEVEMENTS } from '@/constants'; 

export const getAttributeDisplayNameRPG = (attrKey: keyof CoreAttributes_RPG): string => {
    switch (attrKey) {
        case 'strength': return UIText.strengthShort;
        case 'agility': return UIText.agilityShort;
        case 'intelligence': return UIText.intelligenceShort;
        case 'charisma': return UIText.charismaShort;
        case 'luck': return UIText.luckShort;
        case 'sanity': return UIText.sanityShort;
        default:
            const _exhaustiveCheck: never = attrKey; // Ensures all keys are handled
            return String(_exhaustiveCheck).charAt(0).toUpperCase() + String(_exhaustiveCheck).slice(1);
    }
};

export const TIME_OF_DAY_ORDER_RPG: string[] = ["凌晨", "早晨", "上午", "中午", "下午", "黄昏", "深夜"];


export const calculateXpAndLevelChangesRPG = (
    currentXp: number,
    currentLevel: number,
    currentXpToNextLevel: number,
    currentAttributePoints: number,
    currentSkillPoints: number,
    amount: number
): { statusChangesDelta: Partial<PlayerStatus_RPG>, notifications: RPGNotificationDetails[], achievementsToUnlock: string[] } => {
    if (amount <= 0) return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };

    let newXp = currentXp + amount;
    let newLevel = currentLevel;
    let newXpToNextLevel = currentXpToNextLevel;
    let newAttributePoints = currentAttributePoints;
    let newSkillPoints = currentSkillPoints;
    const notifications: RPGNotificationDetails[] = [];
    const achievementsToUnlockInternal: string[] = [];

    notifications.push({ message: UIText.xpGained(amount), type: 'info', duration: 3000, displayToUser: true });

    while (newXp >= newXpToNextLevel) {
        newLevel++;
        newXp -= newXpToNextLevel;
        newXpToNextLevel = Math.floor(LEVEL_UP_BASE_XP_RPG * Math.pow(LEVEL_UP_XP_FACTOR_RPG, newLevel - 1));

        const awardedAttrPoints = LEVEL_UP_ATTRIBUTE_POINTS_AWARDED_RPG;
        const awardedSkillPoints = LEVEL_UP_SKILL_POINTS_AWARDED_RPG;

        newAttributePoints += awardedAttrPoints;
        newSkillPoints += awardedSkillPoints;

        notifications.push({ message: UIText.levelUpNotification(newLevel), type: 'achievement', duration: 6000, title: UIText.achievementUnlockedTitle, displayToUser: true });
        if (awardedAttrPoints > 0) {
            notifications.push({ message: `等级提升奖励: 获得 ${awardedAttrPoints} 属性点！`, type: 'success', duration: 4000, displayToUser: true });
        }
        if (awardedSkillPoints > 0) {
            notifications.push({ message: `等级提升奖励: 获得 ${awardedSkillPoints} 技能点！`, type: 'success', duration: 4000, displayToUser: true });
        }
        achievementsToUnlockInternal.push('level_up_first');
        if (newLevel >= 5) achievementsToUnlockInternal.push('level_five');
        if (newLevel >= 10) achievementsToUnlockInternal.push('level_ten_hero');
    }

    return {
        statusChangesDelta: {
            xp: newXp,
            level: newLevel,
            xpToNextLevel: newXpToNextLevel,
            attributePoints: newAttributePoints,
            skillPoints: newSkillPoints,
        },
        notifications,
        achievementsToUnlock: achievementsToUnlockInternal
    };
};

interface RPGTagProcessor_RPG {
    regex: RegExp;
    handler: (match: RegExpExecArray, currentStatusRPG: PlayerStatus_RPG) => RPGProcessingResult;
}


export const rpgTagProcessors_RPG: RPGTagProcessor_RPG[] = [
    {
        regex: /\[RPG:attribute_change:(?<attrKey>\w+),(?<changeValue>[+-]\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentStatusRPG) => {
            const attrKey = match.groups?.attrKey?.toLowerCase() as keyof CoreAttributes_RPG | undefined;
            const changeValue = parseInt(match.groups?.changeValue || '0', 10);
            const reason = match.groups?.reason || "未知原因";

            if (!attrKey || !Object.keys(defaultCoreAttributes_RPG).includes(attrKey) || isNaN(changeValue)) {
                console.warn("Malformed RPG:attribute_change tag:", match[0]);
                return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };
            }
            const currentAttributes = { ...(currentStatusRPG.coreAttributes || defaultCoreAttributes_RPG) };
            const oldValue = currentAttributes[attrKey] || 0;
            const newValue = Math.max(0, oldValue + changeValue);
            const achievementsToUnlock: string[] = [];

            if (newValue > oldValue) {
                if (attrKey === 'strength' && newValue >= 10 && oldValue < 10) achievementsToUnlock.push('strength_manifested');
                else if (attrKey === 'agility' && newValue >= 10 && oldValue < 10) achievementsToUnlock.push('agile_as_wind');
                else if (attrKey === 'intelligence' && newValue >= 10 && oldValue < 10) achievementsToUnlock.push('light_of_intellect');
                else if (attrKey === 'charisma' && newValue >= 10 && oldValue < 10) achievementsToUnlock.push('charm_of_words');
            }
             if (attrKey === 'sanity' && newValue <= 0 && oldValue > 0) achievementsToUnlock.push('sanity_broken');

            return {
                statusChangesDelta: { coreAttributes: { ...currentAttributes, [attrKey]: newValue } },
                notifications: [{ message: `${getAttributeDisplayNameRPG(attrKey)} ${changeValue > 0 ? '提升' : '降低'}至 ${newValue} (原因: ${reason})`, type: changeValue > 0 ? 'success' : 'warning', displayToUser: true }],
                achievementsToUnlock
            };
        },
    },
    { 
        regex: /\[RPG:skill_update:(?<skillId>[\w_]+),level:(?<newLevel>\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentStatusRPG) => {
            const skillId = match.groups?.skillId;
            const newLevelFromTag = parseInt(match.groups?.newLevel || '0', 10);
            const reason = match.groups?.reason || "技能提升";
            const notifications: RPGNotificationDetails[] = [];
            const achievementsToUnlock: string[] = [];

            if (!skillId || isNaN(newLevelFromTag)) {
                console.warn("Malformed RPG:skill_update tag:", match[0]);
                return { statusChangesDelta: {}, notifications, achievementsToUnlock };
            }

            const currentSkills = [...(currentStatusRPG.skills || []).map(s => ({ ...s }))];
            const skillIndex = currentSkills.findIndex(s => s.id === skillId);
            const skillDefinition = defaultSkills_RPG.find(s => s.id === skillId);

            if (skillIndex > -1) { 
                if (newLevelFromTag > currentSkills[skillIndex].level) {
                    currentSkills[skillIndex].level = newLevelFromTag;
                    currentSkills[skillIndex].currentXp = 0; 
                    notifications.push({ message: UIText.skillLeveledUp(currentSkills[skillIndex].name, newLevelFromTag) + ` (${reason})`, type: 'success', displayToUser: true });
                    achievementsToUnlock.push('skill_master_initiate');
                     if (currentSkills.filter(s => s.level >= 5).length >= 3) achievementsToUnlock.push('three_arts_mastery');
                }
            } else if (skillDefinition && newLevelFromTag > 0) { 
                currentSkills.push({ ...skillDefinition, level: newLevelFromTag, currentXp: 0 });
                notifications.push({ message: UIText.skillLearned(skillDefinition.name) + ` (初始等级 ${newLevelFromTag}, ${reason})`, type: 'success', displayToUser: true });
                achievementsToUnlock.push('skill_master_initiate');
            } else {
                console.warn("RPG:skill_update tag for unknown or invalid skill:", match[0]);
                return { statusChangesDelta: {}, notifications, achievementsToUnlock };
            }
            return { statusChangesDelta: { skills: currentSkills }, notifications, achievementsToUnlock };
        },
    },
     { 
        regex: /\[RPG:skill_xp_gain:(?<skillId>[\w_]+),xp:(?<xpAmount>\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentStatusRPG) => {
            const skillId = match.groups?.skillId;
            const xpAmount = parseInt(match.groups?.xpAmount || '0', 10);
            const reason = match.groups?.reason || "技能运用";
            const notifications: RPGNotificationDetails[] = [];
            const achievementsToUnlock: string[] = [];

            if (!skillId || isNaN(xpAmount) || xpAmount <= 0) {
                console.warn("Malformed RPG:skill_xp_gain tag:", match[0]);
                return { statusChangesDelta: {}, notifications, achievementsToUnlock };
            }

            let currentSkills = [...(currentStatusRPG.skills || []).map(s => ({ ...s }))];
            const skillIndex = currentSkills.findIndex(s => s.id === skillId);
            const skillDefinition = defaultSkills_RPG.find(s => s.id === skillId);

            if (skillIndex > -1) { 
                let skill = currentSkills[skillIndex];
                skill.currentXp = (skill.currentXp || 0) + xpAmount;
                notifications.push({ message: `${skill.name} 获得 ${xpAmount} 技能经验 (${reason})。`, type: 'info', displayToUser: true });
                while (skill.currentXp >= (skill.xpToNextLevel ?? 50)) {
                    skill.level++;
                    skill.currentXp -= (skill.xpToNextLevel ?? 50); 
                    skill.xpToNextLevel = Math.floor((skill.xpToNextLevel ?? 50) * 1.3); 
                    notifications.push({ message: UIText.skillLeveledUp(skill.name, skill.level), type: 'success', title: "技能提升！", displayToUser: true });
                    achievementsToUnlock.push('skill_master_initiate');
                    if (currentSkills.filter(s => s.level >= 5).length >= 3) achievementsToUnlock.push('three_arts_mastery');
                }
                currentSkills[skillIndex] = skill;
            } else if (skillDefinition) { 
                 let newSkill: Skill_RPG = { ...skillDefinition, level: 0, currentXp: xpAmount, xpToNextLevel: (skillDefinition.xpToNextLevel ?? 50) }; 
                 notifications.push({ message: `${newSkill.name} 获得 ${xpAmount} 技能经验 (${reason})。`, type: 'info', displayToUser: true });
                 while (newSkill.currentXp >= (newSkill.xpToNextLevel ?? 50) && newSkill.level < 1) { 
                    newSkill.level++;
                    newSkill.currentXp -= (newSkill.xpToNextLevel ?? 50); 
                    newSkill.xpToNextLevel = Math.floor((newSkill.xpToNextLevel ?? 50) * 1.3);
                    notifications.push({ message: UIText.skillLearned(newSkill.name) + ` (初始等级 ${newSkill.level})`, type: 'success', title: "习得技能！", displayToUser: true });
                    achievementsToUnlock.push('skill_master_initiate');
                 }
                 if (newSkill.level > 0) currentSkills.push(newSkill); 
            } else {
                console.warn("RPG:skill_xp_gain tag for unknown skill:", match[0]);
                return { statusChangesDelta: {}, notifications, achievementsToUnlock };
            }
            return { statusChangesDelta: { skills: currentSkills }, notifications, achievementsToUnlock };
        },
    },
    {
        regex: /\[RPG:attribute_points_award:(?<amount>\d+),reason:(?<reason>[^\]]+)\]/gi,
        handler: (match, currentStatusRPG) => {
            const amount = parseInt(match.groups?.amount || '0', 10);
            const reason = match.groups?.reason || "剧情奖励";
            if (isNaN(amount) || amount <= 0) {
                console.warn("Malformed RPG:attribute_points_award tag (invalid amount):", match[0]);
                return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };
            }
            return {
                statusChangesDelta: { attributePoints: (currentStatusRPG.attributePoints || 0) + amount },
                notifications: [{ message: `获得 ${amount} 属性点! (原因: ${reason})`, type: 'success', displayToUser: true }],
                achievementsToUnlock: []
            };
        },
    },
    {
        regex: /\[RPG:skill_points_award:(?<amount>\d+),reason:(?<reason>[^\]]+)\]/gi,
        handler: (match, currentStatusRPG) => {
            const amount = parseInt(match.groups?.amount || '0', 10);
            const reason = match.groups?.reason || "剧情奖励";
            if (isNaN(amount) || amount <= 0) {
                console.warn("Malformed RPG:skill_points_award tag (invalid amount):", match[0]);
                return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };
            }
            return {
                statusChangesDelta: { skillPoints: (currentStatusRPG.skillPoints || 0) + amount },
                notifications: [{ message: `获得 ${amount} 技能点! (原因: ${reason})`, type: 'success', displayToUser: true }],
                achievementsToUnlock: []
            };
        },
    },
    { 
        regex: /\[RPG:health_energy_(?<action>change|set):(?:(?<changeValue>[+-]\d+)|current=(?<current>\d+),max=(?<max>\d+))(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentStatusRPG) => {
            const action = match.groups?.action;
            const reason = match.groups?.reason || (action === 'change' ? "未知原因" : "状态设定");
            let newHealth = currentStatusRPG.healthEnergy.current;
            let newMaxHealth = currentStatusRPG.healthEnergy.max;
            let notificationMessage = "";

            if (action === 'change') {
                const change = parseInt(match.groups?.changeValue || '0', 10);
                if (isNaN(change)) return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };
                newHealth = Math.max(0, Math.min(newHealth + change, newMaxHealth));
                const changeText = change > 0 ? `恢复 ${change}` : `受到 ${-change} 点伤害`;
                notificationMessage = `元气值变化: ${changeText} (原因: ${reason})。当前: ${newHealth}/${newMaxHealth}`;
            } else if (action === 'set') {
                const current = parseInt(match.groups?.current || String(newHealth), 10);
                const max = parseInt(match.groups?.max || String(newMaxHealth), 10);
                if (isNaN(current) || isNaN(max) || max <= 0) return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };
                newMaxHealth = max;
                newHealth = Math.min(current, newMaxHealth);
                notificationMessage = `元气值设定为: ${newHealth}/${newMaxHealth} (原因: ${reason})`;
            } else {
                return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };
            }
            
            return {
                statusChangesDelta: { healthEnergy: { current: newHealth, max: newMaxHealth } },
                notifications: [{ message: notificationMessage, type: action === 'set' ? 'info' : (parseInt(match.groups?.changeValue || '0') > 0 ? 'success' : 'warning'), displayToUser: true }],
                achievementsToUnlock: []
            };
        },
    },
    { 
        regex: /\[RPG:effect_add:type=(?<type>buff|debuff),name=(?<name>[^,\]]+),duration=(?<duration>-?\d+)(?:,description:(?<description>[^,\]]+))?(?:,icon:(?<icon>[^,\]]+))?(?:,source:(?<source>[^,\]]+))?\]/gi,
        handler: (match, currentStatusRPG): RPGProcessingResult => {
            const { type, name, duration, description, icon, source } = match.groups as unknown as RPGEffectAddParams_RPG & { type: 'buff' | 'debuff' }; 
            if (!type || !name || duration === undefined) {
                console.warn("Malformed RPG:effect_add tag:", match[0]);
                return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };
            }
            const newEffect: StatusEffect_RPG = {
                id: `effect_${name.replace(/\s+/g, '_')}_${Date.now()}`,
                name: name.trim(), description: description?.trim(), icon: icon?.trim(),
                durationTurns: Number(duration), remainingTurns: Number(duration),
                timestampApplied: Date.now(), source: source?.trim(), isBuff: type.toLowerCase() === 'buff',
            };
            let changes: Partial<PlayerStatus_RPG> = {};
            if (newEffect.isBuff) changes.buffs = [...(currentStatusRPG.buffs || []), newEffect];
            else changes.debuffs = [...(currentStatusRPG.debuffs || []), newEffect];
            return {
                statusChangesDelta: changes,
                notifications: [{ message: `${newEffect.isBuff ? '获得增益' : '受到减益'}: ${newEffect.name}`, type: newEffect.isBuff ? 'success' : 'warning', displayToUser: true }],
                achievementsToUnlock: []
            };
        }
    },
    { 
        regex: /\[RPG:effect_remove:name=(?<name>[^\]]+)\]/gi,
        handler: (match, currentStatusRPG): RPGProcessingResult => {
            const name = match.groups?.name?.trim();
            if (!name) {
                console.warn("Malformed RPG:effect_remove tag:", match[0]);
                return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };
            }
            return {
                statusChangesDelta: {
                    buffs: (currentStatusRPG.buffs || []).filter(eff => eff.name !== name),
                    debuffs: (currentStatusRPG.debuffs || []).filter(eff => eff.name !== name),
                },
                notifications: [{ message: `效果移除: ${name}`, type: 'info', displayToUser: true }],
                achievementsToUnlock: []
            };
        }
    },
    {
        regex: /\[RPG:quest_complete:(?<questId>[a-zA-Z0-9_]+),xp_reward:(?<xpReward>\d+),attribute_points_reward:(?<attrPointsReward>\d+),skill_points_reward:(?<skillPointsReward>\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi,
        handler: (match, currentStatusRPG) => {
            const questId = match.groups?.questId;
            const xpReward = parseInt(match.groups?.xpReward || '0', 10);
            const attrPointsReward = parseInt(match.groups?.attrPointsReward || '0', 10);
            const skillPointsReward = parseInt(match.groups?.skillPointsReward || '0', 10);
            const reason = match.groups?.reason || "任务完成";
    
            if (!questId) {
                console.warn("Malformed RPG:quest_complete tag (missing questId):", match[0]);
                return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };
            }
    
            const currentQuests = [...(currentStatusRPG.quests || [])];
            const questIndex = currentQuests.findIndex(q => q.id === questId && q.status === 'active');
    
            if (questIndex === -1) {
                console.warn(`RPG:quest_complete tag for unknown or already completed/failed quest: ${questId}`, match[0]);
                return { statusChangesDelta: {}, notifications: [], achievementsToUnlock: [] };
            }
    
            const completedQuest = { ...currentQuests[questIndex], status: 'completed' as 'completed', lastUpdated: Date.now() };
            currentQuests[questIndex] = completedQuest;
    
            const notificationsInternal: RPGNotificationDetails[] = [];
            notificationsInternal.push({ message: `任务【${completedQuest.title}】已完成！(${reason})`, type: 'success', title: "任务完成", displayToUser: true });
    
            let newStatusChanges: Partial<PlayerStatus_RPG> = { quests: currentQuests };
            let achievementsFromRewards: string[] = [];
    
            if (xpReward > 0) {
              const xpUpdateResult = calculateXpAndLevelChangesRPG(
                  currentStatusRPG.xp, currentStatusRPG.level, currentStatusRPG.xpToNextLevel,
                  currentStatusRPG.attributePoints, currentStatusRPG.skillPoints, xpReward
              );
              newStatusChanges = { ...newStatusChanges, ...xpUpdateResult.statusChangesDelta };
              notificationsInternal.push(...xpUpdateResult.notifications.map(n => ({...n, displayToUser: true})));
              achievementsFromRewards.push(...xpUpdateResult.achievementsToUnlock);
            }
    
            if (attrPointsReward > 0) {
                newStatusChanges.attributePoints = ((newStatusChanges.attributePoints ?? currentStatusRPG.attributePoints) || 0) + attrPointsReward;
                notificationsInternal.push({ message: `任务奖励: 获得 ${attrPointsReward} 属性点！`, type: 'success', displayToUser: true });
            }
    
            if (skillPointsReward > 0) {
                newStatusChanges.skillPoints = ((newStatusChanges.skillPoints ?? currentStatusRPG.skillPoints) || 0) + skillPointsReward;
                notificationsInternal.push({ message: `任务奖励: 获得 ${skillPointsReward} 技能点！`, type: 'success', displayToUser: true });
            }
    
            return {
                statusChangesDelta: newStatusChanges,
                notifications: notificationsInternal,
                achievementsToUnlock: achievementsFromRewards
            };
        },
    },
  ];

export const processStoryUpdateTagsRPG = (
    currentStatusRPG: PlayerStatus_RPG,
    storyUpdateText: string,
    newSessionTimeOfDay?: string, // New time of day from AI (Session perspective)
    oldSessionTimeOfDay?: string  // Session time of day before this AI turn
): RPGProcessingResult => {
    let cumulativeChanges: Partial<PlayerStatus_RPG> = {};
    const cumulativeNotifications: RPGNotificationDetails[] = [];
    const cumulativeAchievements: string[] = [];

    let workingStatus: PlayerStatus_RPG = JSON.parse(JSON.stringify(currentStatusRPG)); // Deep copy

    // --- Effect turn countdown ---
    if (workingStatus.buffs) {
        workingStatus.buffs = workingStatus.buffs
            .map(b => (b.durationTurns > 0 ? { ...b, remainingTurns: b.remainingTurns - 1 } : b))
            .filter(b => b.durationTurns === -1 || b.remainingTurns > 0);
    }
    if (workingStatus.debuffs) {
        workingStatus.debuffs = workingStatus.debuffs
            .map(d => (d.durationTurns > 0 ? { ...d, remainingTurns: d.remainingTurns - 1 } : d))
            .filter(d => d.durationTurns === -1 || d.remainingTurns > 0);
    }
    cumulativeChanges = { ...cumulativeChanges, buffs: workingStatus.buffs, debuffs: workingStatus.debuffs };


    // --- XP Gain Pass (must be first to ensure level ups apply before other calcs that might depend on level) ---
    let textBeingProcessed = storyUpdateText;
    const xpGainRegex = /\[RPG:xp_gain:(?<amount>\d+)(?:,reason:(?<reason>[^\]]+))?\]/gi;
    let totalXpGainedThisTurn = 0;
    let xpMatch;
    while ((xpMatch = xpGainRegex.exec(textBeingProcessed)) !== null) {
        const amount = parseInt(xpMatch.groups?.amount || '0', 10);
        if (!isNaN(amount) && amount > 0) totalXpGainedThisTurn += amount;
    }
    textBeingProcessed = textBeingProcessed.replace(xpGainRegex, "").trim(); // Remove XP tags

    if (totalXpGainedThisTurn > 0) {
        const xpUpdateResult = calculateXpAndLevelChangesRPG(
            workingStatus.xp, workingStatus.level, workingStatus.xpToNextLevel,
            workingStatus.attributePoints, workingStatus.skillPoints, totalXpGainedThisTurn
        );
        workingStatus = { ...workingStatus, ...xpUpdateResult.statusChangesDelta };
        cumulativeChanges = { ...cumulativeChanges, ...xpUpdateResult.statusChangesDelta };
        cumulativeNotifications.push(...xpUpdateResult.notifications);
        cumulativeAchievements.push(...xpUpdateResult.achievementsToUnlock);
    }

    // --- Process other RPG tags ---
    for (const processor of rpgTagProcessors_RPG) {
        // Skip xp_gain as it's already handled
        if (processor.regex.source.includes('RPG:xp_gain')) continue;

        let match;
        processor.regex.lastIndex = 0; 
        while ((match = processor.regex.exec(textBeingProcessed)) !== null) {
            const result = processor.handler(match, workingStatus);
            
            workingStatus = { ...workingStatus, ...result.statusChangesDelta };
            cumulativeChanges = { ...cumulativeChanges, ...result.statusChangesDelta };

            if (result.notifications) cumulativeNotifications.push(...result.notifications);
            if (result.achievementsToUnlock) cumulativeAchievements.push(...result.achievementsToUnlock);
        }
        textBeingProcessed = textBeingProcessed.replace(processor.regex, "").trim(); 
    }
    
    // --- Sanity-based achievement check ---
    if(workingStatus.coreAttributes?.sanity !== undefined && workingStatus.coreAttributes.sanity <= 2 && workingStatus.currentDay >=3) {
        const existingLowSanityAchievement = AVAILABLE_ACHIEVEMENTS.find(ach => ach.id === 'price_of_clarity');
        if (existingLowSanityAchievement) cumulativeAchievements.push(existingLowSanityAchievement.id);
    }

    // --- Day Advancement Logic (uses session times) ---
    if (newSessionTimeOfDay && oldSessionTimeOfDay) {
      const oldTimeIndex = TIME_OF_DAY_ORDER_RPG.indexOf(oldSessionTimeOfDay);
      const newTimeIndex = TIME_OF_DAY_ORDER_RPG.indexOf(newSessionTimeOfDay);

      if (oldTimeIndex !== -1 && newTimeIndex !== -1 && newTimeIndex < oldTimeIndex && newSessionTimeOfDay !== oldSessionTimeOfDay) { 
        const newDay = workingStatus.currentDay + 1;
        workingStatus.currentDay = newDay; // Update working status for consistency if other tags rely on it
        cumulativeChanges.currentDay = newDay; // Ensure delta reflects this change
        cumulativeNotifications.push({ message: UIText.dayAdvancedTo(newDay), type: 'info', displayToUser: true });
      }
    }


    return {
        statusChangesDelta: cumulativeChanges,
        notifications: Array.from(new Map(cumulativeNotifications.map(n => [`${n.type}_${n.message.substring(0,50)}`, n])).values()), // Deduplicate
        achievementsToUnlock: Array.from(new Set(cumulativeAchievements))
    };
};
