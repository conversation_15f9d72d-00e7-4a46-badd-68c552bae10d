import React, { useContext } from 'react';
import { PlayerStatus } from '../types';
import { UIText } from '../constants';
import { ThemeContext }from '../contexts/ThemeContext';

interface StatusPanelProps {
  status: PlayerStatus;
}

const StatusPanel: React.FC<StatusPanelProps> = ({ status }) => {
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  return (
    // bg-secondary-themed now has opacity from CSS vars, backdrop-blur-sm provides the frost.
    <div className="p-4 bg-secondary-themed backdrop-blur-sm rounded-xl shadow-themed-md mb-4 transition-colors duration-300">
      <h3 className="text-lg font-semibold text-accent-themed mb-2">
        {UIText.status}
      </h3>
      {/* 
        The 'emojis' property was removed from PlayerStatus.
        The detailed status is now shown in PersistentStatusDisplay.
        This panel, if used, will show a generic message.
      */}
      <p className="text-secondary-themed italic text-sm">{UIText.noSpecialEffects}</p>
    </div>
  );
};

export default StatusPanel;