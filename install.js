#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function log(message, color = 'reset') {
  console.log(colorize(message, color));
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(message, 'cyan');
  console.log('='.repeat(60));
}

function logStep(step, message) {
  log(`[${step}] ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(colorize(prompt, 'yellow'), resolve);
  });
}

async function checkPrerequisites() {
  logHeader('Checking Prerequisites');
  
  // Check Node.js version
  try {
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion >= 18) {
      logSuccess(`Node.js ${nodeVersion} detected`);
    } else {
      logError(`Node.js 18+ required, found ${nodeVersion}`);
      process.exit(1);
    }
  } catch (error) {
    logError('Node.js not found. Please install Node.js 18+ from https://nodejs.org/');
    process.exit(1);
  }

  // Check npm
  try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    logSuccess(`npm ${npmVersion} detected`);
  } catch (error) {
    logError('npm not found. Please install npm.');
    process.exit(1);
  }

  // Check available disk space
  try {
    const stats = fs.statSync('.');
    logSuccess('Sufficient disk space available');
  } catch (error) {
    logWarning('Could not check disk space');
  }
}

async function installDependencies() {
  logHeader('Installing Dependencies');
  
  logStep('1/3', 'Installing main dependencies...');
  try {
    execSync('npm install', { stdio: 'inherit' });
    logSuccess('Main dependencies installed');
  } catch (error) {
    logError('Failed to install dependencies');
    throw error;
  }

  logStep('2/3', 'Installing development dependencies...');
  try {
    execSync('npm install --save-dev', { stdio: 'inherit' });
    logSuccess('Development dependencies installed');
  } catch (error) {
    logWarning('Some development dependencies may have failed to install');
  }

  logStep('3/3', 'Verifying installation...');
  try {
    execSync('npm list --depth=0', { stdio: 'pipe' });
    logSuccess('All dependencies verified');
  } catch (error) {
    logWarning('Some dependency issues detected, but installation can continue');
  }
}

async function setupConfiguration() {
  logHeader('Setting Up Configuration');
  
  const envPath = '.env.local';
  const envExamplePath = '.env.local.example';
  
  if (fs.existsSync(envPath)) {
    const overwrite = await question('Configuration file already exists. Overwrite? (y/N): ');
    if (overwrite.toLowerCase() !== 'y') {
      logSuccess('Keeping existing configuration');
      return;
    }
  }

  if (fs.existsSync(envExamplePath)) {
    logStep('1/4', 'Copying configuration template...');
    fs.copyFileSync(envExamplePath, envPath);
    logSuccess('Configuration template copied');
  } else {
    logStep('1/4', 'Creating basic configuration...');
    const basicConfig = `# MemoryAble Configuration
GEMINI_API_KEY=
LOCAL_SERVER_PORT=3001
STORAGE_PROVIDER=local
ENABLE_ADVANCED_REGEX=true
ENABLE_CHARACTER_RELATIONSHIPS=true
`;
    fs.writeFileSync(envPath, basicConfig);
    logSuccess('Basic configuration created');
  }

  logStep('2/4', 'Configuration options...');
  
  const setupMode = await question('Setup mode? (1) Local-only (2) Hybrid (3) Cloud-only [2]: ');
  const mode = setupMode.trim() || '2';
  
  let configUpdates = '';
  
  switch (mode) {
    case '1':
      log('Setting up for local-only operation...', 'blue');
      configUpdates += 'STORAGE_PROVIDER=local\n';
      configUpdates += 'IMAGE_PROVIDER=local\n';
      configUpdates += 'ENABLE_LOCAL_AI=true\n';
      break;
    case '3':
      log('Setting up for cloud-only operation...', 'blue');
      configUpdates += 'STORAGE_PROVIDER=cloud\n';
      configUpdates += 'IMAGE_PROVIDER=cloud\n';
      configUpdates += 'ENABLE_LOCAL_AI=false\n';
      break;
    default:
      log('Setting up for hybrid operation...', 'blue');
      configUpdates += 'STORAGE_PROVIDER=hybrid\n';
      configUpdates += 'IMAGE_PROVIDER=cloud\n';
      configUpdates += 'ENABLE_LOCAL_AI=false\n';
  }

  logStep('3/4', 'API Key configuration...');
  if (mode !== '1') {
    const apiKey = await question('Enter your Gemini API key (optional, press Enter to skip): ');
    if (apiKey.trim()) {
      configUpdates += `GEMINI_API_KEY=${apiKey.trim()}\n`;
      logSuccess('API key configured');
    } else {
      logWarning('No API key provided - some features may be limited');
    }
  }

  logStep('4/4', 'Applying configuration...');
  if (configUpdates) {
    fs.appendFileSync(envPath, '\n# Auto-generated configuration\n' + configUpdates);
  }
  logSuccess('Configuration applied');
}

async function setupDirectories() {
  logHeader('Setting Up Directories');
  
  const directories = [
    'local-server/data',
    'local-server/data/saves',
    'local-server/data/characters',
    'local-server/data/worldbooks',
    'local-server/data/regex',
    'local-server/data/images',
    'local-server/data/backups',
    'local-server/uploads',
    'logs'
  ];

  for (let i = 0; i < directories.length; i++) {
    const dir = directories[i];
    logStep(`${i + 1}/${directories.length}`, `Creating ${dir}...`);
    
    try {
      fs.mkdirSync(dir, { recursive: true });
      logSuccess(`Created ${dir}`);
    } catch (error) {
      if (error.code === 'EEXIST') {
        logSuccess(`${dir} already exists`);
      } else {
        logWarning(`Failed to create ${dir}: ${error.message}`);
      }
    }
  }
}

async function testInstallation() {
  logHeader('Testing Installation');
  
  logStep('1/3', 'Testing local server startup...');
  try {
    // Start server in background and test
    const { spawn } = require('child_process');
    const server = spawn('node', ['local-server/index.js'], { 
      stdio: 'pipe',
      detached: true
    });
    
    // Wait a moment for server to start
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test health endpoint
    const http = require('http');
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/health',
      method: 'GET',
      timeout: 5000
    };
    
    const req = http.request(options, (res) => {
      if (res.statusCode === 200) {
        logSuccess('Local server is working');
      } else {
        logWarning(`Local server responded with status ${res.statusCode}`);
      }
      server.kill();
    });
    
    req.on('error', (error) => {
      logWarning('Local server test failed - this is normal for first run');
      server.kill();
    });
    
    req.on('timeout', () => {
      logWarning('Local server test timed out');
      server.kill();
    });
    
    req.end();
    
    // Wait for test to complete
    await new Promise(resolve => setTimeout(resolve, 3000));
    
  } catch (error) {
    logWarning('Could not test local server automatically');
  }

  logStep('2/3', 'Testing build process...');
  try {
    execSync('npm run build', { stdio: 'pipe' });
    logSuccess('Build process works');
  } catch (error) {
    logWarning('Build test failed - check your configuration');
  }

  logStep('3/3', 'Verifying file permissions...');
  try {
    fs.accessSync('.', fs.constants.R_OK | fs.constants.W_OK);
    logSuccess('File permissions are correct');
  } catch (error) {
    logError('File permission issues detected');
  }
}

async function showCompletionMessage() {
  logHeader('Installation Complete!');
  
  log('🎉 MemoryAble has been successfully installed!', 'green');
  console.log();
  
  log('Next steps:', 'cyan');
  log('1. Start the local server:', 'blue');
  log('   npm run local-server', 'bright');
  console.log();
  
  log('2. In another terminal, start the development server:', 'blue');
  log('   npm run dev', 'bright');
  console.log();
  
  log('3. Or start both simultaneously:', 'blue');
  log('   npm run dev:local', 'bright');
  console.log();
  
  log('4. Open your browser to:', 'blue');
  log('   http://localhost:5173', 'bright');
  console.log();
  
  log('Configuration:', 'cyan');
  log('• Configuration file: .env.local', 'blue');
  log('• Local data directory: local-server/data/', 'blue');
  log('• Logs directory: logs/', 'blue');
  console.log();
  
  log('Documentation:', 'cyan');
  log('• README.md - Complete usage guide', 'blue');
  log('• OPTIMIZATION_PLAN.md - Technical details', 'blue');
  log('• .env.local.example - Configuration reference', 'blue');
  console.log();
  
  log('Need help?', 'cyan');
  log('• Check the troubleshooting section in README.md', 'blue');
  log('• Review the configuration options in .env.local', 'blue');
  log('• Submit issues on GitHub', 'blue');
  console.log();
  
  log('Happy storytelling! 📚✨', 'magenta');
}

async function main() {
  try {
    log('🚀 MemoryAble Installation Script', 'bright');
    log('This script will set up MemoryAble for local development and usage.', 'cyan');
    console.log();

    await checkPrerequisites();
    await installDependencies();
    await setupConfiguration();
    await setupDirectories();
    await testInstallation();
    await showCompletionMessage();
    
  } catch (error) {
    logError(`Installation failed: ${error.message}`);
    console.log();
    log('Troubleshooting:', 'cyan');
    log('1. Check that you have Node.js 18+ installed', 'blue');
    log('2. Ensure you have write permissions in this directory', 'blue');
    log('3. Try running: npm cache clean --force', 'blue');
    log('4. Check your internet connection for dependency downloads', 'blue');
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the installation
if (require.main === module) {
  main();
}
