// hooks/useGameSaves.ts

import { useState, useEffect, useCallback } from 'react';
import { GameSaveData, GameSettingsData, DialogueLine, PlayerStatus, LocalStorageKeys, NotificationType, Content, CharacterCardData, PlayerStatus_Session } from '../types';
import { UIText, InitialPlayerStatus, DefaultGameSettings, GEMINI_MODEL_TEXT_FALLBACK } from '../constants';
import { ensureCustomElementsStructure } from '../utils/dataUtils';
import { convertDialogueLogToGeminiHistory } from '../utils/geminiUtils';
import { InitialPlayerStatus_RPG } from '@/rpgSystem/constants_rpg'; // Import RPG initial status

// Type alias for the saveGame function
type SaveGameFn = (
  name: string,
  playerName: string,
  dialogueLog: DialogueLine[],
  currentSceneImageKeyword: string,
  currentPlayerStatus: PlayerStatus, // Updated to use composite PlayerStatus
  geminiHistory: Content[]
) => Promise<string | null>;

// Type alias for the updateSave function
type UpdateSaveFn = (
  saveId: string,
  playerName: string,
  dialogueLog: DialogueLine[],
  currentSceneImageKeyword: string,
  currentPlayerStatus: PlayerStatus, // Updated
  geminiHistory: Content[]
) => void;

// Explicit return type for the useGameSaves hook
interface UseGameSavesReturn {
  gameSaves: GameSaveData[];
  setGameSaves: React.Dispatch<React.SetStateAction<GameSaveData[]>>;
  currentQuickSaveId: string | null;
  setCurrentQuickSaveId: React.Dispatch<React.SetStateAction<string | null>>;
  saveGame: SaveGameFn;
  updateSave: UpdateSaveFn;
  loadGame: (saveId: string) => GameSaveData | null;
  deleteSave: (saveId: string) => void;
  renameSave: (saveId: string, newName: string) => void;
}

export const useGameSaves = (
  currentGameSettings: GameSettingsData,
  unlockedAchievements: Record<string, number>, 
  unlockAchievement: (achievementId: string) => void,
  addNotification: (message: string, type: NotificationType, duration?: number) => void
): UseGameSavesReturn => {
  const [gameSaves, setGameSaves] = useState<GameSaveData[]>(() => {
    const saved = localStorage.getItem(LocalStorageKeys.GAME_SAVES);
    return saved ? JSON.parse(saved) : [];
  });

  const [currentQuickSaveId, setCurrentQuickSaveId] = useState<string | null>(null);

  useEffect(() => {
    localStorage.setItem(LocalStorageKeys.GAME_SAVES, JSON.stringify(gameSaves));
  }, [gameSaves]);

  const saveGame: SaveGameFn = useCallback(async (
    name,
    playerName,
    dialogueLog,
    currentSceneImageKeyword,
    currentPlayerStatus, // Now composite
    geminiHistory
  ) => {
    if (!playerName.trim()) { addNotification(UIText.saveErrorNoSession, 'error'); return null; }
    if (!name.trim()){ addNotification(UIText.saveErrorInvalidName, 'error'); return null; }

    const { ...gameSettingsToSave } = currentGameSettings; 

    const newSave: GameSaveData = {
      ...gameSettingsToSave, // This now includes CharacterCardData from GameSettingsData
      id: `save-${Date.now()}`,
      name: name.trim(),
      timestamp: Date.now(),
      playerName, // Explicitly set playerName for the save
      dialogueLog,
      currentSceneImageKeyword,
      currentPlayerStatus: {...currentPlayerStatus, name: playerName}, // Ensure name in status matches
      geminiChatHistory: geminiHistory
    };
    setGameSaves(prevSaves => [...prevSaves, newSave]);
    addNotification(UIText.saveSuccess(newSave.name), 'success');
    unlockAchievement('memory_keeper_initiate');
    if (gameSaves.length + 1 >= 5) unlockAchievement('memory_keeper_master');
    setCurrentQuickSaveId(newSave.id);
    return newSave.id;
  }, [currentGameSettings, addNotification, unlockAchievement, gameSaves, setCurrentQuickSaveId]);

  const updateSave: UpdateSaveFn = useCallback((
    saveId: string,
    playerName: string,
    dialogueLog: DialogueLine[],
    currentSceneImageKeyword: string,
    currentPlayerStatus, // Now composite
    geminiHistory: Content[]
  ) => {
    const saveToUpdate = gameSaves.find(s => s.id === saveId);
    if (!saveToUpdate) { addNotification(UIText.loadErrorNotFound, "error"); return; }
    if (!playerName.trim()) { addNotification(UIText.saveErrorNoSession, 'error'); return; }

    const { ...gameSettingsToSave } = currentGameSettings;

    const updatedSave: GameSaveData = {
      ...saveToUpdate,
      ...gameSettingsToSave, // Includes CharacterCardData
      timestamp: Date.now(),
      playerName, // Update playerName for the save
      dialogueLog,
      currentSceneImageKeyword,
      currentPlayerStatus: {...currentPlayerStatus, name: playerName}, // Ensure name in status matches
      geminiChatHistory: geminiHistory 
    };
    setGameSaves(prevSaves => prevSaves.map(s => s.id === saveId ? updatedSave : s));
    addNotification(UIText.updateSuccess(updatedSave.name), 'success');
    setCurrentQuickSaveId(saveId);
  }, [gameSaves, currentGameSettings, addNotification, setCurrentQuickSaveId]);

  const loadGame = useCallback((saveId: string): GameSaveData | null => {
    const saveToLoad = gameSaves.find(s => s.id === saveId);
    if (saveToLoad) {
      // Ensure the loaded player status conforms to the latest PlayerStatus type
      const initialSessionStatus: PlayerStatus_Session = {
          name: saveToLoad.playerName,
          mood: saveToLoad.currentPlayerStatus?.mood || InitialPlayerStatus.mood,
          timeOfDay: saveToLoad.currentPlayerStatus?.timeOfDay || InitialPlayerStatus.timeOfDay,
          weather: saveToLoad.currentPlayerStatus?.weather || InitialPlayerStatus.weather,
      };

      const initialRPGStatus: typeof InitialPlayerStatus_RPG = {
          ...InitialPlayerStatus_RPG, // Base RPG defaults
          ...(saveToLoad.currentPlayerStatus || {}), // Spread only RPG relevant fields from saved status
      };
      
      // Reconstruct the full PlayerStatus ensuring all fields are present
      const loadedPlayerStatus: PlayerStatus = {
          ...initialSessionStatus, // Session parts
          ...initialRPGStatus,    // RPG parts
          // Explicitly ensure name is from saveToLoad.playerName for consistency
          name: saveToLoad.playerName,
          // Ensure RPG specific fields that might be missing in older saves get RPG defaults
          currentDay: saveToLoad.currentPlayerStatus?.currentDay ?? InitialPlayerStatus_RPG.currentDay,
      };


      const fullyLoadedSaveData: GameSaveData = {
          ...DefaultGameSettings, 
          ...saveToLoad, 
          customNarrativeElements: ensureCustomElementsStructure(saveToLoad.customNarrativeElements || DefaultGameSettings.customNarrativeElements),
          currentPlayerStatus: loadedPlayerStatus, 
          selectedModelId: saveToLoad.selectedModelId || DefaultGameSettings.selectedModelId,
          selectedImagePromptStyleId: saveToLoad.selectedImagePromptStyleId || DefaultGameSettings.selectedImagePromptStyleId,
          selectedSummaryModelId: saveToLoad.selectedSummaryModelId || DefaultGameSettings.selectedSummaryModelId,
          chatInterfaceOpacity: (saveToLoad as any).chatInterfaceOpacity ?? (saveToLoad as any).dialogueOpacity ?? DefaultGameSettings.chatInterfaceOpacity,
          dialogueBubbleOpacity: saveToLoad.dialogueBubbleOpacity ?? DefaultGameSettings.dialogueBubbleOpacity,
          dialogueBlur: saveToLoad.dialogueBlur ?? DefaultGameSettings.dialogueBlur,
          fontSizeScale: saveToLoad.fontSizeScale ?? DefaultGameSettings.fontSizeScale,
          enableBackdropBlur: saveToLoad.enableBackdropBlur ?? DefaultGameSettings.enableBackdropBlur,
          enableImageGeneration: saveToLoad.enableImageGeneration ?? DefaultGameSettings.enableImageGeneration,
          minOutputChars: saveToLoad.minOutputChars ?? DefaultGameSettings.minOutputChars,
          maxOutputChars: saveToLoad.maxOutputChars ?? DefaultGameSettings.maxOutputChars,
          imageGenerationInterval: saveToLoad.imageGenerationInterval ?? DefaultGameSettings.imageGenerationInterval,
          enableStreamMode: saveToLoad.enableStreamMode ?? DefaultGameSettings.enableStreamMode,
          enablePseudoStreamMode: saveToLoad.enablePseudoStreamMode ?? DefaultGameSettings.enablePseudoStreamMode,
          githubPat: saveToLoad.githubPat, 
          gistId: saveToLoad.gistId,
          saveGithubPat: saveToLoad.saveGithubPat ?? DefaultGameSettings.saveGithubPat,
          enableGistAutoBackup: saveToLoad.enableGistAutoBackup ?? DefaultGameSettings.enableGistAutoBackup,
          gistAutoBackupIntervalHours: saveToLoad.gistAutoBackupIntervalHours ?? DefaultGameSettings.gistAutoBackupIntervalHours,
          gistUseSystemProxy: saveToLoad.gistUseSystemProxy ?? DefaultGameSettings.gistUseSystemProxy,
          geminiChatHistory: saveToLoad.geminiChatHistory || convertDialogueLogToGeminiHistory(saveToLoad.dialogueLog),
          enableRegexReplacement: saveToLoad.enableRegexReplacement ?? DefaultGameSettings.enableRegexReplacement,
          regexRules: saveToLoad.regexRules ?? DefaultGameSettings.regexRules,
      };
      delete (fullyLoadedSaveData as any).dialogueOpacity;

      addNotification(UIText.loadSuccess(fullyLoadedSaveData.name, fullyLoadedSaveData.playerName), 'success');
      setCurrentQuickSaveId(saveId);
      return fullyLoadedSaveData;
    } else {
      addNotification(UIText.loadErrorNotFound, 'error');
      return null;
    }
  }, [gameSaves, addNotification, setCurrentQuickSaveId]);

  const deleteSave = useCallback((saveId: string) => {
    const deletedSaveName = gameSaves.find(s => s.id === saveId)?.name || "Unknown Save";
    setGameSaves(prevSaves => prevSaves.filter(s => s.id !== saveId));
    addNotification(UIText.deleteSuccess(deletedSaveName), 'success');
    if(currentQuickSaveId === saveId) setCurrentQuickSaveId(null);
  }, [gameSaves, addNotification, currentQuickSaveId, setCurrentQuickSaveId]);

  const renameSave = useCallback((saveId: string, newName: string) => {
    if (!newName.trim()){ addNotification(UIText.saveErrorInvalidName, 'error'); return; }
    let oldName = "存档";
    setGameSaves(prevSaves => prevSaves.map(s => { if (s.id === saveId) { oldName = s.name; return { ...s, name: newName.trim() }; } return s; }));
    addNotification(UIText.renameSuccess(oldName, newName.trim()), 'success');
  }, [addNotification]);

  return {
    gameSaves,
    setGameSaves,
    currentQuickSaveId,
    setCurrentQuickSaveId,
    saveGame,
    updateSave,
    loadGame,
    deleteSave,
    renameSave,
  };
};
