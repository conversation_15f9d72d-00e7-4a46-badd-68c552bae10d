
import { useState, useEffect } from 'react';

export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(window.matchMedia(query).matches);

  useEffect(() => {
    const mediaQueryList = window.matchMedia(query);
    const listener = (event: MediaQueryListEvent) => setMatches(event.matches);

    // Modern browsers
    try {
        mediaQueryList.addEventListener('change', listener);
    } catch (e1) {
        // Fallback for older browsers like Safari < 14
        try {
            mediaQueryList.addListener(listener);
        } catch (e2) {
            console.error("useMediaQuery: addListener/addEventListener failed", e2);
        }
    }

    // Set initial state correctly
    setMatches(mediaQueryList.matches);

    return () => {
      try {
        mediaQueryList.removeEventListener('change', listener);
      } catch (e1) {
        try {
            mediaQueryList.removeListener(listener);
        } catch (e2) {
            console.error("useMediaQuery: removeListener/removeEventListener failed", e2);
        }
      }
    };
  }, [query]);

  return matches;
};
