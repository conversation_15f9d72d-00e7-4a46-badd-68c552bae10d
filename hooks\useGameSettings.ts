

import { useState, useEffect, useCallback } from 'react';
import { GameSettingsData, LocalStorageKeys, CustomNarrativePrimaryElement, CharacterCardData, RegexRule } from '../types';
import { DefaultGameSettings, AVAILABLE_GEMINI_MODELS, AVAILABLE_IMAGE_PROMPT_STYLES, AVAILABLE_SUMMARY_MODELS } from '../constants';
import { ensureCustomElementsStructure } from '../utils/dataUtils';

interface StoredSettings extends Partial<GameSettingsData> {
  dialogueOpacity?: number; // Old field for migration
}

// Utility to ensure regex rules have the correct structure and default values
export const ensureRegexRulesStructure = (rules: any): RegexRule[] => {
    if (!Array.isArray(rules)) return [];
    return rules.map((rule: any, index: number) => ({
        id: rule.id || `regex_${Date.now()}_${index}`,
        name: typeof rule.name === 'string' ? rule.name : `规则 ${index + 1}`, // Default name
        pattern: typeof rule.pattern === 'string' ? rule.pattern : '',
        replacement: typeof rule.replacement === 'string' ? rule.replacement : '',
        flags: typeof rule.flags === 'string' ? rule.flags : 'gi',
        scope: ['input', 'output', 'all'].includes(rule.scope) ? rule.scope : 'all',
        isActive: typeof rule.isActive === 'boolean' ? rule.isActive : true,
        isDisplayOnly: typeof rule.isDisplayOnly === 'boolean' ? rule.isDisplayOnly : false, // Default isDisplayOnly
        trimInput: typeof rule.trimInput === 'string' ? rule.trimInput : '', // Default trimInput
    }));
};


export const useGameSettings = () => {
  const [gameSettings, setGameSettingsState] = useState<GameSettingsData>(() => {
    const savedSettingsJson = localStorage.getItem(LocalStorageKeys.GAME_SETTINGS);
    let baseSettings = { ...DefaultGameSettings }; 

    if (savedSettingsJson) {
      try {
        const parsedSettings = JSON.parse(savedSettingsJson) as StoredSettings;
        
        const { 
            dialogueOpacity: oldDialogueOpacity,
            ...restOfParsedSettings 
        } = parsedSettings;

        let chatInterfaceOpacityValue = restOfParsedSettings.chatInterfaceOpacity;
        if (typeof oldDialogueOpacity === 'number' && typeof chatInterfaceOpacityValue === 'undefined') {
            chatInterfaceOpacityValue = oldDialogueOpacity;
        }
        if (typeof chatInterfaceOpacityValue !== 'number' || chatInterfaceOpacityValue < 0.1 || chatInterfaceOpacityValue > 1) {
            chatInterfaceOpacityValue = DefaultGameSettings.chatInterfaceOpacity;
        }
        
        const characterCardPart: CharacterCardData = {
            characterName: restOfParsedSettings.characterName || DefaultGameSettings.characterName,
            characterDescription: restOfParsedSettings.characterDescription || DefaultGameSettings.characterDescription,
            characterOpeningMessage: restOfParsedSettings.characterOpeningMessage || DefaultGameSettings.characterOpeningMessage,
            characterPersonality: restOfParsedSettings.characterPersonality || DefaultGameSettings.characterPersonality,
            characterScenario: restOfParsedSettings.characterScenario || DefaultGameSettings.characterScenario,
            characterExampleDialogue: restOfParsedSettings.characterExampleDialogue || DefaultGameSettings.characterExampleDialogue,
            characterPortraitKeywords: restOfParsedSettings.characterPortraitKeywords || DefaultGameSettings.characterPortraitKeywords,
        };
        
        const enableStreamModeValue = typeof restOfParsedSettings.enableStreamMode === 'boolean'
                                      ? restOfParsedSettings.enableStreamMode
                                      : DefaultGameSettings.enableStreamMode;
        
        const saveGithubPatValue = typeof restOfParsedSettings.saveGithubPat === 'boolean' 
                                    ? restOfParsedSettings.saveGithubPat 
                                    : DefaultGameSettings.saveGithubPat;
        const githubPatValue = saveGithubPatValue && typeof restOfParsedSettings.githubPat === 'string' 
                                ? restOfParsedSettings.githubPat 
                                : DefaultGameSettings.githubPat;


        baseSettings = {
          ...DefaultGameSettings, 
          ...restOfParsedSettings, 
          ...characterCardPart, 
          chatInterfaceOpacity: chatInterfaceOpacityValue,
          customNarrativeElements: ensureCustomElementsStructure(restOfParsedSettings.customNarrativeElements || DefaultGameSettings.customNarrativeElements),
          selectedModelId: AVAILABLE_GEMINI_MODELS.some(m => m.id === restOfParsedSettings.selectedModelId)
                            ? restOfParsedSettings.selectedModelId!
                            : DefaultGameSettings.selectedModelId,
          selectedImagePromptStyleId: AVAILABLE_IMAGE_PROMPT_STYLES.some(s => s.id === restOfParsedSettings.selectedImagePromptStyleId)
                            ? restOfParsedSettings.selectedImagePromptStyleId!
                            : DefaultGameSettings.selectedImagePromptStyleId,
          selectedSummaryModelId: AVAILABLE_SUMMARY_MODELS.some(m => m.id === restOfParsedSettings.selectedSummaryModelId)
                            ? restOfParsedSettings.selectedSummaryModelId!
                            : DefaultGameSettings.selectedSummaryModelId,
          dialogueBubbleOpacity: typeof restOfParsedSettings.dialogueBubbleOpacity === 'number'
                            ? Math.max(0.1, Math.min(1, restOfParsedSettings.dialogueBubbleOpacity))
                            : DefaultGameSettings.dialogueBubbleOpacity,
          dialogueBlur: typeof restOfParsedSettings.dialogueBlur === 'number'
                            ? Math.max(0, Math.min(20, restOfParsedSettings.dialogueBlur))
                            : DefaultGameSettings.dialogueBlur,
          fontSizeScale: typeof restOfParsedSettings.fontSizeScale === 'number' 
                            ? Math.max(0.8, Math.min(1.5, restOfParsedSettings.fontSizeScale))
                            : DefaultGameSettings.fontSizeScale,
          enableBackdropBlur: typeof restOfParsedSettings.enableBackdropBlur === 'boolean'
                            ? restOfParsedSettings.enableBackdropBlur
                            : DefaultGameSettings.enableBackdropBlur,
          enableImageGeneration: typeof restOfParsedSettings.enableImageGeneration === 'boolean'
                            ? restOfParsedSettings.enableImageGeneration
                            : DefaultGameSettings.enableImageGeneration,
          minOutputChars: typeof restOfParsedSettings.minOutputChars === 'number'
                            ? Math.max(30, Math.min(1500, restOfParsedSettings.minOutputChars)) 
                            : DefaultGameSettings.minOutputChars,
          maxOutputChars: typeof restOfParsedSettings.maxOutputChars === 'number'
                            ? Math.max(500, Math.min(5000, restOfParsedSettings.maxOutputChars))
                            : DefaultGameSettings.maxOutputChars,
          imageGenerationInterval: typeof restOfParsedSettings.imageGenerationInterval === 'number'
                            ? Math.max(0, Math.min(20, restOfParsedSettings.imageGenerationInterval))
                            : DefaultGameSettings.imageGenerationInterval,
          enableStreamMode: enableStreamModeValue,
          enablePseudoStreamMode: typeof restOfParsedSettings.enablePseudoStreamMode === 'boolean' && enableStreamModeValue
                                  ? restOfParsedSettings.enablePseudoStreamMode
                                  : DefaultGameSettings.enablePseudoStreamMode,
          githubPat: githubPatValue,
          gistId: typeof restOfParsedSettings.gistId === 'string' ? restOfParsedSettings.gistId : DefaultGameSettings.gistId,
          saveGithubPat: saveGithubPatValue,
          enableGistAutoBackup: typeof restOfParsedSettings.enableGistAutoBackup === 'boolean' ? restOfParsedSettings.enableGistAutoBackup : DefaultGameSettings.enableGistAutoBackup,
          gistAutoBackupIntervalHours: typeof restOfParsedSettings.gistAutoBackupIntervalHours === 'number' && restOfParsedSettings.gistAutoBackupIntervalHours >= 0.25 
                                          ? restOfParsedSettings.gistAutoBackupIntervalHours 
                                          : DefaultGameSettings.gistAutoBackupIntervalHours,
          gistUseSystemProxy: typeof restOfParsedSettings.gistUseSystemProxy === 'boolean' ? restOfParsedSettings.gistUseSystemProxy : DefaultGameSettings.gistUseSystemProxy,
          enableRegexReplacement: typeof restOfParsedSettings.enableRegexReplacement === 'boolean' 
                                    ? restOfParsedSettings.enableRegexReplacement 
                                    : DefaultGameSettings.enableRegexReplacement,
          regexRules: ensureRegexRulesStructure(restOfParsedSettings.regexRules || DefaultGameSettings.regexRules),
        };
        delete (baseSettings as any).dialogueOpacity; 

      } catch (e) {
        console.error("Failed to parse saved game settings, using defaults:", e);
      }
    }
    return baseSettings;
  });

  const persistGameSettings = useCallback(() => {
    const settingsToSave: GameSettingsData = {
        ...gameSettings, 
        // Only save githubPat if saveGithubPat is true; otherwise, save an empty string.
        githubPat: gameSettings.saveGithubPat ? gameSettings.githubPat : "", 
    };
    localStorage.setItem(LocalStorageKeys.GAME_SETTINGS, JSON.stringify(settingsToSave));
  }, [gameSettings]);

  useEffect(() => {
    persistGameSettings();
  }, [gameSettings, persistGameSettings]);
  
  const updateGameSettings = useCallback((newSettings: Partial<GameSettingsData> | ((prevState: GameSettingsData) => GameSettingsData)) => {
    setGameSettingsState(prev => {
        let updatedState = typeof newSettings === 'function' ? newSettings(prev) : {...prev, ...newSettings};
        
        if (updatedState.hasOwnProperty('customNarrativeElements')) {
             updatedState.customNarrativeElements = ensureCustomElementsStructure(updatedState.customNarrativeElements);
        }
        if (updatedState.hasOwnProperty('regexRules')) {
            updatedState.regexRules = ensureRegexRulesStructure(updatedState.regexRules);
       }
        if (updatedState.hasOwnProperty('minOutputChars')) {
             updatedState.minOutputChars = Math.max(30, Math.min(1500, updatedState.minOutputChars || DefaultGameSettings.minOutputChars));
        }
        if (updatedState.hasOwnProperty('maxOutputChars')) {
             updatedState.maxOutputChars = Math.max(500, Math.min(5000, updatedState.maxOutputChars || DefaultGameSettings.maxOutputChars));
        }
        if (updatedState.hasOwnProperty('chatInterfaceOpacity')) {
            updatedState.chatInterfaceOpacity = Math.max(0.1, Math.min(1, updatedState.chatInterfaceOpacity || DefaultGameSettings.chatInterfaceOpacity));
        }
        if (updatedState.hasOwnProperty('dialogueBubbleOpacity')) {
            updatedState.dialogueBubbleOpacity = Math.max(0.1, Math.min(1, updatedState.dialogueBubbleOpacity || DefaultGameSettings.dialogueBubbleOpacity));
        }
        if (updatedState.hasOwnProperty('dialogueBlur')) {
            updatedState.dialogueBlur = Math.max(0, Math.min(20, updatedState.dialogueBlur || DefaultGameSettings.dialogueBlur));
        }
        if (updatedState.hasOwnProperty('fontSizeScale')) {
            updatedState.fontSizeScale = Math.max(0.8, Math.min(1.5, updatedState.fontSizeScale || DefaultGameSettings.fontSizeScale));
        }
        if (updatedState.hasOwnProperty('imageGenerationInterval')) {
            updatedState.imageGenerationInterval = Math.max(0, Math.min(20, updatedState.imageGenerationInterval || DefaultGameSettings.imageGenerationInterval));
        }
        if (updatedState.hasOwnProperty('enableStreamMode') && updatedState.enableStreamMode === false) {
            updatedState.enablePseudoStreamMode = false;
        }
        if (updatedState.hasOwnProperty('enablePseudoStreamMode') && updatedState.enablePseudoStreamMode === true && updatedState.enableStreamMode === false) {
            updatedState.enablePseudoStreamMode = false; 
        }
        if (updatedState.hasOwnProperty('gistAutoBackupIntervalHours')) {
            updatedState.gistAutoBackupIntervalHours = Math.max(0.25, updatedState.gistAutoBackupIntervalHours || DefaultGameSettings.gistAutoBackupIntervalHours);
        }
        if (updatedState.hasOwnProperty('gistUseSystemProxy') && typeof updatedState.gistUseSystemProxy !== 'boolean') {
            updatedState.gistUseSystemProxy = DefaultGameSettings.gistUseSystemProxy;
        }
        // If saveGithubPat is being set to false, clear the githubPat
        if (updatedState.hasOwnProperty('saveGithubPat') && updatedState.saveGithubPat === false) {
            updatedState.githubPat = "";
        }

        return updatedState;
    });
  }, []);

  return { gameSettings, setGameSettings: updateGameSettings, persistGameSettings };
};