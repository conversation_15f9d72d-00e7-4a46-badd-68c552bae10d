

import React, { useContext, useEffect, useState, useRef } from 'react';
import { DialogueLine, GameSettingsData, Theme, NotificationType } from '../types'; 
import { ThemeContext } from '../contexts/ThemeContext';
import { Icons, UIText } from '../constants'; 
import { useMediaQuery } from '../hooks/useMediaQuery'; 
import { NotificationContext } from '../contexts/NotificationContext';


interface DialogueBubbleProps {
  line: DialogueLine;
  gameSettings: GameSettingsData;
  onRegenerate?: (lineId: string) => void;
  onDeleteLine?: (lineId: string) => void; 
  // Editing props
  editingLineId: string | null;
  currentlyEditedContent: string;
  onStartEdit: (lineId: string, currentHtml: string) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onCurrentlyEditedContentChange: (newHtml: string) => void;
}

// Helper function to apply opacity to an RGBA color string
// This function now SETS the alpha to desiredFinalAlpha, overriding original alpha.
const applyOpacityToRgbaColor = (colorString: string, desiredFinalAlpha: number): string => {
  if (!colorString || typeof colorString !== 'string') return colorString;

  const rgbaMatch = colorString.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)$/i);
  if (rgbaMatch) {
    const r = rgbaMatch[1];
    const g = rgbaMatch[2];
    const b = rgbaMatch[3];
    // const existingAlpha = rgbaMatch[4] !== undefined ? parseFloat(rgbaMatch[4]) : 1; // Original alpha is no longer used to calculate newAlpha
    const newAlpha = Math.max(0, Math.min(1, desiredFinalAlpha)); // Clamp desiredFinalAlpha to [0,1]
    return `rgba(${r}, ${g}, ${b}, ${newAlpha.toFixed(3)})`;
  }
  // If the color string is not RGBA (e.g., hex, named color), it's returned as is.
  // This is acceptable as dialogue bubble backgrounds are expected to be RGBA from CSS variables.
  return colorString; 
};


const DialogueBubble: React.FC<DialogueBubbleProps> = React.memo(({ 
  line, 
  gameSettings, 
  onRegenerate, 
  onDeleteLine,
  editingLineId,
  currentlyEditedContent,
  onStartEdit,
  onSaveEdit,
  onCancelEdit,
  onCurrentlyEditedContentChange
}) => {
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");
  const { theme } = themeContext;
  const isPortrait = useMediaQuery('(orientation: portrait)'); 

  const notificationContext = useContext(NotificationContext);
  if (!notificationContext) throw new Error("NotificationContext not found for DialogueBubble");
  const { addNotification } = notificationContext;

  const [dynamicBubbleBackground, setDynamicBubbleBackground] = useState<string>('');
  const [dynamicNameplateBackground, setDynamicNameplateBackground] = useState<string>('');
  const editableDivRef = useRef<HTMLDivElement>(null);

  const isPlayer = line.speakerType === 'player';
  const isNarrator = line.speakerType === 'narrator';
  const isEditingThisLine = editingLineId === line.id;
  
  const displayName = isPlayer ? UIText.player(line.speakerName) : 
                      isNarrator ? UIText.narrator : line.speakerName;

  useEffect(() => {
    const rootStyle = getComputedStyle(document.documentElement);
    // dialogueBubbleOpacity now directly dictates the final alpha of the bubble.
    const finalBubbleAlpha = gameSettings.dialogueBubbleOpacity; 

    let bgVarStart = '';
    let bgVarEnd = '';
    let nameplateBgVarStart = '--npc-nameplate-bg-light-start'; 
    let nameplateBgVarEnd = '--npc-nameplate-bg-light-end';

    if (isPlayer) {
      bgVarStart = theme === Theme.Dark ? '--bg-dialogue-player-dark-start' : 
                   theme === Theme.Sakura ? '--bg-dialogue-player-light-start' : 
                   theme === Theme.Candy ? '--bg-dialogue-player-light-start' :  
                   theme === Theme.Forest ? '--bg-dialogue-player-light-start' : 
                   theme === Theme.Starry ? '--bg-dialogue-player-dark-start' :  
                   '--bg-dialogue-player-light-start';
      bgVarEnd = theme === Theme.Dark ? '--bg-dialogue-player-dark-end' : 
                 theme === Theme.Sakura ? '--bg-dialogue-player-light-end' :
                 theme === Theme.Candy ? '--bg-dialogue-player-light-end' :
                 theme === Theme.Forest ? '--bg-dialogue-player-light-end' :
                 theme === Theme.Starry ? '--bg-dialogue-player-dark-end' :
                 '--bg-dialogue-player-light-end';
    } else if (isNarrator) {
      bgVarStart = theme === Theme.Dark ? '--bg-dialogue-narrator-dark-start' : 
                   theme === Theme.Sakura ? '--bg-dialogue-narrator-light-start' :
                   theme === Theme.Candy ? '--bg-dialogue-narrator-light-start' :
                   theme === Theme.Forest ? '--bg-dialogue-narrator-light-start' :
                   theme === Theme.Starry ? '--bg-dialogue-narrator-dark-start' :
                   '--bg-dialogue-narrator-light-start';
      bgVarEnd = theme === Theme.Dark ? '--bg-dialogue-narrator-dark-end' : 
                 theme === Theme.Sakura ? '--bg-dialogue-narrator-light-end' :
                 theme === Theme.Candy ? '--bg-dialogue-narrator-light-end' :
                 theme === Theme.Forest ? '--bg-dialogue-narrator-light-end' :
                 theme === Theme.Starry ? '--bg-dialogue-narrator-dark-end' :
                 '--bg-dialogue-narrator-light-end';
    } else { // NPC
      bgVarStart = theme === Theme.Dark ? '--bg-dialogue-npc-dark-start' : 
                   theme === Theme.Sakura ? '--bg-dialogue-npc-light-start' :
                   theme === Theme.Candy ? '--bg-dialogue-npc-light-start' :
                   theme === Theme.Forest ? '--bg-dialogue-npc-light-start' :
                   theme === Theme.Starry ? '--bg-dialogue-npc-dark-start' :
                   '--bg-dialogue-npc-light-start';
      bgVarEnd = theme === Theme.Dark ? '--bg-dialogue-npc-dark-end' : 
                 theme === Theme.Sakura ? '--bg-dialogue-npc-light-end' :
                 theme === Theme.Candy ? '--bg-dialogue-npc-light-end' :
                 theme === Theme.Forest ? '--bg-dialogue-npc-light-end' :
                 theme === Theme.Starry ? '--bg-dialogue-npc-dark-end' :
                 '--bg-dialogue-npc-light-end';
      
      nameplateBgVarStart = theme === Theme.Dark ? '--npc-nameplate-bg-dark-start' :
                            theme === Theme.Sakura ? '--npc-nameplate-bg-light-start' :
                            theme === Theme.Candy ? '--npc-nameplate-bg-light-start' :
                            theme === Theme.Forest ? '--npc-nameplate-bg-light-start' :
                            theme === Theme.Starry ? '--npc-nameplate-bg-dark-start' :
                            '--npc-nameplate-bg-light-start';
      nameplateBgVarEnd = theme === Theme.Dark ? '--npc-nameplate-bg-dark-end' :
                          theme === Theme.Sakura ? '--npc-nameplate-bg-light-end' :
                          theme === Theme.Candy ? '--npc-nameplate-bg-light-end' :
                          theme === Theme.Forest ? '--npc-nameplate-bg-light-end' :
                          theme === Theme.Starry ? '--npc-nameplate-bg-dark-end' :
                          '--npc-nameplate-bg-light-end';
    }

    const rawColorStart = rootStyle.getPropertyValue(bgVarStart).trim();
    const rawColorEnd = rootStyle.getPropertyValue(bgVarEnd).trim();
    
    const opacityAdjustedColorStart = applyOpacityToRgbaColor(rawColorStart, finalBubbleAlpha);
    const opacityAdjustedColorEnd = applyOpacityToRgbaColor(rawColorEnd, finalBubbleAlpha);

    setDynamicBubbleBackground(`linear-gradient(180deg, ${opacityAdjustedColorStart}, ${opacityAdjustedColorEnd})`);

    if (!isPlayer && !isNarrator) {
      const rawNameplateColorStart = rootStyle.getPropertyValue(nameplateBgVarStart).trim();
      const rawNameplateColorEnd = rootStyle.getPropertyValue(nameplateBgVarEnd).trim();
      // Nameplate opacity also uses finalBubbleAlpha (gameSettings.dialogueBubbleOpacity)
      const opacityAdjustedNameplateStart = applyOpacityToRgbaColor(rawNameplateColorStart, finalBubbleAlpha);
      const opacityAdjustedNameplateEnd = applyOpacityToRgbaColor(rawNameplateColorEnd, finalBubbleAlpha);
      setDynamicNameplateBackground(`linear-gradient(180deg, ${opacityAdjustedNameplateStart}, ${opacityAdjustedNameplateEnd})`);
    }

  }, [theme, gameSettings.dialogueBubbleOpacity, isPlayer, isNarrator]);


  let bubbleBaseClasses = 'p-3 md:p-4 rounded-xl shadow-themed text-sm md:text-base leading-relaxed transition-colors duration-300';
  let bubbleAlignmentClasses = '';
  let textContainerClasses = ''; 
  let bubbleContentWrapperClasses = '';

  if (isPlayer) {
    bubbleBaseClasses += ' rounded-br-none border border-[var(--border-color)]'; 
    bubbleAlignmentClasses = 'self-end';
    textContainerClasses = 'text-[var(--dialogue-player-text)]';
  } else if (isNarrator) {
    bubbleBaseClasses += ' border italic rounded-lg text-left'; 
    bubbleAlignmentClasses = 'self-start w-full md:w-5/6'; 
    textContainerClasses = 'text-[var(--dialogue-narrator-text)]';
    bubbleBaseClasses += ' border-[var(--border-color)]'; 
  } else { // NPC
    bubbleBaseClasses += ' rounded-bl-none border border-[var(--border-color)]'; 
    bubbleAlignmentClasses = 'self-start';
    textContainerClasses = 'text-[var(--dialogue-npc-text)]';
  }
  
  bubbleContentWrapperClasses = isPortrait 
    ? 'dialogue-bubble-content-portrait' 
    : 'max-w-sm md:max-w-lg lg:max-w-xl xl:max-w-2xl';

  const showStandardControls = !isEditingThisLine && (line.speakerType === 'npc' || line.speakerType === 'narrator');
  
  const finalBubbleStyle: React.CSSProperties = {
    background: dynamicBubbleBackground, 
  };
  
  const finalNameplateStyle: React.CSSProperties = {
    background: dynamicNameplateBackground,
    color: 'var(--npc-nameplate-text)',
    border: '1px solid var(--npc-nameplate-border)',
    transition: 'background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease',
  };

  // Ensure blinking cursor is removed for final display if it was part of streaming
  const displayHtml = line.processedHtml.endsWith('▋') ? line.processedHtml.slice(0, -1) : line.processedHtml;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(line.processedHtml);
      addNotification(UIText.dialogueCopiedSuccess, 'success');
    } catch (err) {
      console.error('Failed to copy text: ', err);
      addNotification('复制失败', 'error');
    }
  };

  const handleStartEditClick = () => {
    onStartEdit(line.id, displayHtml);
    setTimeout(() => { // Ensure div is rendered before focusing
        editableDivRef.current?.focus();
    },0);
  };
  
  const handleEditableDivInput = (e: React.FormEvent<HTMLDivElement>) => {
    onCurrentlyEditedContentChange(e.currentTarget.innerHTML);
  };

  return (
    <div className={`w-full flex mb-3 animate-fadeIn ${isPlayer ? 'justify-end' : 'justify-start'}`}>
      <div 
        className={`relative ${bubbleBaseClasses} ${bubbleAlignmentClasses} ${bubbleContentWrapperClasses}`}
        style={finalBubbleStyle} 
      >
        {!isPlayer && !isNarrator && displayName && !isEditingThisLine && (
          <div
            className="inline-block px-3 py-0.5 mb-1.5 rounded-md text-xs font-semibold shadow-sm"
            style={finalNameplateStyle}
          >
            {displayName}
          </div>
        )}

        {isEditingThisLine ? (
          <div className="space-y-2">
            <div
              ref={editableDivRef}
              contentEditable={true}
              suppressContentEditableWarning={true}
              className={`rich-text-content ${textContainerClasses} p-2 border border-accent-themed rounded min-h-[60px] bg-element-themed/30 focus:outline-none focus:ring-1 focus:ring-accent-themed`}
              dangerouslySetInnerHTML={{ __html: currentlyEditedContent }}
              onInput={handleEditableDivInput}
              onBlurCapture={(e) => { // Prevent immediate onBlur save if clicking Save/Cancel
                if (!e.relatedTarget || !e.currentTarget.parentElement?.contains(e.relatedTarget as Node)) {
                     // No default onBlur save, rely on buttons
                }
              }}
            />
            <div className="flex justify-end space-x-2">
              <button onClick={onSaveEdit} className="btn-dreamy btn-dreamy-xs">{UIText.saveGame}</button>
              <button onClick={onCancelEdit} className="btn-dreamy btn-dreamy-xs bg-gray-500/50 hover:bg-gray-600/50">{UIText.cancelAction}</button>
            </div>
          </div>
        ) : (
          <div 
              className={`rich-text-content ${textContainerClasses} pb-2.5`} 
              dangerouslySetInnerHTML={{ __html: displayHtml }}
          />
        )}

        {showStandardControls && (
          <div className={`absolute flex space-x-1 bottom-1 right-1`} style={{ zIndex: 1 }}>
            <button onClick={handleCopy} className="p-1.5 rounded-full hover:bg-element-themed/50 text-secondary-themed hover:text-accent-themed opacity-60 hover:opacity-100 transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-accent-themed" title={UIText.copyDialogueTooltip} aria-label={UIText.copyDialogueTooltip} >
              <Icons.Copy className="w-4 h-4" />
            </button>
            <button onClick={handleStartEditClick} className="p-1.5 rounded-full hover:bg-element-themed/50 text-secondary-themed hover:text-accent-themed opacity-60 hover:opacity-100 transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-accent-themed" title={UIText.editDialogueTooltip} aria-label={UIText.editDialogueTooltip} >
              <Icons.Edit className="w-4 h-4" />
            </button>
            {onRegenerate && (
              <button onClick={() => onRegenerate(line.id)} className="p-1.5 rounded-full hover:bg-element-themed/50 text-secondary-themed hover:text-accent-themed opacity-60 hover:opacity-100 transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-accent-themed" title={UIText.regenerateResponse} aria-label={UIText.regenerateResponseAria} >
                <Icons.ArrowPath className="w-4 h-4" /> 
              </button>
            )}
            {onDeleteLine && (
              <button onClick={() => onDeleteLine(line.id)} className="p-1.5 rounded-full hover:bg-element-themed/50 text-secondary-themed hover:text-red-500 opacity-60 hover:opacity-100 transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-red-500" title={UIText.deleteDialogueTooltip} aria-label={UIText.deleteDialogueTooltip} >
                <Icons.Trash className="w-4 h-4" /> 
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
});

export default DialogueBubble;
