// src/rpgSystem/constants_rpg.ts

import { CoreAttributes_RPG, Skill_RPG, PlayerStatus_RPG } from './types_rpg';
import { UIText } from '@/constants'; // For default values if needed for initial state

export const LEVEL_UP_BASE_XP_RPG = 100;
export const LEVEL_UP_XP_FACTOR_RPG = 1.25;
export const LEVEL_UP_ATTRIBUTE_POINTS_AWARDED_RPG = 1;
export const LEVEL_UP_SKILL_POINTS_AWARDED_RPG = 1;

export const defaultCoreAttributes_RPG: CoreAttributes_RPG = {
  strength: 4,
  agility: 5,
  intelligence: 6,
  charisma: 3,
  luck: 3,
  sanity: 7,
};

export const defaultSkills_RPG: Skill_RPG[] = [
  { id: 'observation', name: "观察", level: 0, description: "提升发现细节、线索和异常情况的能力。", currentXp: 0, xpToNextLevel: 50 },
  { id: 'stealth', name: "潜行", level: 0, description: "提升在不被察觉的情况下行动的能力，避开危险。", currentXp: 0, xpToNextLevel: 60 },
  { id: 'psychology', name: "心理学", level: 0, description: "提升洞察他人（或非人）意图、情绪和精神状态的能力。", currentXp: 0, xpToNextLevel: 70 },
  { id: 'arcana_knowledge', name: "神秘学识", level: 0, description: "提升理解超自然现象、符咒和禁忌知识的能力。", currentXp: 0, xpToNextLevel: 80 },
  { id: 'willpower', name: "意志力", level: 0, description: "提升抵抗恐惧、精神侵蚀和保持理智的能力。", currentXp: 0, xpToNextLevel: 60 },
];

export const InitialPlayerStatus_RPG: PlayerStatus_RPG = {
  healthEnergy: { current: 70, max: 70 },
  // specialEffects_legacy: [], // Deprecated, handled by buffs/debuffs
  currentDay: 1, // Moved from PlayerStatus_Session
  
  inventory: [
      {id: "student_id_card", name: "雨宫澈的学生证", description: "崭新的学生证，上面有你的照片和名字。", quantity: 1},
      {id: "old_notebook", name: "一本陈旧的笔记本", description: "在转校第一天，不知为何出现在书包里的笔记本，封面空白，里面似乎有淡得几乎看不见的字迹。", quantity: 1},
      {id: "school_map_fragment", name: "校园地图碎片", description: "一张部分烧焦的校园地图，只显示了教学楼一楼和部分庭院的布局。", quantity: 1}
  ],
  visitedLocations: [],
  quests: [
    { id: "survive_first_night", title: "第一夜的求生", description: "了解永夜学园的基本生存规则，并安然度过第一个夜晚。", objectives: ["找到宿舍并安全进入。", "在晚上十点后待在宿舍房间内。", "避免触发任何已知的危险规则。"], status: 'active', lastUpdated: Date.now() }
  ],
  characterProfiles: [],
  importantEvents: [
      {id: "transfer_day_unease", text: "转入永夜学园的第一天，黄昏，校园内气氛压抑，几乎空无一人。", timestamp: Date.now(), category: "note", source: "初始设定"}
  ],
  
  level: 1,
  xp: 0,
  xpToNextLevel: LEVEL_UP_BASE_XP_RPG,
  attributePoints: 1,
  skillPoints: 1,
  coreAttributes: { ...defaultCoreAttributes_RPG },
  skills: defaultSkills_RPG.map(skill => ({ ...skill, level: 0, currentXp: 0 })), 
  buffs: [],
  debuffs: [],
};
