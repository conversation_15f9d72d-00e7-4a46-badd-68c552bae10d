import React, { useState } from 'react';
import { Icons } from '../constants';
import { AIProviderModal } from './AIProviderModal';

interface AIProviderButtonProps {
  className?: string;
}

export const AIProviderButton: React.FC<AIProviderButtonProps> = ({ className = '' }) => {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        className={`flex items-center space-x-2 px-4 py-2 bg-accent-themed text-white rounded-lg hover:bg-accent-themed/80 transition-colors ${className}`}
        title="AI提供商设置"
      >
        <Icons.Cog6Tooth className="w-5 h-5" />
        <span className="text-sm font-medium">AI提供商</span>
      </button>

      <AIProviderModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
      />
    </>
  );
};
