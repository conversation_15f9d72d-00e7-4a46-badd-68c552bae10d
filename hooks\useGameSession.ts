
// hooks/useGameSession.ts

import { useState, useEffect, useCallback, useRef } from 'react';
import { Chat, GenerateContentResponse, GenerateContentParameters, Part } from '@google/genai'; // Added Part, GenerateContentParameters, GenerateContentResponse
import { DialogueLine, PlayerStatus, GameSettingsData, GamePhase, NotificationType, Content, CustomNarrativePrimaryElement, LocalStorageKeys, RegexRuleScope } from '../types';
import { createChatSession, sendMessageToGemini, parseGeminiResponse } from '../services/geminiService'; // Updated import
import { generateAnimeImage } from '../services/imageGenerationService';
import { UIText, START_SCREEN_BACKGROUND_KEYWORDS, IMAGE_GENERATION_API_CONFIG, InitialPlayerStatus as defaultPlayerStatus, AUTO_SUMMARY_TURN_INTERVAL, GEMINI_MODEL_TEXT_FALLBACK } from '../constants';
import { renderRichTextStatic, htmlToPlainText, extractTaggedChoicesFromHtml } from '../utils/textUtils'; // Updated to extractTaggedChoicesFromHtml
import { convertDialogueLogToGeminiHistory } from '../utils/geminiUtils';
import { saveToBackgroundCache, getRandomFromBackgroundCache } from '../utils/backgroundCache';
import { ai } from '../services/geminiClient'; // Direct import for re-init
import { findBestChoiceMatch, MatchedChoice, FUZZY_MATCH_THRESHOLD } from '../utils/stringMatcher'; 
import { applyRegexRules } from '../utils/regexUtils';


export const useGameSession = (
  gameSettings: GameSettingsData,
  setGameSettingsHook: (newSettings: Partial<GameSettingsData> | ((prevState: GameSettingsData) => GameSettingsData)) => void,
  playerStatus: PlayerStatus,
  setPlayerStatusHook: React.Dispatch<React.SetStateAction<PlayerStatus>>,
  unlockAchievement: (achievementId: string) => void,
  addNotification: (message: string, type: NotificationType, duration?: number, title?: string) => void,
  processStoryUpdateForRPG: (storyUpdateText: string | undefined, mood?: string, timeOfDay?: string) => void, 
  handleSummarizeAll: (dialogueLog: DialogueLine[], summaryModelId: string) => Promise<void>,
  defaultInitialPlayerStatus: PlayerStatus
) => {
  const [gamePhase, setGamePhase] = useState<GamePhase>(GamePhase.StartScreen);
  const [playerName, setPlayerNameState] = useState<string>(() => {
    try {
      return typeof localStorage !== 'undefined' ? (localStorage.getItem('lastPlayerName_memoryable') || '') : '';
    } catch (e) {
      console.warn("Failed to access localStorage for playerName, using default empty string:", e);
      return '';
    }
  });
  const [dialogueLog, setDialogueLog] = useState<DialogueLine[]>([]);
  const [currentChoices, setCurrentChoices] = useState<string[]>(['','','','']); // Initialize with 4 empty strings
  const chatSessionRef = useRef<Chat | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentSceneImageKeyword, setCurrentSceneImageKeyword] = useState<string>('');
  const [finalBackgroundUrl, setFinalBackgroundUrl] = useState<string>('');
  const [isLoadingBackground, setIsLoadingBackground] = useState<boolean>(true);
  const dialogueTurnCounterRef = useRef(0);
  const streamingAiLineIdRef = useRef<string | null>(null); 
  const lastSummarizedTurnRef = useRef(0); 

  const currentDialogueHistoryForChatRef = useRef<Content[]>([]);

  const [editingLineId, setEditingLineId] = useState<string | null>(null);
  const [currentlyEditedContent, setCurrentlyEditedContent] = useState<string>('');

  useEffect(() => {
    currentDialogueHistoryForChatRef.current = convertDialogueLogToGeminiHistory(dialogueLog);
  }, [dialogueLog]);

  useEffect(() => {
    if (gamePhase === GamePhase.Playing && chatSessionRef.current) {
      chatSessionRef.current = createChatSession(
        playerName,
        gameSettings,
        playerStatus,
        currentDialogueHistoryForChatRef.current 
      );
    }
  }, [
    gameSettings.userRole,
    gameSettings.systemRole,
    JSON.stringify(gameSettings.customNarrativeElements), 
    gameSettings.selectedModelId,
    gameSettings.characterName, gameSettings.characterDescription, gameSettings.characterOpeningMessage, 
    gameSettings.characterPersonality, gameSettings.characterScenario, gameSettings.characterExampleDialogue,
    playerName,
    JSON.stringify(playerStatus), 
    gamePhase,
  ]);


  const setPlayerName = useCallback((name: string) => {
    setPlayerNameState(name);
    try {
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem('lastPlayerName_memoryable', name);
        }
    } catch (e) {
        console.warn("Failed to set playerName in localStorage:", e);
    }
  }, []);

  const fetchAndSetBackgroundImage = useCallback(async (keyword: string) => {
    if (!gameSettings.enableImageGeneration || !keyword || keyword.trim() === "" || keyword.toLowerCase() === 'loading') {
        const cachedUrl = getRandomFromBackgroundCache();
        setFinalBackgroundUrl(cachedUrl || `https://via.placeholder.com/${IMAGE_GENERATION_API_CONFIG.resolution.replace('x','/')}/${gameSettings.enableBackdropBlur ? 'CCCCCC' : 'FFFFFF'}/808080?text=${UIText.loadingScene}`);
        setIsLoadingBackground(false);
        return;
    }
    setIsLoadingBackground(true);
    try {
        const imageUrl = await generateAnimeImage(keyword, gameSettings.selectedImagePromptStyleId);
        setFinalBackgroundUrl(imageUrl);
        saveToBackgroundCache(imageUrl);
    } catch (error: any) {
        console.error("Error generating background image:", error);
        addNotification(UIText.failedGenerateImage(keyword) + `: ${error.message}`, "error");
        const fallbackUrl = `https://via.placeholder.com/${IMAGE_GENERATION_API_CONFIG.resolution.replace('x','/')}/D10000/FFFFFF?text=${encodeURIComponent(UIText.failedGenerateImage(keyword))}`;
        setFinalBackgroundUrl(fallbackUrl);
    } finally {
        setIsLoadingBackground(false);
    }
  }, [gameSettings.enableImageGeneration, gameSettings.selectedImagePromptStyleId, gameSettings.enableBackdropBlur, addNotification]);


  const initializeNewGame = useCallback((
    name: string, 
    settingsToUse: GameSettingsData,
    initialStatus: PlayerStatus,
    initialDialogueLogParam?: DialogueLine[],
    initialSceneKeyword?: string
  ) => {
    setPlayerName(name);
    setGameSettingsHook(settingsToUse); 
    setPlayerStatusHook({ 
        ...defaultInitialPlayerStatus, 
        ...initialStatus, 
        name: name, 
        mood: initialStatus.mood || UIText.mockMood,
        timeOfDay: initialStatus.timeOfDay || UIText.mockTimeOfDay 
    });
    
    let finalInitialDialogueLog: DialogueLine[] = [];
    let historyForChatSession: Content[]; 

    if (!initialDialogueLogParam || initialDialogueLogParam.length === 0) {
      const rawOpeningMessage = settingsToUse.characterOpeningMessage || UIText.dynamicOpeningLineFallback;
      const openingMessage = rawOpeningMessage.replace(/\{\{user\}\}/gi, name);
      const openingDialogueLine: DialogueLine = {
        id: `opening-line-${Date.now()}`,
        speakerName: settingsToUse.characterName || UIText.narrator,
        speakerType: 'npc',
        text: openingMessage, 
        processedHtml: renderRichTextStatic(openingMessage), 
        timestamp: Date.now(),
        storyUpdate: "故事开始",
      };
      finalInitialDialogueLog = [openingDialogueLine];
      historyForChatSession = convertDialogueLogToGeminiHistory([openingDialogueLine]);
      dialogueTurnCounterRef.current = 0; 
      lastSummarizedTurnRef.current = 0; 
    } else {
      finalInitialDialogueLog = initialDialogueLogParam;
      historyForChatSession = convertDialogueLogToGeminiHistory(initialDialogueLogParam);
      dialogueTurnCounterRef.current = initialDialogueLogParam.length; 
      lastSummarizedTurnRef.current = 0; 
    }

    setDialogueLog(finalInitialDialogueLog);
    currentDialogueHistoryForChatRef.current = historyForChatSession; 
    setCurrentChoices(extractTaggedChoicesFromHtml(finalInitialDialogueLog[finalInitialDialogueLog.length - 1]?.processedHtml || ''));
    streamingAiLineIdRef.current = null;
    setEditingLineId(null); 
    setCurrentlyEditedContent(''); 
    
    chatSessionRef.current = createChatSession(name, settingsToUse, initialStatus, historyForChatSession);
    
    const sceneKeyword = initialSceneKeyword || START_SCREEN_BACKGROUND_KEYWORDS[Math.floor(Math.random() * START_SCREEN_BACKGROUND_KEYWORDS.length)];
    setCurrentSceneImageKeyword(sceneKeyword);
    fetchAndSetBackgroundImage(sceneKeyword);
    setGamePhase(GamePhase.Playing);
    unlockAchievement('first_step');
  }, [setPlayerName, setGameSettingsHook, setPlayerStatusHook, fetchAndSetBackgroundImage, unlockAchievement, defaultInitialPlayerStatus]);

  const handleSendMessage = useCallback(async (message: string) => {
    if (!chatSessionRef.current || isLoading) return;
    setIsLoading(true);
    streamingAiLineIdRef.current = null;
    setCurrentChoices(['','','','']); // Clear old choices immediately

    const trimmedMessage = message.trim();
    const messageForUserDisplay = applyRegexRules(trimmedMessage, gameSettings.regexRules.filter(r => r.isActive && r.isDisplayOnly && (r.scope === 'input' || r.scope === 'all')), ['input', 'all']);
    const messageToSendToAI = applyRegexRules(trimmedMessage, gameSettings.regexRules.filter(r => r.isActive && !r.isDisplayOnly && (r.scope === 'input' || r.scope === 'all')), ['input', 'all']);


    const userMessageLine: DialogueLine = {
      id: `msg-${Date.now()}-user`,
      speakerName: playerName,
      speakerType: 'player',
      text: trimmedMessage, // Store raw user input
      processedHtml: renderRichTextStatic(messageForUserDisplay), // Render display-only regex for UI
      timestamp: Date.now(),
    };
    setDialogueLog(prevLog => [...prevLog, userMessageLine]);
    currentDialogueHistoryForChatRef.current = convertDialogueLogToGeminiHistory([...dialogueLog, userMessageLine]);


    const useTrueStreamingUI = gameSettings.enableStreamMode && !gameSettings.enablePseudoStreamMode;
    let tempAiLineId = '';

    if (useTrueStreamingUI) {
      tempAiLineId = `streaming-ai-${Date.now()}`;
      streamingAiLineIdRef.current = tempAiLineId;
      const placeholderAiLine: DialogueLine = {
        id: tempAiLineId,
        speakerName: UIText.thinking.slice(0, 3),
        speakerType: 'npc',
        text: '',
        processedHtml: '▋',
        timestamp: Date.now(),
      };
      setDialogueLog(prevLog => [...prevLog, placeholderAiLine]);
    }

    const handleStreamChunk = (chunkText: string) => { 
      if (useTrueStreamingUI && streamingAiLineIdRef.current) {
        setDialogueLog(prevLog =>
          prevLog.map(line => {
            if (line.id === streamingAiLineIdRef.current) {
              const newRawText = line.text + chunkText;
              // Apply display-only regex rules to the incrementally built text for display
              const displayChunkText = applyRegexRules(newRawText, gameSettings.regexRules.filter(r => r.isActive && r.isDisplayOnly && (r.scope === 'output' || r.scope === 'all')), ['output', 'all']);
              const processedChunkHtml = renderRichTextStatic(displayChunkText);
              return {
                ...line,
                text: newRawText, // Store cumulative raw text
                processedHtml: processedChunkHtml + '▋', // Display processed text
              };
            }
            return line;
          })
        );
      }
    };

    try {
      const currentChatSession = chatSessionRef.current;
      if (!currentChatSession) {
        throw new Error("Chat session is not available.");
      }

      const rawAggregatedResponseText = await sendMessageToGemini(
        currentChatSession,
        messageToSendToAI, // Send AI-processed message
        gameSettings, 
        addNotification,
        useTrueStreamingUI ? handleStreamChunk : undefined
      );

      const geminiResponse = parseGeminiResponse(rawAggregatedResponseText, playerName, addNotification);
      
      if (geminiResponse.speakerName === "{{char}}") {
          geminiResponse.speakerName = gameSettings.characterName || UIText.narrator;
      }

      let finalAiLineText = geminiResponse.dialogue; // This now includes embedded choices if AI generates them per prompt
      
      // Apply all output regex rules (display-only and non-display-only) for the final processed HTML
      const finalProcessedHtml = renderRichTextStatic(
        applyRegexRules(finalAiLineText, gameSettings.regexRules.filter(r => r.isActive && (r.scope === 'output' || r.scope === 'all')), ['output', 'all'])
      );
      // Apply non-display-only output regex rules for the raw text to be stored (affecting logic like choice extraction)
      const logicalAiLineText = applyRegexRules(finalAiLineText, gameSettings.regexRules.filter(r => r.isActive && !r.isDisplayOnly && (r.scope === 'output' || r.scope === 'all')), ['output', 'all']);


      let finalAiLine: DialogueLine;

      if (useTrueStreamingUI && streamingAiLineIdRef.current) {
        const finalAiLineId = streamingAiLineIdRef.current;
        let updatedLog: DialogueLine[] | undefined;
        setDialogueLog(prevLog => {
            updatedLog = prevLog.map(line => {
                if (line.id === finalAiLineId) {
                finalAiLine = {
                    ...line,
                    id: `msg-${Date.now()}-ai-final`,
                    speakerName: geminiResponse.speakerName,
                    speakerType: geminiResponse.speakerType,
                    text: logicalAiLineText, // Store logically processed text
                    processedHtml: finalProcessedHtml, 
                    timestamp: line.timestamp,
                    storyUpdate: geminiResponse.storyUpdate,
                    storyUpdateForSummary: geminiResponse.storyUpdate, 
                };
                return finalAiLine;
                }
                return line;
            });
            return updatedLog;
        });
        if (updatedLog) currentDialogueHistoryForChatRef.current = convertDialogueLogToGeminiHistory(updatedLog);

      } else {
        finalAiLine = {
          id: `msg-${Date.now()}-ai`,
          speakerName: geminiResponse.speakerName,
          speakerType: geminiResponse.speakerType,
          text: logicalAiLineText, // Store logically processed text
          processedHtml: finalProcessedHtml, 
          timestamp: Date.now(),
          storyUpdate: geminiResponse.storyUpdate,
          storyUpdateForSummary: geminiResponse.storyUpdate, 
        };
        setDialogueLog(prevLog => {
            const newLog = [...prevLog, finalAiLine];
            currentDialogueHistoryForChatRef.current = convertDialogueLogToGeminiHistory(newLog);
            return newLog;
        });
      }
      streamingAiLineIdRef.current = null;

      if (finalAiLineText.includes(UIText.errorNarratorConfused) ||
          finalAiLineText.includes(UIText.errorSpiritsFuzzy) ||
          finalAiLineText.includes(UIText.errorEmptyResponse)) {
        addNotification(UIText.errorGeneric + " AI 响应解析可能不完整或为空。", 'warning', 7000);
      }

      setCurrentChoices(extractTaggedChoicesFromHtml(finalProcessedHtml)); // Extract choices from fully processed HTML
      processStoryUpdateForRPG(geminiResponse.storyUpdate, geminiResponse.mood, geminiResponse.timeOfDay);
      
      if (geminiResponse.sceneImageKeyword && geminiResponse.sceneImageKeyword.toLowerCase() !== "keep_current" && geminiResponse.sceneImageKeyword !== currentSceneImageKeyword) {
        setCurrentSceneImageKeyword(geminiResponse.sceneImageKeyword);
      }
      
      dialogueTurnCounterRef.current += 1; 
      
      if (dialogueLog.length > 25) unlockAchievement('story_lover');

    } catch (error) {
      console.error("Error in handleSendMessage:", error);
      addNotification(UIText.errorGeneric, 'error');
      if (streamingAiLineIdRef.current) {
        setDialogueLog(prev => prev.filter(line => line.id !== streamingAiLineIdRef.current));
        streamingAiLineIdRef.current = null;
      }
    } finally {
      setIsLoading(false);
    }
  }, [
    isLoading, playerName, gameSettings, addNotification, processStoryUpdateForRPG, 
    currentSceneImageKeyword, handleSummarizeAll, dialogueLog, unlockAchievement, 
    setDialogueLog, setCurrentChoices, setCurrentSceneImageKeyword 
  ]);

  const handleDeleteDialogueLine = useCallback((lineIdToDelete: string) => {
    let lineIndexToDelete = -1;
    const newDialogueLog = dialogueLog.filter((line, index) => {
        if (line.id === lineIdToDelete) {
            lineIndexToDelete = index;
            return false;
        }
        return true;
    });

    if (lineIndexToDelete === -1 || lineIndexToDelete === 0) { 
        addNotification("无法删除此消息。", "warning");
        return;
    }

    const truncatedLog = newDialogueLog.slice(0, lineIndexToDelete);
    
    setDialogueLog(truncatedLog);
    const geminiHistory = convertDialogueLogToGeminiHistory(truncatedLog);
    currentDialogueHistoryForChatRef.current = geminiHistory; 
    
    if (truncatedLog.length < lastSummarizedTurnRef.current) {
        lastSummarizedTurnRef.current = 0; 
    }
    dialogueTurnCounterRef.current = truncatedLog.length;
    
    chatSessionRef.current = createChatSession(playerName, gameSettings, playerStatus, geminiHistory);
    
    addNotification(UIText.dialogueDeleted, 'success');
    setCurrentChoices(extractTaggedChoicesFromHtml(truncatedLog[truncatedLog.length - 1]?.processedHtml || ''));
    streamingAiLineIdRef.current = null; 
    setEditingLineId(null); 
    setCurrentlyEditedContent('');
  }, [dialogueLog, playerName, gameSettings, playerStatus, addNotification]);

  const handleRegenerateResponse = useCallback(async () => {
    if (isLoading || dialogueLog.length < 1) { 
        addNotification(UIText.regenerateErrorInvalidLine, "warning");
        return;
    }
    
    let lastPlayerInputIndex = -1;
    for (let i = dialogueLog.length - 1; i >= 0; i--) {
        if (dialogueLog[i].speakerType === 'player') {
            lastPlayerInputIndex = i;
            break;
        }
    }

    if (lastPlayerInputIndex === -1) {
        addNotification(UIText.regenerateErrorInputNotFound, "error");
        return;
    }
    
    const lastPlayerMessage = dialogueLog[lastPlayerInputIndex].text; // Use raw text for regeneration
    const logForRegeneration = dialogueLog.slice(0, lastPlayerInputIndex);

    setDialogueLog(logForRegeneration); 
    const geminiHistory = convertDialogueLogToGeminiHistory(logForRegeneration);
    currentDialogueHistoryForChatRef.current = geminiHistory; 
    
    if (logForRegeneration.length < lastSummarizedTurnRef.current) {
        lastSummarizedTurnRef.current = 0;
    }
    dialogueTurnCounterRef.current = logForRegeneration.length;
    
    chatSessionRef.current = createChatSession(playerName, gameSettings, playerStatus, geminiHistory);
    streamingAiLineIdRef.current = null; 
    setEditingLineId(null); 
    setCurrentlyEditedContent('');
    
    addNotification("正在重新生成AI回复...", 'info');
    await handleSendMessage(lastPlayerMessage); 

  }, [isLoading, dialogueLog, playerName, gameSettings, playerStatus, addNotification, handleSendMessage]);

  useEffect(() => {
    if (gamePhase === GamePhase.Playing && dialogueLog.length > 0) {
      if (dialogueTurnCounterRef.current >= AUTO_SUMMARY_TURN_INTERVAL &&
          dialogueTurnCounterRef.current % AUTO_SUMMARY_TURN_INTERVAL === 0 &&
          dialogueLog[dialogueLog.length - 1]?.speakerType !== 'player' &&
          dialogueTurnCounterRef.current !== lastSummarizedTurnRef.current 
      ) {
        handleSummarizeAll(dialogueLog, gameSettings.selectedSummaryModelId || GEMINI_MODEL_TEXT_FALLBACK)
          .then(() => {
            lastSummarizedTurnRef.current = dialogueTurnCounterRef.current; 
          })
          .catch(error => console.error("Error during auto-summary which might affect loop guard:", error));
      }
    }
  }, [dialogueLog, gamePhase, gameSettings.selectedSummaryModelId, handleSummarizeAll]);


  useEffect(() => {
    if (gamePhase === GamePhase.StartScreen && !finalBackgroundUrl) {
        const randomKeyword = START_SCREEN_BACKGROUND_KEYWORDS[Math.floor(Math.random() * START_SCREEN_BACKGROUND_KEYWORDS.length)];
        setCurrentSceneImageKeyword(randomKeyword);
        fetchAndSetBackgroundImage(randomKeyword);
    }
  }, [gamePhase, finalBackgroundUrl, fetchAndSetBackgroundImage]);

  useEffect(() => {
    if (gamePhase === GamePhase.Playing && currentSceneImageKeyword && gameSettings.enableImageGeneration) {
        fetchAndSetBackgroundImage(currentSceneImageKeyword);
    }
  }, [currentSceneImageKeyword, gameSettings.enableImageGeneration, gamePhase, fetchAndSetBackgroundImage]); 


  useEffect(() => {
    if (gamePhase === GamePhase.Playing) {
      const sessionData = {
        finalBackgroundUrl: finalBackgroundUrl,
        currentSceneImageKeyword: currentSceneImageKeyword,
        gameSettingsId: gameSettings.selectedModelId || GEMINI_MODEL_TEXT_FALLBACK, 
        timestamp: Date.now() 
      };
      try {
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem(LocalStorageKeys.LAST_SESSION_DATA, JSON.stringify(sessionData));
        }
      } catch (e) {
        console.warn("Failed to set last session data in localStorage:", e);
      }
    }
  }, [finalBackgroundUrl, currentSceneImageKeyword, gameSettings.selectedModelId, gamePhase]);

  const handleStartEditLine = useCallback((lineId: string, currentHtml: string) => {
    setEditingLineId(lineId);
    setCurrentlyEditedContent(currentHtml);
  }, []);

  const handleSaveEditedLine = useCallback(() => {
    if (!editingLineId) return;

    let newLog: DialogueLine[] = [];
    setDialogueLog(prevLog => {
      newLog = prevLog.map(line => {
        if (line.id === editingLineId) {
          const newText = htmlToPlainText(currentlyEditedContent); // Convert edited HTML back to plain text
          const targetScopes: RegexRuleScope[] = line.speakerType === 'player' ? ['input', 'all'] : ['output', 'all'];
          // Re-apply all relevant rules (display-only and non-display-only) for the new processedHtml
          const reProcessedHtml = renderRichTextStatic(
            applyRegexRules(newText, gameSettings.regexRules.filter(r => r.isActive && targetScopes.includes(r.scope)), targetScopes)
          );
          
          return {
            ...line,
            text: newText,  // Store updated plain text
            processedHtml: reProcessedHtml, // Store re-processed HTML
            timestamp: Date.now(), 
          };
        }
        return line;
      });
      currentDialogueHistoryForChatRef.current = convertDialogueLogToGeminiHistory(newLog);
      // Re-extract choices from the potentially modified AI line
      if (newLog.find(l => l.id === editingLineId)?.speakerType !== 'player') {
        const lastAiLine = newLog.filter(l => l.speakerType !== 'player').pop();
        if (lastAiLine) {
            setCurrentChoices(extractTaggedChoicesFromHtml(lastAiLine.processedHtml));
        }
      }
      return newLog;
    });
    
    addNotification(UIText.dialogueUpdateSuccess, 'success');
    setEditingLineId(null);
    setCurrentlyEditedContent('');
  }, [editingLineId, currentlyEditedContent, setDialogueLog, addNotification, gameSettings.regexRules, gameSettings.enableRegexReplacement]);

  const handleCancelEditLine = useCallback(() => {
    setEditingLineId(null);
    setCurrentlyEditedContent('');
  }, []);

  const handleCurrentlyEditedContentChange = useCallback((newHtml: string) => {
    setCurrentlyEditedContent(newHtml);
  }, []);

  useEffect(() => {
    if (dialogueLog.length === 0 || gamePhase !== GamePhase.Playing) return;
    const activeRules = gameSettings.enableRegexReplacement ? gameSettings.regexRules.filter(rule => rule.isActive) : [];
    setDialogueLog(prevLog => {
      const logLength = prevLog.length;
      const startIndex = Math.max(0, logLength - 5); // Re-process last few lines for efficiency
      let madeChanges = false;
      const updatedLog = prevLog.map((line, index) => {
        if (index >= startIndex) {
          let newProcessedHtml;
          const targetScopes: RegexRuleScope[] = line.speakerType === 'player' ? ['input', 'all'] : ['output', 'all'];
          // Apply all relevant (display and non-display) regex rules for processedHtml
          const reProcessedText = applyRegexRules(line.text, activeRules.filter(rule => targetScopes.includes(rule.scope)), targetScopes);
          newProcessedHtml = renderRichTextStatic(reProcessedText);
          
          if (newProcessedHtml !== line.processedHtml) { 
            madeChanges = true; 
            return { ...line, processedHtml: newProcessedHtml };
          }
        }
        return line;
      });
      // If regex rules changed what choices are visible, re-extract.
      if (madeChanges) {
          const lastAiLine = updatedLog.filter(l => l.speakerType !== 'player').pop();
          if (lastAiLine) {
              setCurrentChoices(extractTaggedChoicesFromHtml(lastAiLine.processedHtml));
          }
      }
      return madeChanges ? updatedLog : prevLog;
    });
  }, [gameSettings.regexRules, gameSettings.enableRegexReplacement, gamePhase]); 

  return {
    gamePhase, setGamePhase, playerName, setPlayerName, dialogueLog, setDialogueLog,
    currentChoices, setCurrentChoices, chatSessionRef, isLoading, setIsLoading,
    currentSceneImageKeyword, setCurrentSceneImageKeyword, finalBackgroundUrl, setFinalBackgroundUrl,
    isLoadingBackground, setIsLoadingBackground, dialogueTurnCounterRef, 
    initializeNewGame, handleSendMessage, handleDeleteDialogueLine, handleRegenerateResponse,
    editingLineId, currentlyEditedContent, handleStartEditLine, handleSaveEditedLine,
    handleCancelEditLine, handleCurrentlyEditedContentChange,
  };
};