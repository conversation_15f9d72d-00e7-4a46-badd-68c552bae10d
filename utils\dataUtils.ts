
import { CustomNarrativePrimaryElement } from '../types';

export const ensureCustomElementsStructure = (elements: any): CustomNarrativePrimaryElement[] => {
    if (!Array.isArray(elements)) return [];
    return elements.map((primaryEl: any, pIndex: number) => ({
        id: primaryEl.id || `primary_${Date.now()}_${pIndex}`,
        name: primaryEl.name || "Unnamed Element Set",
        isActive: typeof primaryEl.isActive === 'boolean' ? primaryEl.isActive : true,
        subElements: Array.isArray(primaryEl.subElements)
            ? primaryEl.subElements.map((subEl: any, sIndex: number) => ({
                id: subEl.id || `sub_${Date.now()}_${pIndex}_${sIndex}`,
                key: subEl.key || "Unnamed Rule",
                value: subEl.value || "",
                isActive: typeof subEl.isActive === 'boolean' ? subEl.isActive : true,
              }))
            : [],
    }));
};
