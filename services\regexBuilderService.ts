import { RegexRule, RegexRuleScope } from '../types';

export interface RegexPattern {
  id: string;
  name: string;
  description: string;
  pattern: string;
  category: 'text-formatting' | 'content-filtering' | 'style-enhancement' | 'custom';
  examples: Array<{
    input: string;
    output: string;
    description: string;
  }>;
}

export interface RegexTestResult {
  matches: Array<{
    match: string;
    index: number;
    groups: string[];
  }>;
  isValid: boolean;
  error?: string;
  transformedText: string;
}

export interface RegexLibraryCategory {
  id: string;
  name: string;
  description: string;
  patterns: RegexPattern[];
}

export class RegexBuilderService {
  private patternLibrary: Map<string, RegexLibraryCategory> = new Map();

  constructor() {
    this.initializePatternLibrary();
  }

  private initializePatternLibrary(): void {
    // Text Formatting Patterns
    this.addPatternCategory({
      id: 'text-formatting',
      name: '文本格式化',
      description: '用于格式化和美化文本显示的正则表达式',
      patterns: [
        {
          id: 'emphasis-bold',
          name: '粗体强调',
          description: '将 **文本** 或 __文本__ 转换为粗体',
          pattern: '(\\*\\*|__)(.*?)\\1',
          category: 'text-formatting',
          examples: [
            {
              input: '这是**重要**的信息',
              output: '这是<strong>重要</strong>的信息',
              description: '双星号转粗体'
            }
          ]
        },
        {
          id: 'emphasis-italic',
          name: '斜体强调',
          description: '将 *文本* 或 _文本_ 转换为斜体',
          pattern: '(\\*|_)(.*?)\\1',
          category: 'text-formatting',
          examples: [
            {
              input: '这是*强调*的文本',
              output: '这是<em>强调</em>的文本',
              description: '单星号转斜体'
            }
          ]
        },
        {
          id: 'whisper-style',
          name: '低语样式',
          description: '为括号内的低语文本添加特殊样式',
          pattern: '(\\([^)]*?[低语私语心想默念]{1,2}[^)]*?\\)|（[^）]*?[低语私语心想默念]{1,2}[^）]*?）)',
          category: 'text-formatting',
          examples: [
            {
              input: '(她心想：这里很奇怪)',
              output: '<em style="color: var(--text-secondary); opacity: 0.75;">(她心想：这里很奇怪)</em>',
              description: '心理活动样式化'
            }
          ]
        }
      ]
    });

    // Content Filtering Patterns
    this.addPatternCategory({
      id: 'content-filtering',
      name: '内容过滤',
      description: '用于移除或替换特定内容的正则表达式',
      patterns: [
        {
          id: 'remove-ai-thinking',
          name: '移除AI思考标签',
          description: '移除AI内部思考过程标签',
          pattern: '<(?:thinking|internal|meta|logicpass|reasoning)>[\\s\\S]*?<\\/(?:thinking|internal|meta|logicpass|reasoning)>',
          category: 'content-filtering',
          examples: [
            {
              input: '<thinking>这是内部思考</thinking>实际回复',
              output: '实际回复',
              description: '移除思考标签'
            }
          ]
        },
        {
          id: 'clean-markdown',
          name: '清理Markdown',
          description: '移除多余的Markdown格式',
          pattern: '#{1,6}\\s*',
          category: 'content-filtering',
          examples: [
            {
              input: '## 标题文本',
              output: '标题文本',
              description: '移除标题标记'
            }
          ]
        }
      ]
    });

    // Style Enhancement Patterns
    this.addPatternCategory({
      id: 'style-enhancement',
      name: '样式增强',
      description: '用于增强文本视觉效果的正则表达式',
      patterns: [
        {
          id: 'highlight-rules',
          name: '校规高亮',
          description: '高亮显示校规和警告文本',
          pattern: '(校规第[一二三四五六七八九十零百千两几]+条|警告：|务必记住：|禁止.+?！)',
          category: 'style-enhancement',
          examples: [
            {
              input: '校规第一条：禁止夜间外出！',
              output: '<strong style="color: var(--text-accent); text-shadow: 0 0 3px rgba(var(--text-accent-rgb), 0.5);">校规第一条</strong>：<strong style="color: var(--text-accent); text-shadow: 0 0 3px rgba(var(--text-accent-rgb), 0.5);">禁止夜间外出！</strong>',
              description: '校规文本高亮'
            }
          ]
        },
        {
          id: 'dialogue-quotes',
          name: '对话引号美化',
          description: '美化对话引号的显示',
          pattern: '"([^"]*)"',
          category: 'style-enhancement',
          examples: [
            {
              input: '她说："你好吗？"',
              output: '她说：<span class="dialogue-quote">"你好吗？"</span>',
              description: '对话引号样式化'
            }
          ]
        }
      ]
    });
  }

  private addPatternCategory(category: RegexLibraryCategory): void {
    this.patternLibrary.set(category.id, category);
  }

  // Pattern Library Management
  getPatternLibrary(): RegexLibraryCategory[] {
    return Array.from(this.patternLibrary.values());
  }

  getPatternsByCategory(categoryId: string): RegexPattern[] {
    const category = this.patternLibrary.get(categoryId);
    return category ? category.patterns : [];
  }

  searchPatterns(query: string): RegexPattern[] {
    const results: RegexPattern[] = [];
    const lowerQuery = query.toLowerCase();

    for (const category of this.patternLibrary.values()) {
      for (const pattern of category.patterns) {
        if (
          pattern.name.toLowerCase().includes(lowerQuery) ||
          pattern.description.toLowerCase().includes(lowerQuery) ||
          pattern.pattern.toLowerCase().includes(lowerQuery)
        ) {
          results.push(pattern);
        }
      }
    }

    return results;
  }

  // Regex Testing and Validation
  testRegex(pattern: string, flags: string, testText: string, replacement?: string): RegexTestResult {
    try {
      // Validate flags
      const validFlags = 'gimuy';
      const uniqueFlags = Array.from(new Set(flags.split('')))
        .filter(flag => validFlags.includes(flag))
        .join('');

      const regex = new RegExp(pattern, uniqueFlags);
      const matches: Array<{ match: string; index: number; groups: string[] }> = [];

      let match;
      const globalRegex = new RegExp(pattern, uniqueFlags.includes('g') ? uniqueFlags : uniqueFlags + 'g');
      
      while ((match = globalRegex.exec(testText)) !== null) {
        matches.push({
          match: match[0],
          index: match.index,
          groups: match.slice(1)
        });

        // Prevent infinite loop for zero-length matches
        if (match.index === globalRegex.lastIndex) {
          globalRegex.lastIndex++;
        }
      }

      let transformedText = testText;
      if (replacement !== undefined) {
        transformedText = testText.replace(regex, replacement);
      }

      return {
        matches,
        isValid: true,
        transformedText
      };
    } catch (error) {
      return {
        matches: [],
        isValid: false,
        error: error.message,
        transformedText: testText
      };
    }
  }

  // Rule Generation and Management
  createRuleFromPattern(
    pattern: RegexPattern,
    replacement: string,
    scope: RegexRuleScope,
    isDisplayOnly: boolean = false
  ): RegexRule {
    return {
      id: `rule_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
      name: pattern.name,
      pattern: pattern.pattern,
      replacement,
      flags: 'g',
      scope,
      isActive: true,
      isDisplayOnly,
      description: pattern.description
    };
  }

  validateRule(rule: RegexRule): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate pattern
    try {
      new RegExp(rule.pattern, rule.flags);
    } catch (error) {
      errors.push(`Invalid regex pattern: ${error.message}`);
    }

    // Validate flags
    const validFlags = 'gimuy';
    for (const flag of rule.flags) {
      if (!validFlags.includes(flag)) {
        errors.push(`Invalid flag: ${flag}`);
      }
    }

    // Check for common issues
    if (rule.pattern.includes('.*') && rule.flags.includes('g')) {
      errors.push('Warning: Global flag with .* may cause performance issues');
    }

    if (rule.replacement.includes('$') && !/\$\d+/.test(rule.replacement)) {
      errors.push('Warning: $ in replacement should be followed by a number (e.g., $1, $2)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Advanced Pattern Building
  buildPatternFromComponents(components: {
    prefix?: string;
    core: string;
    suffix?: string;
    modifiers?: {
      caseInsensitive?: boolean;
      multiline?: boolean;
      global?: boolean;
      unicode?: boolean;
      sticky?: boolean;
    };
  }): { pattern: string; flags: string } {
    let pattern = '';
    
    if (components.prefix) {
      pattern += components.prefix;
    }
    
    pattern += components.core;
    
    if (components.suffix) {
      pattern += components.suffix;
    }

    let flags = '';
    if (components.modifiers) {
      if (components.modifiers.global) flags += 'g';
      if (components.modifiers.caseInsensitive) flags += 'i';
      if (components.modifiers.multiline) flags += 'm';
      if (components.modifiers.unicode) flags += 'u';
      if (components.modifiers.sticky) flags += 'y';
    }

    return { pattern, flags };
  }

  // Pattern Optimization
  optimizePattern(pattern: string): { optimized: string; suggestions: string[] } {
    let optimized = pattern;
    const suggestions: string[] = [];

    // Remove redundant escapes
    optimized = optimized.replace(/\\(.)/g, (match, char) => {
      const needsEscape = '.^$*+?{}[]|()\\';
      if (needsEscape.includes(char)) {
        return match;
      } else {
        suggestions.push(`Removed unnecessary escape: \\${char}`);
        return char;
      }
    });

    // Optimize character classes
    optimized = optimized.replace(/\[a-zA-Z\]/g, () => {
      suggestions.push('Replaced [a-zA-Z] with more efficient \\p{L} (if Unicode flag is used)');
      return '[a-zA-Z]'; // Keep original for compatibility
    });

    // Suggest anchors for performance
    if (!pattern.startsWith('^') && !pattern.endsWith('$')) {
      suggestions.push('Consider adding anchors (^ or $) for better performance if matching entire strings');
    }

    return { optimized, suggestions };
  }

  // Export/Import Rules
  exportRules(rules: RegexRule[]): string {
    const exportData = {
      version: '1.0',
      exportedAt: new Date().toISOString(),
      rules: rules.map(rule => ({
        ...rule,
        metadata: {
          created: new Date().toISOString(),
          source: 'MemoryAble Regex Builder'
        }
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }

  importRules(jsonData: string): { rules: RegexRule[]; errors: string[] } {
    const errors: string[] = [];
    const rules: RegexRule[] = [];

    try {
      const data = JSON.parse(jsonData);
      
      if (!data.rules || !Array.isArray(data.rules)) {
        errors.push('Invalid format: rules array not found');
        return { rules, errors };
      }

      for (const ruleData of data.rules) {
        try {
          const rule: RegexRule = {
            id: ruleData.id || `imported_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
            name: ruleData.name || 'Imported Rule',
            pattern: ruleData.pattern || '',
            replacement: ruleData.replacement || '',
            flags: ruleData.flags || 'g',
            scope: ruleData.scope || 'output',
            isActive: ruleData.isActive !== false,
            isDisplayOnly: ruleData.isDisplayOnly || false,
            description: ruleData.description || ''
          };

          const validation = this.validateRule(rule);
          if (validation.isValid) {
            rules.push(rule);
          } else {
            errors.push(`Rule "${rule.name}": ${validation.errors.join(', ')}`);
          }
        } catch (error) {
          errors.push(`Failed to import rule: ${error.message}`);
        }
      }
    } catch (error) {
      errors.push(`JSON parsing failed: ${error.message}`);
    }

    return { rules, errors };
  }
}

// Singleton instance
export const regexBuilderService = new RegexBuilderService();
