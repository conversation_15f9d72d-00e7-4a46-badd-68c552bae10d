
import React, { createContext, useState, useCallback, ReactNode } from 'react';
import { NotificationMessage, NotificationType, NotificationContextType } from '../types';

export const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

const DEFAULT_NOTIFICATION_DURATION = 5000; // 5 seconds

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<NotificationMessage[]>([]);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.filter(notification => notification.id !== id)
    );
  }, []);

  const addNotification = useCallback(
    (message: string, type: NotificationType, duration: number = DEFAULT_NOTIFICATION_DURATION, title?: string) => {
      const id = `notif_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      // For achievement, ensure title is set if not provided
      let finalTitle = title;
      if (type === 'achievement' && !finalTitle) {
        finalTitle = "成就解锁！"; // Default title for achievements
      }

      setNotifications(prevNotifications => [
        ...prevNotifications,
        { id, message, type, duration, title: finalTitle },
      ]);

      const timerId = setTimeout(() => {
        removeNotification(id);
      }, duration); // Notification removed from list after its intended duration.
      
    },
    [removeNotification]
  );

  return (
    <NotificationContext.Provider value={{ notifications, addNotification, removeNotification }}>
      {children}
    </NotificationContext.Provider>
  );
};
