
import React, { useState, useContext, useRef } from 'react';
import { GameSaveData, NotificationType, GameSettingsData } from '../types'; 
import { Icons, UIText } from '../constants';
import { ThemeContext } from '../contexts/ThemeContext';

interface SaveLoadManagerProps {
  saves: GameSaveData[];
  onSave: (name: string) => Promise<string | null>;
  onLoad: (saveId: string) => void;
  onDelete: (saveId: string) => void;
  onRename: (saveId: string, newName: string) => void;
  onUpdateSave?: (saveId: string) => void; 
  isCompact?: boolean; 
  onManageSave?: (save: GameSaveData) => void;
  maxHeight?: string; 
  onRestartFromSave?: (saveId: string) => void;
  addNotification: (message: string, type: NotificationType, duration?: number) => void;
  onRequestConfirmation: (title: string, message: string, onConfirm: () => void, confirmText?: string, cancelText?: string) => void;
  enableBackdropBlur?: boolean;
  
  // Props for Data Management and Reset
  onExportData: () => void;
  onImportData: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onResetAllSettingsToDefaults: () => void;
  onRestartWithSummary?: () => void;
  onDefaultRestartGameRequest?: () => void; 

  // Gist Props
  gameSettings: GameSettingsData; 
  onGameSettingsChange: (newSettings: Partial<GameSettingsData> | ((prevState: GameSettingsData) => GameSettingsData)) => void; 
  isGistLoading?: boolean;
  onInitializeGistBackup?: () => void;
  onBackupToGist?: () => void;
  onRestoreFromGist?: () => void;
}

const SaveLoadManager: React.FC<SaveLoadManagerProps> = ({ 
  saves, 
  onSave, 
  onLoad, 
  onDelete, 
  onRename,
  onUpdateSave,
  isCompact = false,
  onManageSave, 
  maxHeight = "max-h-60",
  onRestartFromSave,
  addNotification,
  onRequestConfirmation,
  enableBackdropBlur = true,
  onExportData,
  onImportData,
  onResetAllSettingsToDefaults,
  onRestartWithSummary,
  onDefaultRestartGameRequest,
  gameSettings, 
  onGameSettingsChange, 
  isGistLoading, 
  onInitializeGistBackup,
  onBackupToGist,
  onRestoreFromGist,
}) => {
  const [saveNameInput, setSaveNameInput] = useState('');
  const [renamingSaveId, setRenamingSaveId] = useState<string | null>(null);
  const [currentRenameValue, setCurrentRenameValue] = useState('');
  const renameInputRef = useRef<HTMLInputElement>(null);
  const importFileRef = useRef<HTMLInputElement>(null); 


  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  const handleInitiateSave = async () => {
    const nameToSave = saveNameInput.trim() || `${UIText.saveGame.split(' ')[0]} - ${new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    await onSave(nameToSave);
    setSaveNameInput('');
  };

  const handleRenameClick = (save: GameSaveData) => {
    setRenamingSaveId(save.id);
    setCurrentRenameValue(save.name);
    setTimeout(() => renameInputRef.current?.focus(), 0);
  };

  const handleConfirmRename = (saveId: string) => {
    if (currentRenameValue.trim()) {
      onRename(saveId, currentRenameValue.trim());
    } else {
      addNotification(UIText.saveErrorInvalidName, "error");
    }
    setRenamingSaveId(null);
    setCurrentRenameValue('');
  };
  
  const handleCancelRename = () => {
    setRenamingSaveId(null);
    setCurrentRenameValue('');
  };
  
  const handleDeleteClick = (saveId: string, saveName: string) => {
    onRequestConfirmation(
      UIText.deleteConfirm("").split("确定要删除存档 \"")[0] + saveName + "\" 吗?", 
      UIText.deleteConfirm(saveName),
      () => onDelete(saveId),
      "确认删除", "取消"
    );
  };

  const handleUpdateClick = (saveId: string) => {
    if (onUpdateSave) onUpdateSave(saveId);
  };

  const handleResetDefaultsClick = () => {
    onRequestConfirmation(
        UIText.resetToDefaults,
        UIText.resetToDefaultsConfirmation,
        onResetAllSettingsToDefaults,
        UIText.confirmReset,
        UIText.cancelAction
    );
  };

  const handleRestartWithSummaryClick = () => {
    if (onRestartWithSummary) {
      onRestartWithSummary();
    }
  };
  
  const handleDefaultRestartClick = () => {
    if (onDefaultRestartGameRequest) {
        onDefaultRestartGameRequest();
    }
  };

  const getButtonStyle = (isLoading?: boolean, isDisabledByConfig?: boolean) => 
    `btn-dreamy btn-dreamy-xs flex items-center justify-center ${ (isLoading || isDisabledByConfig) ? 'opacity-60 cursor-not-allowed filter grayscale-[50%]' : ''}`;
  
  const mainContainerBaseClasses = "p-3 transition-colors duration-300";
  const mainContainerCompactClasses = ""; 
  const mainContainerFullClasses = `bg-secondary-themed rounded-xl shadow-themed-md ${enableBackdropBlur ? "backdrop-blur-sm" : ""}`;
  
  const isGistConfigured = !!(gameSettings.githubPat && gameSettings.gistId);

  const handleGistSettingChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    let processedValue: string | boolean | number;

    if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    } else if (name === 'gistAutoBackupIntervalHours') {
      processedValue = Math.max(0.25, parseFloat(value) || 1);
    } else {
      processedValue = value;
    }
    onGameSettingsChange({ [name]: processedValue } as Partial<GameSettingsData>);
  };
  
  const inputClass = "w-full p-2 bg-element-themed text-primary-themed rounded-md border border-themed focus:ring-2 focus:ring-accent-themed focus:border-transparent text-sm placeholder-themed";
  const labelClass = "block text-sm font-medium text-primary-themed mb-1";

  return (
    <div className={`${mainContainerBaseClasses} ${isCompact ? mainContainerCompactClasses : mainContainerFullClasses}`}>
      {!isCompact && (
        <>
          <h3 className="text-lg font-semibold text-accent-themed mb-3">
            {UIText.saveLoad}
          </h3>
          <div className="mb-4">
            <input
              type="text"
              value={saveNameInput}
              onChange={(e) => setSaveNameInput(e.target.value)}
              placeholder={UIText.saveNamePlaceholder}
              className="w-full p-2.5 bg-element-themed text-primary-themed rounded-lg ring-accent-themed focus:outline-none placeholder-themed border border-themed text-sm transition-colors duration-300"
              aria-label={UIText.saveNamePlaceholder}
            />
            <button
              onClick={handleInitiateSave}
              className={`mt-2 w-full ${getButtonStyle()} flex items-center justify-center`}
            >
              <Icons.Save className="w-4 h-4 mr-2" /> {UIText.saveGame}
            </button>
          </div>
        </>
      )}

      {saves.length > 0 ? (
        <div className={`space-y-2 ${maxHeight} overflow-y-auto pr-1`}>
          {saves.sort((a, b) => b.timestamp - a.timestamp).map((save) => (
            <div
              key={save.id}
              className={`flex flex-col p-2.5 rounded-lg text-sm border shadow-sm transition-all duration-300 ease-in-out group
                          ${renamingSaveId === save.id ? 'bg-element-themed border-accent-themed ring-2 ring-accent-themed' : 'bg-element-themed/70 border-themed hover:border-accent-themed/70 hover:bg-element-themed'}`}
            >
              <div className="w-full overflow-hidden cursor-pointer mb-1.5" onClick={() => onManageSave ? onManageSave(save) : onLoad(save.id)}>
                {renamingSaveId === save.id ? (
                  <input
                    ref={renameInputRef}
                    type="text"
                    value={currentRenameValue}
                    onChange={(e) => setCurrentRenameValue(e.target.value)}
                    onBlur={() => setTimeout(() => { if (document.activeElement !== renameInputRef.current) handleCancelRename(); }, 0)} 
                    onKeyDown={(e) => { if (e.key === 'Enter') handleConfirmRename(save.id); if (e.key === 'Escape') handleCancelRename(); }}
                    className="w-full p-1 bg-transparent text-primary-themed rounded-md focus:outline-none ring-1 ring-accent-themed"
                  />
                ) : (
                  <>
                    <p className="font-medium text-primary-themed truncate" title={save.name}>{save.name}</p>
                    <p className="text-xs text-secondary-themed">{new Date(save.timestamp).toLocaleString(navigator.language || 'zh-CN', { dateStyle: 'short', timeStyle: 'short' })}</p>
                  </>
                )}
              </div>
              
              <div className="flex space-x-1.5 self-end w-full justify-end">
                {renamingSaveId === save.id ? (
                  <>
                    <button onClick={() => handleConfirmRename(save.id)} title={UIText.confirmRename} className={getButtonStyle()} > <Icons.Save className="w-4 h-4" /> </button>
                    <button onClick={handleCancelRename} title={UIText.cancelRename} className={getButtonStyle()} > <Icons.Close className="w-4 h-4" /> </button>
                  </>
                ) : (
                  <>
                    <button onClick={() => onLoad(save.id)} title={UIText.loadGame} className={getButtonStyle()} aria-label={`${UIText.loadGame} ${save.name}`} > <Icons.Load className="w-4 h-4" /> </button>
                    {onUpdateSave && ( <button onClick={() => handleUpdateClick(save.id)} title={UIText.updateSave} className={getButtonStyle()} aria-label={`${UIText.updateSave} ${save.name}`} > <Icons.ArrowPath className="w-4 h-4" /> </button> )}
                    {onRestartFromSave && !isCompact && ( <button onClick={() => onRestartFromSave(save.id)} title={UIText.restartWithSettings} className={getButtonStyle()} aria-label={`${UIText.restartWithSettings} ${save.name}`} > <Icons.ChatBubble className="w-4 h-4" /> </button> )}
                    <button onClick={() => handleRenameClick(save)} title={UIText.renameSave} className={getButtonStyle()} aria-label={`${UIText.renameSave} ${save.name}`} > <Icons.Pencil className="w-4 h-4" /> </button>
                    <button onClick={() => handleDeleteClick(save.id, save.name)} title={UIText.deleteSave} className={getButtonStyle()} aria-label={`${UIText.deleteSave} ${save.name}`} > <Icons.Trash className="w-4 h-4" /> </button>
                  </>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className={`text-secondary-themed italic text-sm ${isCompact ? 'text-center py-4' : ''}`}>
          {isCompact ? UIText.noSavesOnStartScreen : UIText.noSaves}
        </p>
      )}
      
      {!isCompact && (
        <>
          {/* GitHub Gist Sync Section */}
          {(onInitializeGistBackup || onBackupToGist || onRestoreFromGist) && (
            <div className="mt-4 pt-4 border-t border-themed/30">
              <h4 className="text-md font-semibold text-accent-themed mb-2 flex items-center">
                <Icons.GitHub className="w-5 h-5 mr-2"/> {UIText.gistSettingsTitle}
              </h4>
              <div>
                <label htmlFor="githubPat" className={labelClass}>{UIText.gistPatLabel}</label>
                <input
                  type="password"
                  id="githubPat"
                  name="githubPat"
                  value={gameSettings.githubPat || ''}
                  onChange={handleGistSettingChange}
                  placeholder={UIText.gistPatPlaceholder}
                  className={inputClass}
                  autoComplete="new-password"
                />
              </div>
              <div className="flex items-center mt-2">
                <input
                  type="checkbox"
                  id="saveGithubPat"
                  name="saveGithubPat"
                  checked={gameSettings.saveGithubPat || false}
                  onChange={handleGistSettingChange}
                  className="h-3.5 w-3.5 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed mr-2"
                />
                <label htmlFor="saveGithubPat" className={`${labelClass} !mb-0 text-xs`}>{UIText.gistSavePatLabel}</label>
              </div>
              <div className="mt-2">
                <label htmlFor="gistId" className={labelClass}>{UIText.gistIdLabel}</label>
                <input
                  type="text"
                  id="gistId"
                  name="gistId"
                  value={gameSettings.gistId || ''}
                  onChange={handleGistSettingChange}
                  placeholder={UIText.gistIdPlaceholder}
                  className={`${inputClass} bg-element-themed/50`}
                />
              </div>
              <div className="mt-2">
                <div className="flex items-center justify-between">
                    <label htmlFor="enableGistAutoBackup" className={`${labelClass} !mb-0`}>{UIText.enableGistAutoBackupLabel}</label>
                    <input type="checkbox" id="enableGistAutoBackup" name="enableGistAutoBackup" checked={gameSettings.enableGistAutoBackup || false} onChange={handleGistSettingChange} className="h-4 w-4 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed" />
                </div>
              </div>
               <div className="mt-2">
                <label htmlFor="gistAutoBackupIntervalHours" className={labelClass}>{UIText.gistAutoBackupIntervalLabel}</label>
                <input
                    type="number"
                    id="gistAutoBackupIntervalHours"
                    name="gistAutoBackupIntervalHours"
                    value={gameSettings.gistAutoBackupIntervalHours || 1}
                    onChange={handleGistSettingChange}
                    min="0.25"
                    step="0.25"
                    disabled={!gameSettings.enableGistAutoBackup}
                    className={`${inputClass} ${!gameSettings.enableGistAutoBackup ? 'opacity-50' : ''}`}
                />
              </div>
              <div className="flex items-center mt-2">
                <input
                  type="checkbox"
                  id="gistUseSystemProxy"
                  name="gistUseSystemProxy"
                  checked={gameSettings.gistUseSystemProxy === undefined ? true : gameSettings.gistUseSystemProxy}
                  onChange={handleGistSettingChange}
                  className="h-3.5 w-3.5 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed mr-2"
                />
                <label htmlFor="gistUseSystemProxy" className={`${labelClass} !mb-0 text-xs`}>{UIText.gistUseSystemProxyLabel}</label>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 mt-3">
                {onInitializeGistBackup && (
                  <button onClick={onInitializeGistBackup} className={getButtonStyle(isGistLoading, !gameSettings.githubPat)} disabled={isGistLoading || !gameSettings.githubPat}>
                    <Icons.CloudArrowUp className="w-4 h-4 mr-1.5"/>{UIText.gistInitializeButton}
                  </button>
                )}
                {onBackupToGist && (
                  <button onClick={onBackupToGist} className={getButtonStyle(isGistLoading, !isGistConfigured)} disabled={isGistLoading || !isGistConfigured}>
                    <Icons.ArrowUpTray className="w-4 h-4 mr-1.5"/>{UIText.gistBackupNow}
                  </button>
                )}
                {onRestoreFromGist && (
                  <button onClick={onRestoreFromGist} className={getButtonStyle(isGistLoading, !isGistConfigured)} disabled={isGistLoading || !isGistConfigured}>
                    <Icons.ArrowDownTray className="w-4 h-4 mr-1.5"/>{UIText.gistRestoreLatest}
                  </button>
                )}
              </div>
              {isGistLoading && <p className="text-xs text-accent-themed mt-2 animate-pulse">{UIText.gistActionInProgress}</p>}
              {!gameSettings.githubPat && isGistLoading !==true && (
                 <p className="text-xs text-amber-500 mt-2 p-1.5 bg-amber-500/10 rounded-md border border-amber-500/30">{UIText.gistErrorConfigMissing}</p>
              )}
              {gameSettings.githubPat && !gameSettings.gistId && isGistLoading !==true && (
                 <p className="text-xs text-amber-500 mt-2 p-1.5 bg-amber-500/10 rounded-md border border-amber-500/30">{UIText.gistErrorGistIdMissing}</p>
              )}
            </div>
          )}
          
          <div className="mt-4 pt-4 border-t border-themed/30">
            <h4 className="text-md font-semibold text-accent-themed mb-2">{UIText.dataManagementTitle}</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <button
                onClick={onExportData}
                className={`${getButtonStyle()}`}
              >
                <Icons.ArrowDownTray className="w-4 h-4 mr-1.5"/> {UIText.exportAllData}
              </button>
              <button
                onClick={() => importFileRef.current?.click()}
                className={`${getButtonStyle()}`}
              >
                <Icons.ArrowUpTray className="w-4 h-4 mr-1.5"/> {UIText.importAllData}
              </button>
              <input type="file" ref={importFileRef} onChange={onImportData} accept=".json" className="hidden" />
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t border-themed/30">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {onRestartWithSummary && (
                    <button onClick={handleRestartWithSummaryClick} className={`${getButtonStyle()}`}>
                        <Icons.Sparkles className="w-4 h-4 mr-1.5"/>{UIText.restartWithSummaryButton}
                    </button>
                )}
                {onDefaultRestartGameRequest && (
                     <button onClick={handleDefaultRestartClick} className={`${getButtonStyle()}`}>
                        <Icons.ArrowPath className="w-4 h-4 mr-1.5"/>{UIText.restartWithDefaultSettingsButton}
                    </button>
                )}
              </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SaveLoadManager;
