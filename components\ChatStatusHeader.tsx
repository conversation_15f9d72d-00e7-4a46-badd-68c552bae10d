// components/ChatStatusHeader.tsx

import React from 'react';
import { PlayerStatus, StatusEffect } from '../types';
import { Icons, UIText } from '../constants';

interface StatusHeaderItem {
  id: string;
  icon: React.FC<React.SVGProps<SVGSVGElement>>;
  label: string;
  value: string;
  tooltip: string;
  iconClassName?: string; // Optional class for the icon
}

interface ChatStatusHeaderProps {
  status: PlayerStatus;
  className?: string;
}

const ChatStatusHeader: React.FC<ChatStatusHeaderProps> = ({ status, className = '' }) => {
  const {
    currentDay,
    weather,
    mood,
    timeOfDay,
    healthEnergy,
    xp,
    xpToNextLevel,
    buffs,
    debuffs,
  } = status;

  // Resolve display values using UIText with fallbacks
  const displayMood = UIText.moods[mood as keyof typeof UIText.moods] || mood || UIText.mockMood;
  const displayWeather = UIText.weatherTypes[weather as keyof typeof UIText.weatherTypes] || weather || UIText.mockWeather;
  const displayTimeOfDay = UIText.timeOfDayNames[timeOfDay as keyof typeof UIText.timeOfDayNames] || timeOfDay || UIText.mockTimeOfDay;

  const formatDuration = (turns: number): string => {
    if (turns === -1) return UIText.effectDurationPermanent;
    return UIText.effectDurationTurns(turns);
  };

  const activeBuffs = buffs?.filter(b => b.remainingTurns !== 0) || [];
  const activeDebuffs = debuffs?.filter(d => d.remainingTurns !== 0) || [];

  const formatEffectTooltip = (effects: StatusEffect[], title: string): string => {
    if (!effects || effects.length === 0) return title;
    return `${title}: ${effects.map(eff => `${eff.name} (${formatDuration(eff.remainingTurns)})`).join('; ')}`;
  };
  
  const baseItems: StatusHeaderItem[] = [
    { id: 'day', icon: Icons.CalendarDays, label: UIText.currentDayLabel.split("{day}")[0].trim(), value: `${currentDay}${UIText.currentDayLabel.includes('天') ? '天' : ''}`, tooltip: `${UIText.currentDayLabel.split("{day}")[0].trim()}: ${currentDay}${UIText.currentDayLabel.includes('天') ? '天' : ''}` },
    { id: 'time', icon: Icons.Clock, label: UIText.timeOfDayLabel, value: displayTimeOfDay, tooltip: `${UIText.timeOfDayLabel}: ${displayTimeOfDay}` },
    { id: 'weather', icon: Icons.Sun, label: UIText.weatherLabel, value: displayWeather, tooltip: `${UIText.weatherLabel}: ${displayWeather}` },
    { id: 'mood', icon: Icons.FaceNeutral, label: UIText.moodLabel, value: displayMood, tooltip: `${UIText.moodLabel}: ${displayMood}` },
    { id: 'hp', icon: Icons.Heart, label: UIText.healthEnergyLabel.replace('值',''), value: `${healthEnergy?.current || 0}/${healthEnergy?.max || 0}`, tooltip: `${UIText.healthEnergyLabel}: ${healthEnergy?.current || 0}/${healthEnergy?.max || 0}` },
    { id: 'xp', icon: Icons.Sparkles, label: UIText.xpLabel.replace('值',''), value: `${xp || 0}/${xpToNextLevel || 0}`, tooltip: `${UIText.xpLabel}: ${xp || 0}/${xpToNextLevel || 0}` },
  ];

  const dynamicItems: StatusHeaderItem[] = [...baseItems];

  if (activeBuffs.length > 0) {
    dynamicItems.push({
        id: 'buffs',
        icon: Icons.ShieldCheck,
        value: activeBuffs.length.toString(),
        tooltip: formatEffectTooltip(activeBuffs, UIText.buffsSectionTitle),
        iconClassName: 'text-green-500',
        label: UIText.buffsSectionTitle,
    });
  }

  if (activeDebuffs.length > 0) {
    dynamicItems.push({
        id: 'debuffs',
        icon: Icons.ShieldExclamation,
        value: activeDebuffs.length.toString(),
        tooltip: formatEffectTooltip(activeDebuffs, UIText.debuffsSectionTitle),
        iconClassName: 'text-red-500',
        label: UIText.debuffsSectionTitle,
    });
  }

  return (
    <div
      className={`flex flex-wrap items-center justify-start gap-x-2 gap-y-1 p-2 text-xs border-b border-themed/30 mb-2 ${className}`}
      role="toolbar"
      aria-label="Player status bar"
    >
      {dynamicItems.map((item, index) => (
        <React.Fragment key={item.id}>
          <div 
            className="flex items-center text-secondary-themed whitespace-nowrap" 
            title={item.tooltip || `${item.label}: ${item.value}`}
            aria-label={`${item.label}: ${item.value}`}
          >
            <item.icon className={`w-3.5 h-3.5 mr-1 opacity-90 flex-shrink-0 ${item.iconClassName || ''}`} />
            <span className="font-medium text-primary-themed/90">{item.value}</span>
          </div>
          {index < dynamicItems.length - 1 && <span className="mx-1.5 opacity-50" aria-hidden="true">|</span>}
        </React.Fragment>
      ))}
    </div>
  );
};

export default ChatStatusHeader;