

import { BackupData, NotificationType, GistApiResponse, GistCreatePayload, GistUpdatePayload, GistFile, GameSettingsData } from '../types';
import { GIST_BACKUP_FILENAME, UIText } from '../constants';

const GITHUB_API_BASE_URL = 'https://api.github.com';
const GIST_DESCRIPTION_TAG = 'MemoryAble Backup Gist'; // Used to identify our backup gist

// Placeholder for a system proxy. In a real app, this would be configured.
// For example, a common public CORS proxy (use with caution and understanding of security implications).
// const PROXY_URL_PREFIX = 'https://cors-anywhere.herokuapp.com/';
// For this exercise, we'll use a simpler, non-functional placeholder to demonstrate the logic.
const PROXY_URL_PREFIX = 'https://system-proxy.invalid/'; // This URL will not work.


const makeGistRequest = async <T>(
  endpoint: string,
  method: 'GET' | 'POST' | 'PATCH' | 'DELETE',
  pat: string,
  gameSettings: GameSettingsData, // Added gameSettings
  body?: any,
  isRawText?: boolean // For downloading raw file content
): Promise<T> => {
  const headers: HeadersInit = {
    'Accept': 'application/vnd.github.v3+json',
    'Authorization': `token ${pat}`,
    'X-GitHub-Api-Version': '2022-11-28',
  };
  if (method !== 'GET' && method !== 'DELETE' && body && !isRawText) {
    headers['Content-Type'] = 'application/json';
  }

  let actualApiBaseUrl = GITHUB_API_BASE_URL;
  // Conditionally use proxy. Default to true if undefined.
  const useProxy = gameSettings.gistUseSystemProxy === undefined ? true : gameSettings.gistUseSystemProxy;

  if (useProxy) {
    // This is a conceptual demonstration. A real proxy integration is more complex.
    // We are simply prefixing the URL. Many proxies might require specific headers or different URL structures.
    // For example, if GITHUB_API_BASE_URL is 'https://api.github.com', the proxied URL becomes
    // 'https://system-proxy.invalid/https://api.github.com'.
    // This specific example will likely fail network requests but shows the conditional logic.
    actualApiBaseUrl = PROXY_URL_PREFIX + GITHUB_API_BASE_URL.replace(/^https?:\/\//, '');
    console.info("Using Gist proxy:", actualApiBaseUrl);
  }


  const response = await fetch(`${actualApiBaseUrl}${endpoint}`, {
    method: method,
    headers: headers,
    body: body && !isRawText ? JSON.stringify(body) : body,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: response.statusText }));
    const errorMessage = errorData.message || `GitHub API Error: ${response.status}`;
    console.error(`Gist API Error (${method} ${actualApiBaseUrl}${endpoint}): ${response.status}`, errorData);
    if (response.status === 401 || response.status === 403) {
        throw new Error(UIText.gistErrorAuth + ` (${errorMessage})`);
    }
    throw new Error(errorMessage);
  }

  if (isRawText) {
      return response.text() as Promise<T>;
  }
  if (response.status === 204) { // No content for PATCH, DELETE success
    return null as T;
  }
  return response.json() as Promise<T>;
};

export const findOrCreateBackupGist = async (
  pat: string,
  backupFilename: string,
  addNotification: (message: string, type: NotificationType, duration?: number) => void,
  gameSettings: GameSettingsData // Pass gameSettings
): Promise<string | null> => {
  try {
    // 1. Try to find a gist with the specific description or filename
    const gists = await makeGistRequest<GistApiResponse[]>('/gists', 'GET', pat, gameSettings);
    for (const gist of gists) {
      if (gist.description === GIST_DESCRIPTION_TAG && gist.files && gist.files[backupFilename]) {
        addNotification(UIText.gistLocated(gist.id), 'info');
        return gist.id;
      }
      // Fallback: if a gist has the backup file but not the description (e.g., user created it manually)
      if (gist.files && gist.files[backupFilename] && !gist.description) {
         addNotification(UIText.gistLocated(gist.id) + " (文件名匹配)", 'info');
         return gist.id;
      }
    }

    // 2. If not found, create a new secret gist
    const initialContent = JSON.stringify({
      appName: "MemoryAble",
      createdAt: new Date().toISOString(),
      note: "This Gist is used for MemoryAble application backups.",
    }, null, 2);

    const createPayload: GistCreatePayload = {
      description: GIST_DESCRIPTION_TAG,
      public: false, // Create as secret
      files: {
        [backupFilename]: {
          content: initialContent,
        },
      },
    };
    const newGist = await makeGistRequest<GistApiResponse>('/gists', 'POST', pat, gameSettings, createPayload);
    addNotification(UIText.gistCreated(newGist.id), 'success');
    return newGist.id;

  } catch (error: any) {
    console.error("Error finding or creating Gist:", error);
    addNotification(error.message || UIText.gistInitFailed, 'error', 7000);
    return null;
  }
};

export const uploadToGist = async (
  pat: string,
  gistId: string,
  backupFilename: string,
  backupData: BackupData,
  addNotification: (message: string, type: NotificationType, duration?: number) => void,
  gameSettings: GameSettingsData, // Pass gameSettings
  isAutoBackup: boolean = false 
): Promise<boolean> => {
  const updatePayload: GistUpdatePayload = {
    files: {
      [backupFilename]: {
        content: JSON.stringify(backupData, null, 2),
      },
    },
  };

  try {
    await makeGistRequest<void>(`/gists/${gistId}`, 'PATCH', pat, gameSettings, updatePayload);
    if (!isAutoBackup) {
      addNotification(UIText.gistBackupSuccess, 'success');
    }
    return true;
  } catch (error: any) {
    console.error("Error uploading to Gist:", error);
    addNotification(error.message || UIText.gistBackupFailed, 'error', 7000);
    return false;
  }
};

export const downloadFromGist = async (
  pat: string,
  gistId: string,
  backupFilename: string,
  addNotification: (message: string, type: NotificationType, duration?: number) => void,
  gameSettings: GameSettingsData // Pass gameSettings
): Promise<BackupData | null> => {
  try {
    const gist = await makeGistRequest<GistApiResponse>(`/gists/${gistId}`, 'GET', pat, gameSettings);
    const fileData = gist.files[backupFilename];

    if (!fileData || !fileData.raw_url) {
      addNotification(`${UIText.gistRestoreFailed} (备份文件 "${backupFilename}" 在Gist中未找到)`, 'warning');
      return null;
    }
    
    let fileContentString: string;
    let actualRawUrl = fileData.raw_url;
    const useProxy = gameSettings.gistUseSystemProxy === undefined ? true : gameSettings.gistUseSystemProxy;

    if (useProxy) {
        // For raw_url, the proxy logic might be different than API calls.
        // Often, raw content URLs (like raw.githubusercontent.com) might not need the same API proxy.
        // However, for consistency in this example, we'll apply a similar prefixing.
        // This might need adjustment for real-world proxies.
        actualRawUrl = PROXY_URL_PREFIX + actualRawUrl.replace(/^https?:\/\//, '');
        console.info("Using Gist proxy for raw download:", actualRawUrl);
    }


    if (fileData.truncated || !fileData.content) {
        // Raw URLs usually don't need the same PAT auth if the Gist is public or the PAT has repo scope for private gists owned by user.
        // However, for consistency and if the proxy needs auth, it's complex.
        // For simplicity here, we'll assume direct fetch for raw_url or proxied direct fetch.
        const response = await fetch(actualRawUrl); 
        if (!response.ok) {
            throw new Error(`Failed to fetch raw Gist content from ${actualRawUrl}: ${response.statusText}`);
        }
        fileContentString = await response.text();
    } else {
        fileContentString = fileData.content;
    }

    const backupData = JSON.parse(fileContentString) as BackupData;
    addNotification(UIText.gistRestoreSuccess.split('！')[0], 'success');
    return backupData;

  } catch (error: any) {
    console.error("Error downloading from Gist:", error);
     addNotification(error.message || UIText.gistRestoreFailed, 'error', 7000);
    return null;
  }
};