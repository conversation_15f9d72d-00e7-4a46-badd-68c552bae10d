

import { useState, useEffect, useCallback } from 'react';
import { GameSettingsData, SettingPreset, PresetType, CustomNarrativePrimaryElement, LocalStorageKeys, NotificationType, CharacterCardData } from '../types';
import { UIText, DefaultGameSettings, GEMINI_MODEL_TEXT_FALLBACK } from '../constants';
import { ensureCustomElementsStructure } from '../utils/dataUtils';

export const usePresets = (
  currentGameSettings: GameSettingsData,
  setGameSettings: (newSettings: Partial<GameSettingsData> | ((prevState: GameSettingsData) => GameSettingsData)) => void,
  addNotification: (message: string, type: NotificationType, duration?: number) => void,
  unlockAchievement: (achievementId: string) => void
) => {
  const [characterCardPresets, setCharacterCardPresets] = useState<SettingPreset[]>(() => JSON.parse(localStorage.getItem(LocalStorageKeys.CHARACTER_CARD_PRESETS) || '[]'));
  const [userRolePresets, setUserRolePresets] = useState<SettingPreset[]>(() => JSON.parse(localStorage.getItem(LocalStorageKeys.USER_ROLE_PRESETS) || '[]'));
  const [aiStylePresets, setAiStylePresets] = useState<SettingPreset[]>(() => JSON.parse(localStorage.getItem(LocalStorageKeys.AI_STYLE_PRESETS) || '[]'));
  // Removed customElementsPresets state

  useEffect(() => { localStorage.setItem(LocalStorageKeys.CHARACTER_CARD_PRESETS, JSON.stringify(characterCardPresets)); }, [characterCardPresets]);
  useEffect(() => { localStorage.setItem(LocalStorageKeys.USER_ROLE_PRESETS, JSON.stringify(userRolePresets)); }, [userRolePresets]);
  useEffect(() => { localStorage.setItem(LocalStorageKeys.AI_STYLE_PRESETS, JSON.stringify(aiStylePresets)); }, [aiStylePresets]);
  // Removed useEffect for customElementsPresets

  const savePreset = useCallback((type: PresetType, value: string | CustomNarrativePrimaryElement[] | CharacterCardData, name: string) => {
    const newPreset: SettingPreset = { id: `${type}-${Date.now()}`, name, value, type };
    switch (type) {
      case 'characterCard': setCharacterCardPresets(prev => [...prev, newPreset]); break;
      case 'userRole': setUserRolePresets(prev => [...prev, newPreset]); break;
      case 'systemRole': setAiStylePresets(prev => [...prev, newPreset]); break;
      // Removed 'customElementsCollection' case
    } 
    unlockAchievement('preset_collector_light');
  }, [unlockAchievement]);

  const loadPreset = useCallback((type: PresetType, presetId: string) => {
    let presets: SettingPreset[];
    switch (type) { 
      case 'characterCard': presets = characterCardPresets; break;
      case 'userRole': presets = userRolePresets; break; 
      case 'systemRole': presets = aiStylePresets; break; 
      // Removed 'customElementsCollection' case
      default: return; 
    }
    const preset = presets.find(p => p.id === presetId);
    if (preset) {
      if (typeof preset.value === 'string') { 
        let fieldToUpdate: keyof GameSettingsData | null = null;
        if (type === 'userRole') fieldToUpdate = 'userRole';
        else if (type === 'systemRole') fieldToUpdate = 'systemRole';
        
        if (fieldToUpdate) {
            setGameSettings(prev => ({ ...prev, [fieldToUpdate!]: preset.value }));
        }
      } else if (type === 'characterCard' && typeof preset.value === 'object' && !Array.isArray(preset.value)) {
        const charCardData = preset.value as CharacterCardData;
        setGameSettings(prev => ({
          ...prev,
          characterName: charCardData.characterName,
          characterDescription: charCardData.characterDescription,
          characterOpeningMessage: charCardData.characterOpeningMessage,
          characterPersonality: charCardData.characterPersonality,
          characterScenario: charCardData.characterScenario,
          characterExampleDialogue: charCardData.characterExampleDialogue,
          characterPortraitKeywords: charCardData.characterPortraitKeywords,
        }));
      }
      // Removed logic for 'customElementsCollection' loading
      addNotification(UIText.presetLoadedSuccess(preset.name), 'success');
    }
  }, [characterCardPresets, userRolePresets, aiStylePresets, addNotification, setGameSettings]); 
  
  const deletePreset = useCallback((type: PresetType, presetId: string) => {
    switch (type) { 
      case 'characterCard': setCharacterCardPresets(prev => prev.filter(p => p.id !== presetId)); break;
      case 'userRole': setUserRolePresets(prev => prev.filter(p => p.id !== presetId)); break; 
      case 'systemRole': setAiStylePresets(prev => prev.filter(p => p.id !== presetId)); break; 
      // Removed 'customElementsCollection' case
    }
  }, []);

  return {
    characterCardPresets, userRolePresets, aiStylePresets,
    setCharacterCardPresets, setUserRolePresets, setAiStylePresets, 
    // Removed customElementsPresets and its setter from return
    savePreset, loadPreset, deletePreset,
  };
};