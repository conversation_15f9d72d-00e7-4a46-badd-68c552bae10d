
import React, { useContext } from 'react';
import { Icons, UIText } from '../constants';
import { ThemeContext } from '../contexts/ThemeContext';

interface ConfirmationModalProps {
  isOpen: boolean;
  message?: string; // Made optional
  children?: React.ReactNode; // Added children prop
  title?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  enableBackdropBlur?: boolean;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  message,
  children,
  title,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  enableBackdropBlur = true,
}) => {
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found in ConfirmationModal");

  if (!isOpen) {
    return null;
  }
  
  const mainBgClasses = "fixed inset-0 bg-black/60 flex items-center justify-center z-[70] p-4";
  const mainBgBlurClass = enableBackdropBlur ? "backdrop-blur-sm" : "";
  const contentAreaId = "confirmation-modal-content-area";

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    onCancel(); // Close the modal after confirmation
  };

  return (
    <div
      className={`${mainBgClasses} ${mainBgBlurClass}`}
      onClick={onCancel} 
      role="dialog"
      aria-modal="true"
      aria-labelledby="confirmation-modal-title"
      aria-describedby={contentAreaId} // Use a consistent ID for description
    >
      <div
        className="bg-secondary-themed p-5 md:p-6 rounded-xl shadow-themed-xl w-full max-w-md border border-themed"
        onClick={(e) => e.stopPropagation()} 
      >
        <div className="flex justify-between items-center mb-4">
          <h3 id="confirmation-modal-title" className="text-lg font-semibold text-accent-themed">
            {title || "确认操作"}
          </h3>
          <button
            onClick={onCancel}
            className="p-1 text-primary-themed hover:text-accent-themed rounded-md hover:bg-element-themed/50 transition-colors"
            aria-label={UIText.closeModal}
          >
            <Icons.Close className="w-5 h-5" />
          </button>
        </div>
        
        <div id={contentAreaId} className="text-primary-themed mb-6 text-sm">
          {children ? (
            children
          ) : message ? (
            <p className="whitespace-pre-wrap">{message}</p>
          ) : null}
        </div>

        <div className="flex justify-end space-x-3">
          {cancelText !== "" && ( // Conditionally render cancel button
            <button
              onClick={onCancel}
              className="btn-dreamy btn-dreamy-sm"
            >
              {cancelText || UIText.cancelAction} 
            </button>
          )}
          <button
            onClick={handleConfirm}
            className="btn-dreamy btn-dreamy-sm"
          >
            {confirmText || UIText.confirmAction}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;
