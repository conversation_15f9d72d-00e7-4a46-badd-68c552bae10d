

import { useState, useEffect, useCallback } from 'react';
import { GameSaveData, GameSettingsData, DialogueLine, PlayerStatus, LocalStorageKeys, NotificationType, Content, CharacterCardData } from '../types';
import { UIText, InitialPlayerStatus, DefaultGameSettings, GEMINI_MODEL_TEXT_FALLBACK } from '../constants';
import { ensureCustomElementsStructure } from '../utils/dataUtils';
import { convertDialogueLogToGeminiHistory } from '../utils/geminiUtils';

// Type alias for the saveGame function
type SaveGameFn = (
  name: string,
  playerName: string,
  dialogueLog: DialogueLine[],
  currentSceneImageKeyword: string,
  currentPlayerStatus: PlayerStatus,
  geminiHistory: Content[]
) => Promise<string | null>;

// Type alias for the updateSave function
type UpdateSaveFn = (
  saveId: string,
  playerName: string,
  dialogueLog: DialogueLine[],
  currentSceneImageKeyword: string,
  currentPlayerStatus: PlayerStatus,
  geminiHistory: Content[]
) => void;

// Explicit return type for the useGameSaves hook
interface UseGameSavesReturn {
  gameSaves: GameSaveData[];
  setGameSaves: React.Dispatch<React.SetStateAction<GameSaveData[]>>;
  currentQuickSaveId: string | null;
  setCurrentQuickSaveId: React.Dispatch<React.SetStateAction<string | null>>;
  saveGame: SaveGameFn;
  updateSave: UpdateSaveFn;
  loadGame: (saveId: string) => GameSaveData | null;
  deleteSave: (saveId: string) => void;
  renameSave: (saveId: string, newName: string) => void;
}

export const useGameSaves = (
  currentGameSettings: GameSettingsData,
  unlockedAchievements: Record<string, number>, // For unlocking achievements
  unlockAchievement: (achievementId: string) => void,
  addNotification: (message: string, type: NotificationType, duration?: number) => void
): UseGameSavesReturn => {
  const [gameSaves, setGameSaves] = useState<GameSaveData[]>(() => {
    const saved = localStorage.getItem(LocalStorageKeys.GAME_SAVES);
    return saved ? JSON.parse(saved) : [];
  });

  const [currentQuickSaveId, setCurrentQuickSaveId] = useState<string | null>(null);

  useEffect(() => {
    localStorage.setItem(LocalStorageKeys.GAME_SAVES, JSON.stringify(gameSaves));
  }, [gameSaves]);

  const saveGame: SaveGameFn = useCallback(async (
    name,
    playerName,
    dialogueLog,
    currentSceneImageKeyword,
    currentPlayerStatus,
    geminiHistory
  ) => {
    if (!playerName.trim()) { addNotification(UIText.saveErrorNoSession, 'error'); return null; }
    if (!name.trim()){ addNotification(UIText.saveErrorInvalidName, 'error'); return null; }

    const { ...gameSettingsToSave } = currentGameSettings; // Use current game settings from App state

    const newSave: GameSaveData = {
      ...gameSettingsToSave,
      id: `save-${Date.now()}`,
      name: name.trim(),
      timestamp: Date.now(),
      playerName,
      dialogueLog,
      currentSceneImageKeyword,
      currentPlayerStatus: {...currentPlayerStatus, name: playerName},
      geminiChatHistory: geminiHistory
    };
    setGameSaves(prevSaves => [...prevSaves, newSave]);
    addNotification(UIText.saveSuccess(newSave.name), 'success');
    unlockAchievement('memory_keeper_initiate');
    if (gameSaves.length + 1 >= 5) unlockAchievement('memory_keeper_master');
    setCurrentQuickSaveId(newSave.id);
    return newSave.id;
  }, [currentGameSettings, addNotification, unlockAchievement, gameSaves, setCurrentQuickSaveId]);

  const updateSave: UpdateSaveFn = useCallback((
    saveId: string,
    playerName: string,
    dialogueLog: DialogueLine[],
    currentSceneImageKeyword: string,
    currentPlayerStatus: PlayerStatus,
    geminiHistory: Content[]
  ) => {
    const saveToUpdate = gameSaves.find(s => s.id === saveId);
    if (!saveToUpdate) { addNotification(UIText.loadErrorNotFound, "error"); return; }
    if (!playerName.trim()) { addNotification(UIText.saveErrorNoSession, 'error'); return; }

    const { ...gameSettingsToSave } = currentGameSettings;

    const updatedSave: GameSaveData = {
      ...saveToUpdate,
      ...gameSettingsToSave,
      timestamp: Date.now(),
      playerName,
      dialogueLog,
      currentSceneImageKeyword,
      currentPlayerStatus: {...currentPlayerStatus, name: playerName},
      geminiChatHistory: geminiHistory // Ensured geminiHistory is used
    };
    setGameSaves(prevSaves => prevSaves.map(s => s.id === saveId ? updatedSave : s));
    addNotification(UIText.updateSuccess(updatedSave.name), 'success');
    setCurrentQuickSaveId(saveId);
  }, [gameSaves, currentGameSettings, addNotification, setCurrentQuickSaveId]);

  const loadGame = useCallback((saveId: string): GameSaveData | null => {
    const saveToLoad = gameSaves.find(s => s.id === saveId);
    if (saveToLoad) {
      // Ensure the loaded player status conforms to the latest PlayerStatus type
      const loadedPlayerStatus: PlayerStatus = {
          ...InitialPlayerStatus, // Provide all defaults, including non-optional currentDay and timeOfDay
          ...(saveToLoad.currentPlayerStatus || {}), // Spread saved status, handling if it's undefined
          name: saveToLoad.playerName, // Ensure name is correct
          // Explicitly ensure currentDay and timeOfDay if they might be missing from old saves
          // and PlayerStatus type now requires them. Values from saveToLoad.currentPlayerStatus will override InitialPlayerStatus if present.
          currentDay: saveToLoad.currentPlayerStatus?.currentDay ?? InitialPlayerStatus.currentDay,
          timeOfDay: saveToLoad.currentPlayerStatus?.timeOfDay ?? InitialPlayerStatus.timeOfDay,
      };

      const fullyLoadedSaveData: GameSaveData = {
          ...DefaultGameSettings, // Start with application defaults for all settings
          ...saveToLoad, // Spread all saved data properties, overwriting defaults
          customNarrativeElements: ensureCustomElementsStructure(saveToLoad.customNarrativeElements || DefaultGameSettings.customNarrativeElements),
          currentPlayerStatus: loadedPlayerStatus, // Use the conformed player status
          // Ensure other potentially missing settings from GameSettingsData part are defaulted
          selectedModelId: saveToLoad.selectedModelId || DefaultGameSettings.selectedModelId,
          selectedImagePromptStyleId: saveToLoad.selectedImagePromptStyleId || DefaultGameSettings.selectedImagePromptStyleId,
          selectedSummaryModelId: saveToLoad.selectedSummaryModelId || DefaultGameSettings.selectedSummaryModelId,
          chatInterfaceOpacity: (saveToLoad as any).chatInterfaceOpacity ?? (saveToLoad as any).dialogueOpacity ?? DefaultGameSettings.chatInterfaceOpacity,
          dialogueBubbleOpacity: saveToLoad.dialogueBubbleOpacity ?? DefaultGameSettings.dialogueBubbleOpacity,
          dialogueBlur: saveToLoad.dialogueBlur ?? DefaultGameSettings.dialogueBlur,
          fontSizeScale: saveToLoad.fontSizeScale ?? DefaultGameSettings.fontSizeScale,
          enableBackdropBlur: saveToLoad.enableBackdropBlur ?? DefaultGameSettings.enableBackdropBlur,
          enableImageGeneration: saveToLoad.enableImageGeneration ?? DefaultGameSettings.enableImageGeneration,
          minOutputChars: saveToLoad.minOutputChars ?? DefaultGameSettings.minOutputChars,
          maxOutputChars: saveToLoad.maxOutputChars ?? DefaultGameSettings.maxOutputChars,
          imageGenerationInterval: saveToLoad.imageGenerationInterval ?? DefaultGameSettings.imageGenerationInterval,
          enableStreamMode: saveToLoad.enableStreamMode ?? DefaultGameSettings.enableStreamMode,
          enablePseudoStreamMode: saveToLoad.enablePseudoStreamMode ?? DefaultGameSettings.enablePseudoStreamMode,
          // Gist settings
          githubPat: saveToLoad.githubPat, // Will be undefined if not in save, DefaultGameSettings.githubPat used by spread
          gistId: saveToLoad.gistId,
          saveGithubPat: saveToLoad.saveGithubPat ?? DefaultGameSettings.saveGithubPat,
          enableGistAutoBackup: saveToLoad.enableGistAutoBackup ?? DefaultGameSettings.enableGistAutoBackup,
          gistAutoBackupIntervalHours: saveToLoad.gistAutoBackupIntervalHours ?? DefaultGameSettings.gistAutoBackupIntervalHours,
          gistUseSystemProxy: saveToLoad.gistUseSystemProxy ?? DefaultGameSettings.gistUseSystemProxy,
          // Ensure geminiChatHistory is present
          geminiChatHistory: saveToLoad.geminiChatHistory || convertDialogueLogToGeminiHistory(saveToLoad.dialogueLog),
      };
      // Remove potentially old dialogueOpacity if it was spread from saveToLoad
      delete (fullyLoadedSaveData as any).dialogueOpacity;


      addNotification(UIText.loadSuccess(fullyLoadedSaveData.name, fullyLoadedSaveData.playerName), 'success');
      setCurrentQuickSaveId(saveId);
      return fullyLoadedSaveData;
    } else {
      addNotification(UIText.loadErrorNotFound, 'error');
      return null;
    }
  }, [gameSaves, addNotification, setCurrentQuickSaveId]);

  const deleteSave = useCallback((saveId: string) => {
    const deletedSaveName = gameSaves.find(s => s.id === saveId)?.name || "Unknown Save";
    setGameSaves(prevSaves => prevSaves.filter(s => s.id !== saveId));
    addNotification(UIText.deleteSuccess(deletedSaveName), 'success');
    if(currentQuickSaveId === saveId) setCurrentQuickSaveId(null);
  }, [gameSaves, addNotification, currentQuickSaveId, setCurrentQuickSaveId]);

  const renameSave = useCallback((saveId: string, newName: string) => {
    if (!newName.trim()){ addNotification(UIText.saveErrorInvalidName, 'error'); return; }
    let oldName = "存档";
    setGameSaves(prevSaves => prevSaves.map(s => { if (s.id === saveId) { oldName = s.name; return { ...s, name: newName.trim() }; } return s; }));
    addNotification(UIText.renameSuccess(oldName, newName.trim()), 'success');
  }, [addNotification]);

  return {
    gameSaves,
    setGameSaves,
    currentQuickSaveId,
    setCurrentQuickSaveId,
    saveGame,
    updateSave,
    loadGame,
    deleteSave,
    renameSave,
  };
};