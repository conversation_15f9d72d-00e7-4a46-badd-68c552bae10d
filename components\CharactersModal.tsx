
import React, { useContext } from 'react';
import { CharacterProfile } from '../types';
import { Icons, UIText } from '../constants';
import { ThemeContext } from '../contexts/ThemeContext';

interface CharactersModalProps {
  isOpen: boolean;
  characters: CharacterProfile[];
  onClose: () => void;
  enableBackdropBlur?: boolean;
}

const RelationshipMeterRow: React.FC<{current: number, max: number, size?: 'sm' | 'xs'}> = ({current, max, size = 'sm'}) => {
  const iconSize = size === 'sm' ? 'w-3.5 h-3.5' : 'w-3 h-3';
  return (
    <div className="flex items-center">
      {Array.from({ length: max }).map((_, i) => (
        <Icons.Heart
          key={i}
          className={`${iconSize} ml-0.5 ${i < current ? 'text-accent-themed' : 'text-secondary-themed/30'}`}
        />
      ))}
    </div>
  );
};


const CharactersModal: React.FC<CharactersModalProps> = ({ isOpen, characters, onClose, enableBackdropBlur = true }) => {
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  if (!isOpen) return null;

  const mainBgClasses = "fixed inset-0 bg-black/60 flex items-center justify-center z-[70] p-4";
  const mainBgBlurClass = enableBackdropBlur ? "backdrop-blur-sm" : "";
  const contentAreaId = "characters-modal-content";

  return (
    <div
      className={`${mainBgClasses} ${mainBgBlurClass}`}
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="characters-modal-title"
      aria-describedby={contentAreaId}
    >
      <div
        className="bg-secondary-themed p-5 md:p-6 rounded-xl shadow-themed-xl w-full max-w-lg border border-themed flex flex-col max-h-[80vh]"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4 flex-shrink-0">
          <h3 id="characters-modal-title" className="text-lg font-semibold text-accent-themed flex items-center">
            <Icons.Profile className="w-5 h-5 mr-2" />
            {UIText.profile}
          </h3>
          <button
            onClick={onClose}
            className="p-1 text-primary-themed hover:text-accent-themed rounded-md hover:bg-element-themed/50 transition-colors"
            aria-label={UIText.closeModal}
          >
            <Icons.Close className="w-5 h-5" />
          </button>
        </div>
        
        <div id={contentAreaId} className="text-primary-themed text-sm overflow-y-auto flex-grow pr-1">
          {characters.length === 0 ? (
            <p className="italic text-secondary-themed text-center py-4">{UIText.noProfiles}</p>
          ) : (
            <ul className="space-y-3">
              {characters.sort((a,b) => b.firstEncountered - a.firstEncountered).map(char => (
                <li key={char.id} className="p-2.5 bg-element-themed/50 rounded-md border border-themed/30 shadow-sm">
                  <div className="flex justify-between items-start mb-1">
                    <p className="font-semibold text-primary-themed">{char.name}</p>
                    <RelationshipMeterRow current={char.relationshipLevel} max={10} size="xs" />
                  </div>
                  <p className="text-xs text-secondary-themed mt-0.5">首次遇见: {new Date(char.firstEncountered).toLocaleDateString()}</p>
                  <p className="text-xs text-secondary-themed mt-1">{char.description}</p>
                  {char.notableInteractions && char.notableInteractions.length > 0 && (
                    <div className="mt-1.5">
                      <p className="text-xs font-medium text-accent-themed/90">重要互动:</p>
                      <ul className="list-disc list-inside pl-2 text-xs text-secondary-themed space-y-0.5 max-h-20 overflow-y-auto">
                        {char.notableInteractions.slice(-3).reverse().map((interaction, i) => <li key={i} className="truncate" title={interaction}>{interaction}</li>)}
                      </ul>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          )}
        </div>
        <div className="mt-6 flex justify-end flex-shrink-0">
            <button
              onClick={onClose}
              className="btn-dreamy btn-dreamy-sm"
            >
              {UIText.closeModal}
            </button>
        </div>
      </div>
    </div>
  );
};

export default CharactersModal;
