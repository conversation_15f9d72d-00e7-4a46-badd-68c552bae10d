
import React, { useContext } from 'react';
import { ImportantEvent } from '../types';
import { Icons, UIText } from '../constants';
import { ThemeContext } from '../contexts/ThemeContext';

interface ImportantEventsModalProps {
  isOpen: boolean;
  events: ImportantEvent[];
  onClose: () => void;
  enableBackdropBlur?: boolean;
}

const ImportantEventsModal: React.FC<ImportantEventsModalProps> = ({ isOpen, events, onClose, enableBackdropBlur = true }) => {
  const themeContext = useContext(ThemeContext);
  if (!themeContext) throw new Error("ThemeContext not found");

  if (!isOpen) return null;

  const mainBgClasses = "fixed inset-0 bg-black/60 flex items-center justify-center z-[70] p-4";
  const mainBgBlurClass = enableBackdropBlur ? "backdrop-blur-sm" : "";
  const contentAreaId = "important-events-modal-content";

  const sortedEvents = [...events].sort((a, b) => b.timestamp - a.timestamp);

  return (
    <div
      className={`${mainBgClasses} ${mainBgBlurClass}`}
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="important-events-modal-title"
      aria-describedby={contentAreaId}
    >
      <div
        className="bg-secondary-themed p-5 md:p-6 rounded-xl shadow-themed-xl w-full max-w-lg border border-themed flex flex-col max-h-[80vh]"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4 flex-shrink-0">
          <h3 id="important-events-modal-title" className="text-lg font-semibold text-accent-themed flex items-center">
            <Icons.ClipboardList className="w-5 h-5 mr-2" />
            {UIText.importantEventsModalTitle}
          </h3>
          <button
            onClick={onClose}
            className="p-1 text-primary-themed hover:text-accent-themed rounded-md hover:bg-element-themed/50 transition-colors"
            aria-label={UIText.closeModal}
          >
            <Icons.Close className="w-5 h-5" />
          </button>
        </div>
        
        <div id={contentAreaId} className="text-primary-themed text-sm overflow-y-auto flex-grow pr-1">
          {sortedEvents.length === 0 ? (
            <p className="italic text-secondary-themed text-center py-4">{UIText.noImportantEvents}</p>
          ) : (
            <ul className="space-y-3">
              {sortedEvents.map(event => (
                <li 
                  key={event.id} 
                  className="p-2.5 rounded-md shadow-sm"
                  style={{
                    backgroundColor: 'var(--rpg-slot-bg)',
                    border: '1px solid var(--rpg-slot-border)',
                  }}
                >
                  <p className="text-[var(--rpg-text-primary)]">{event.text}</p>
                  <div className="flex justify-between items-center mt-1.5 text-xs text-[var(--rpg-text-secondary)] opacity-80">
                    <span>
                      {event.category && <span className="bg-element-themed/50 px-1.5 py-0.5 rounded-full mr-1.5 text-xs">{event.category}</span>}
                      {event.source && <span title={`来源: ${event.source}`}>来源: {event.source.length > 20 ? event.source.substring(0,18) + '...' : event.source}</span>}
                    </span>
                    <span title={new Date(event.timestamp).toLocaleString()}>
                      {new Date(event.timestamp).toLocaleDateString(navigator.language || 'zh-CN')}
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
         <div className="mt-6 flex justify-end flex-shrink-0">
            <button
              onClick={onClose}
              className="btn-dreamy btn-dreamy-sm"
            >
              {UIText.closeModal}
            </button>
        </div>
      </div>
    </div>
  );
};

export default ImportantEventsModal;