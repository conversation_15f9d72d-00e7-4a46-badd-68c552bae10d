import {
  BaseAIProvider,
  AIProviderConfig,
  AIProviderCapabilities,
  AIModelInfo,
  AIMessage,
  AIResponse,
  AIStreamChunk,
  AIGenerationConfig,
  AIProviderError,
  AIRateLimitError,
  AIQuotaExceededError,
  AIProviderType
} from './types';

export class OpenAIProvider extends BaseAIProvider {
  private baseUrl: string;

  constructor(config: AIProviderConfig) {
    super(config);
    this.baseUrl = config.baseUrl || 'https://api.openai.com/v1';
  }

  getCapabilities(): AIProviderCapabilities {
    return {
      supportsStreaming: true,
      supportsSystemMessages: true,
      supportsToolCalls: true,
      supportsImageInput: true,
      maxContextLength: 128000, // GPT-4 context length
      supportedFormats: ['text', 'json']
    };
  }

  async getAvailableModels(): Promise<AIModelInfo[]> {
    // Try to fetch models from API endpoint
    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          ...this.config.customHeaders
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.data && Array.isArray(data.data)) {
          return data.data.map((model: any) => ({
            id: model.id,
            name: model.id,
            provider: 'openai' as const,
            maxTokens: model.context_length || 4096,
            supportsStreaming: true
          }));
        }
      }
    } catch (error) {
      console.warn('Failed to fetch models from API, using defaults:', error);
    }

    // Fallback to default models
    return [
      {
        id: 'gpt-4o',
        name: 'GPT-4o (最新)',
        provider: 'openai',
        maxTokens: 128000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.005, output: 0.015 }
      },
      {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini (经济)',
        provider: 'openai',
        maxTokens: 128000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.00015, output: 0.0006 }
      },
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        provider: 'openai',
        maxTokens: 128000,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.01, output: 0.03 }
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: 'openai',
        maxTokens: 16385,
        supportsStreaming: true,
        costPer1kTokens: { input: 0.0005, output: 0.0015 }
      }
    ];
  }

  async validateConfig(): Promise<boolean> {
    if (!this.config.apiKey) {
      throw new AIProviderError('OpenAI API key is required', 'openai', 'MISSING_API_KEY');
    }

    try {
      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          ...this.config.customHeaders
        }
      });

      if (response.status === 401) {
        throw new AIProviderError('Invalid OpenAI API key', 'openai', 'INVALID_API_KEY', 401);
      }

      return response.ok;
    } catch (error) {
      if (error instanceof AIProviderError) throw error;
      throw new AIProviderError(`OpenAI validation failed: ${error.message}`, 'openai', 'VALIDATION_ERROR');
    }
  }

  // Test connection with detailed response
  async testConnection(): Promise<{ success: boolean; responseTime: number; error?: string; modelCount?: number }> {
    const startTime = Date.now();

    try {
      if (!this.config.apiKey) {
        return {
          success: false,
          responseTime: 0,
          error: 'API密钥未配置'
        };
      }

      const response = await fetch(`${this.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          ...this.config.customHeaders
        }
      });

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error?.message || errorMessage;
        } catch {}

        return {
          success: false,
          responseTime,
          error: errorMessage
        };
      }

      const data = await response.json();
      const modelCount = data.data?.length || 0;

      return {
        success: true,
        responseTime,
        modelCount
      };
    } catch (error) {
      return {
        success: false,
        responseTime: Date.now() - startTime,
        error: error.message || '连接失败'
      };
    }
  }

  async generateResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): Promise<AIResponse> {
    const validatedConfig = this.validateGenerationConfig(config);
    const formattedMessages = this.formatMessages(messages);

    const requestBody = {
      model: this.config.defaultModel || 'gpt-4o-mini',
      messages: formattedMessages.map(msg => ({
        role: msg.role,
        content: msg.content,
        ...(msg.name && { name: msg.name })
      })),
      temperature: validatedConfig.temperature,
      max_tokens: validatedConfig.maxTokens,
      top_p: validatedConfig.topP,
      frequency_penalty: validatedConfig.frequencyPenalty || 0,
      presence_penalty: validatedConfig.presencePenalty || 0,
      stop: validatedConfig.stopSequences,
      stream: false
    };

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          ...this.config.customHeaders
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        await this.handleErrorResponse(response);
      }

      const data = await response.json();
      const choice = data.choices?.[0];

      if (!choice) {
        throw new AIProviderError('No response from OpenAI', 'openai', 'NO_RESPONSE');
      }

      return {
        content: choice.message.content || '',
        finishReason: choice.finish_reason,
        usage: data.usage ? {
          promptTokens: data.usage.prompt_tokens,
          completionTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens
        } : undefined,
        model: data.model,
        provider: 'openai'
      };
    } catch (error) {
      if (error instanceof AIProviderError) throw error;
      throw new AIProviderError(`OpenAI request failed: ${error.message}`, 'openai', 'REQUEST_ERROR');
    }
  }

  async* generateStreamResponse(
    messages: AIMessage[],
    config?: AIGenerationConfig
  ): AsyncGenerator<AIStreamChunk, void, unknown> {
    const validatedConfig = this.validateGenerationConfig(config);
    const formattedMessages = this.formatMessages(messages);

    const requestBody = {
      model: this.config.defaultModel || 'gpt-4o-mini',
      messages: formattedMessages.map(msg => ({
        role: msg.role,
        content: msg.content,
        ...(msg.name && { name: msg.name })
      })),
      temperature: validatedConfig.temperature,
      max_tokens: validatedConfig.maxTokens,
      top_p: validatedConfig.topP,
      frequency_penalty: validatedConfig.frequencyPenalty || 0,
      presence_penalty: validatedConfig.presencePenalty || 0,
      stop: validatedConfig.stopSequences,
      stream: true
    };

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          ...this.config.customHeaders
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        await this.handleErrorResponse(response);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new AIProviderError('No response stream from OpenAI', 'openai', 'NO_STREAM');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                yield { content: '', isComplete: true };
                return;
              }

              try {
                const parsed = JSON.parse(data);
                const choice = parsed.choices?.[0];
                if (choice?.delta?.content) {
                  yield {
                    content: choice.delta.content,
                    isComplete: false,
                    usage: parsed.usage ? {
                      promptTokens: parsed.usage.prompt_tokens,
                      completionTokens: parsed.usage.completion_tokens,
                      totalTokens: parsed.usage.total_tokens
                    } : undefined
                  };
                }
              } catch (e) {
                // Skip invalid JSON lines
                continue;
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      if (error instanceof AIProviderError) throw error;
      throw new AIProviderError(`OpenAI stream failed: ${error.message}`, 'openai', 'STREAM_ERROR');
    }
  }

  private async handleErrorResponse(response: Response): Promise<never> {
    const errorData = await response.json().catch(() => ({}));
    const errorMessage = errorData.error?.message || `HTTP ${response.status}`;

    switch (response.status) {
      case 401:
        throw new AIProviderError('Invalid OpenAI API key', 'openai', 'INVALID_API_KEY', 401);
      case 429:
        const retryAfter = response.headers.get('retry-after');
        throw new AIRateLimitError('openai', retryAfter ? parseInt(retryAfter) : undefined);
      case 402:
        throw new AIQuotaExceededError('openai');
      case 400:
        throw new AIProviderError(`Bad request: ${errorMessage}`, 'openai', 'BAD_REQUEST', 400);
      case 500:
      case 502:
      case 503:
        throw new AIProviderError(`OpenAI server error: ${errorMessage}`, 'openai', 'SERVER_ERROR', response.status);
      default:
        throw new AIProviderError(`OpenAI error: ${errorMessage}`, 'openai', 'UNKNOWN_ERROR', response.status);
    }
  }
}
